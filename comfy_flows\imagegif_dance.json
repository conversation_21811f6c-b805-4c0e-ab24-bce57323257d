{"14": {"inputs": {"directory": "dance_demo", "image_load_cap": 0, "skip_first_images": 0, "select_every_nth": 4}, "class_type": "VHS_LoadImages", "_meta": {"title": "Load Images (Upload) 🎥🅥🅗🅢"}}, "18": {"inputs": {"strength": 0.8, "start_percent": 0, "end_percent": 1, "positive": ["25", 0], "negative": ["25", 1], "control_net": ["20", 0], "image": ["21", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet (Advanced)"}}, "20": {"inputs": {"control_net_name": "control_v11f1p_sd15_depth_fp16.safetensors"}, "class_type": "ControlNetLoaderAdvanced", "_meta": {"title": "Load Advanced ControlNet Model 🛂🅐🅒🅝"}}, "21": {"inputs": {"preprocessor": "DepthAnythingPreprocessor", "resolution": 512, "image": ["28", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "25": {"inputs": {"strength": 0.8, "start_percent": 0, "end_percent": 1, "positive": ["38", 0], "negative": ["40", 0], "control_net": ["26", 0], "image": ["27", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet (Advanced)"}}, "26": {"inputs": {"control_net_name": "control_v11p_sd15_openpose_fp16.safetensors"}, "class_type": "ControlNetLoaderAdvanced", "_meta": {"title": "Load Advanced ControlNet Model 🛂🅐🅒🅝"}}, "27": {"inputs": {"preprocessor": "OpenposePreprocessor", "resolution": 512, "image": ["28", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "28": {"inputs": {"image_gen_width": 512, "image_gen_height": 768, "resize_mode": "Crop and Resize", "hint_image": ["14", 0]}, "class_type": "HintImageEnchance", "_meta": {"title": "Enchance And Resize Hint Images"}}, "32": {"inputs": {"seed": 976813350844326, "steps": 8, "cfg": 1.5, "sampler_name": "lcm", "scheduler": "karras", "denoise": 1, "model": ["96", 0], "positive": ["103", 0], "negative": ["105", 1], "latent_image": ["41", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "33": {"inputs": {"image": "test (6).png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "34": {"inputs": {"image_gen_width": 512, "image_gen_height": 768, "resize_mode": "Crop and Resize", "hint_image": ["33", 0]}, "class_type": "HintImageEnchance", "_meta": {"title": "Enchance And Resize Hint Images"}}, "35": {"inputs": {"model": "wd-vit-tagger-v3", "threshold": 0.45, "character_threshold": 0.85, "replace_underscore": false, "trailing_comma": false, "exclude_tags": "", "image": ["34", 0]}, "class_type": "WD14Tagger|pysssss", "_meta": {"title": "WD14 Tagger 🐍"}}, "36": {"inputs": {"ckpt_name": "majicmixRealistic_v7.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "37": {"inputs": {"stop_at_clip_layer": -2, "clip": ["58", 1]}, "class_type": "CLIPSetLastLayer", "_meta": {"title": "CLIP Set Last Layer"}}, "38": {"inputs": {"text": ["45", 0], "clip": ["37", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "40": {"inputs": {"text": "(embeddings:ng_deepnegative_v1_75t.pt:1.0),S(nsfw:1.2),(worst quality, low quality:1.4),(depth of field, blurry:1.2),(greyscale, monochrome:1.1),3D face,cropped,lowres,text,(nsfw:1.3),(worst quality:2),(low quality:2),(normal quality:2),normal quality,((grayscale)),skin spots,acnes,skin blemishes,age spot,(ugly:1.331),(duplicate:1.331),(morbid:1.21),(mutilated:1.21),(tranny:1.331),mutated hands,(poorly drawn hands:1.5),blurry,(bad anatomy:1.21),(bad proportions:1.331),extra limbs,(disfigured:1.331),(missing arms:1.331),(extra legs:1.331),(fused fingers:1.61051),(too many fingers:1.61051),(unclear eyes:1.331),lowers,bad hands,missing fingers,extra digit,bad hands,missing fingers,(((extra arms and legs))),", "clip": ["37", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "41": {"inputs": {"width": 512, "height": 768, "batch_size": 16}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "42": {"inputs": {"samples": ["32", 0], "vae": ["43", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "43": {"inputs": {"vae_name": "vae-ft-mse-840000-ema-pruned.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "45": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "ultra realistic 8k cg,masterpiece,professional artwork,famous artwork,cinematic lighting,cinematic bloom,", "text_b": ["35", 0], "text_c": ","}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "47": {"inputs": {"beta_schedule": "lcm", "model": ["58", 0], "m_models": ["49", 0], "context_options": ["51", 0]}, "class_type": "ADE_UseEvolvedSampling", "_meta": {"title": "Use Evolved Sampling 🎭🅐🅓②"}}, "49": {"inputs": {"motion_model": ["57", 0]}, "class_type": "ADE_ApplyAnimateDiffModelSimple", "_meta": {"title": "Apply AnimateDiff Model 🎭🅐🅓②"}}, "51": {"inputs": {"context_length": 16, "context_stride": 2, "context_overlap": 4, "fuse_method": "pyramid", "use_on_equal_length": false, "start_percent": 0, "guarantee_steps": 1}, "class_type": "ADE_StandardUniformContextOptions", "_meta": {"title": "Context Options◆Standard Uniform 🎭🅐🅓"}}, "57": {"inputs": {"model_name": "AnimateLCM_sd15_t2v.ckpt"}, "class_type": "ADE_LoadAnimateDiffModel", "_meta": {"title": "Load AnimateDiff Model 🎭🅐🅓②"}}, "58": {"inputs": {"lora_name": "animatediff\\AnimateLCM_sd15_t2v_lora.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["36", 0], "clip": ["36", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "59": {"inputs": {"enabled": true, "swap_model": "inswapper_128.onnx", "facedetection": "retinaface_resnet50", "face_restore_model": "codeformer-v0.1.0.pth", "face_restore_visibility": 1, "codeformer_weight": 0.5, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "input_image": ["42", 0], "source_image": ["33", 0]}, "class_type": "ReActorFaceSwap", "_meta": {"title": "ReActor 🌌 Fast Face Swap"}}, "61": {"inputs": {"ckpt_name": "film_net_fp32.pt", "clear_cache_after_n_frames": 10, "multiplier": 4, "frames": ["59", 0]}, "class_type": "FILM VFI", "_meta": {"title": "FILM VFI"}}, "62": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "20240527/190225_976813350844326", "format": "image/gif", "pingpong": true, "save_output": true, "images": ["61", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "67": {"inputs": {"weight": 1, "weight_type": "linear", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["68", 0], "ipadapter": ["68", 1], "image": ["33", 0], "clip_vision": ["69", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "68": {"inputs": {"preset": "LIGHT - SD1.5 only (low strength)", "model": ["47", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "69": {"inputs": {"clip_name": "CLIP_ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "84": {"inputs": {"categories": "person", "confidence_threshold": 0.1, "iou_threshold": 0.1, "box_thickness": 2, "text_thickness": 2, "text_scale": 1, "with_confidence": true, "with_class_agnostic_nms": false, "with_segmentation": true, "mask_combined": true, "mask_extracted": true, "mask_extracted_index": 0, "yolo_world_model": ["85", 0], "esam_model": ["86", 0], "image": ["28", 0]}, "class_type": "Yoloworld_ESAM_Zho", "_meta": {"title": "🔎Yoloworld ESAM"}}, "85": {"inputs": {"yolo_world_model": "yolo_world/l"}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Model<PERSON>oader_Zho", "_meta": {"title": "🔎Yoloworld Model Loader"}}, "86": {"inputs": {"device": "CUDA"}, "class_type": "ESAM_ModelLoader_Zho", "_meta": {"title": "🔎ESAM Model Loader"}}, "96": {"inputs": {"b1": 1.4000000000000001, "b2": 1.4, "s1": 0.9, "s2": 0.2, "model": ["67", 0]}, "class_type": "FreeU_V2", "_meta": {"title": "FreeU_V2"}}, "98": {"inputs": {"strength": 1, "set_cond_area": "default", "conditioning": ["105", 0], "mask": ["84", 1]}, "class_type": "ConditioningSetMask", "_meta": {"title": "Conditioning (Set Mask)"}}, "100": {"inputs": {"mask": ["84", 1]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "101": {"inputs": {"strength": 0.5, "set_cond_area": "default", "conditioning": ["38", 0], "mask": ["100", 0]}, "class_type": "ConditioningSetMask", "_meta": {"title": "Conditioning (Set Mask)"}}, "103": {"inputs": {"conditioning_1": ["98", 0], "conditioning_2": ["101", 0]}, "class_type": "Conditioning<PERSON><PERSON><PERSON>", "_meta": {"title": "Conditioning (Combine)"}}, "105": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["18", 0], "negative": ["18", 1], "control_net": ["106", 0], "image": ["107", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet (Advanced)"}}, "106": {"inputs": {"control_net_name": "control_v11p_sd15_lineart_fp16.safetensors"}, "class_type": "ControlNetLoaderAdvanced", "_meta": {"title": "Load Advanced ControlNet Model 🛂🅐🅒🅝"}}, "107": {"inputs": {"preprocessor": "LineArtPreprocessor", "resolution": 512, "image": ["28", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}}