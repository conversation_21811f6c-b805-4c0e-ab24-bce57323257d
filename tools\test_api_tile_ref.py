#!/usr/bin/env python 
# -*- coding:utf-8 -*-
# 调用接口 
# sd 文生图
# lora  
# crontol net tile | tile difusion |ultimate SD upscale 
# target resolution 1080,1920 
#

import requests
import base64
import os
import re 
import json
import random
import argparse
import io
import uuid

from PIL import Image, PngImagePlugin
# from huggingface_hub import hf_hub_download
from ultralytics import YOLO
from supervision import Detections

#yolo_path = hf_hub_download(repo_id="arnabdhar/YOLOv8-Face-Detection", filename="model.pt", cache_dir="./models")
yolo_path = "./models/models--arnabdhar--YOLOv8-Face-Detection/snapshots/4094ffaba7a6e243801ddb7f5f9a1fae0cf4d78b/model.pt"
yolo_model = YOLO(yolo_path)


base_url="http://127.0.0.1:7860"
# base_url="http://10.4.189.212:7860"

common_negative="EasyNegative, lowres, bad anatomy, bad hands, text, error, missing fingers, cropped, worst quality, low quality, normal quality, blurry"

def url_txt2img():
    return f"{base_url}/sdapi/v1/txt2img"

def url_img2img():
    return f"{base_url}/sdapi/v1/img2img"

def url_interrogate():
    return f"{base_url}/sdapi/v1/interrogate"

def save_img(images:list[str], args: dict, outdir: str, name_prefix: str):
    
    for i, img in enumerate(images):

        if i >= 4:
            break
        
        prefix = "%06d" % (i)
        suffix = str(uuid.uuid4())
        outfile = os.path.join(outdir, f"{name_prefix}_{prefix}_{suffix}.png")
        while os.path.exists(outfile):
            i +=  1
            prefix = "%06d" % (i)
            outfile = os.path.join(outdir, f"{name_prefix}_{prefix}.png")

        with open(outfile, "wb") as f:
            f.write(base64.b64decode(img))

def simple_txt2img_request():
    return {
        "prompt": None,
        "negative_prompt": None,
        "styles": [],
        "seed": -1,
        "subseed": -1,
        "subseed_strength": 0,
        "seed_resize_from_h": -1,
        "seed_resize_from_w": -1,
        "sampler_name": "DPM++ 2M SDE Karras",
        "batch_size": 4,
        "n_iter": 1,
        "steps": 20,
        "cfg_scale": 7,
        "width": 512,
        "height": 768,
        "restore_faces": True,
        "tiling": False,
        "do_not_save_samples": True,
        "do_not_save_grid": True,
        "eta": 0,
        "denoising_strength": 0.75,
        "s_min_uncond": 0,
        "s_churn": 0,
        "s_tmax": 0,
        "s_tmin": 0,
        "s_noise": 0,
        "override_settings": {},
        "override_settings_restore_afterwards": True,
        "refiner_checkpoint": None,
        "refiner_switch_at": 0,
        "disable_extra_networks": True,
        "comments": {},
        "enable_hr": False,
        "firstphase_width": 0,
        "firstphase_height": 0,
        "hr_scale": 2,
        "hr_upscaler": None,
        "hr_second_pass_steps": 0,
        "hr_resize_x": 0,
        "hr_resize_y": 0,
        "hr_checkpoint_name": None,
        "hr_sampler_name": None,
        "hr_prompt": None,
        "hr_negative_prompt": None,
        "sampler_index": "DPM++ 2M SDE Karras",
        "script_name": None,
        "script_args": [],
        "send_images": True,
        "save_images": False,
        "alwayson_scripts": {}
        }

def simple_img2img_request():
    return {
        "prompt": None,
        "negative_prompt": None,
        "seed": -1,
        "subseed": -1,
        "subseed_strength": 0,
        "seed_resize_from_h": -1,
        "seed_resize_from_w": -1,
        "sampler_name": "DPM++ 2M SDE Karras",
        "batch_size": 4,
        "n_iter": 1,
        "steps": 20,
        "cfg_scale": 7,
        "width": 512,
        "height": 512,
        "restore_faces": True,
        "tiling": False,
        "do_not_save_samples": True,
        "do_not_save_grid": True,
        "eta": 0,
        "denoising_strength": 0.75,
        "s_min_uncond": 0,
        "s_churn": 0,
        "s_tmax": 0,
        "s_tmin": 0,
        "s_noise": 0,
        "override_settings": {},
        "override_settings_restore_afterwards": True,
        "refiner_checkpoint": None,
        "refiner_switch_at": 0,
        "disable_extra_networks": True,
        "comments": {},
        "init_images": [
            "string"
        ],
        "resize_mode": 0,
        "image_cfg_scale": 0,
        "mask": "string",
        "mask_blur_x": 4,
        "mask_blur_y": 4,
        "mask_blur": 0,
        "inpainting_fill": 0,
        "inpaint_full_res": True,
        "inpaint_full_res_padding": 0,
        "inpainting_mask_invert": 0,
        "initial_noise_multiplier": 0,
        "latent_mask": None,
        "sampler_index": "DPM++ 2M SDE Karras",
        "include_init_images": True,
        "script_name": None,
        "script_args": [],
        "send_images": True,
        "save_images": False,
        "alwayson_scripts": {}
        }


def sample_controlnet_unit():
    """
    "input_image" : image to use in this unit. defaults to null
    "mask" : mask pixel_perfect to filter the image. defaults to null
    "module" : preprocessor to use on the image passed to this unit before using it for conditioning. accepts values returned by the /controlnet/module_list route. defaults to "none"
    "model" : name of the model to use for conditioning in this unit. accepts values returned by the /controlnet/model_list route. defaults to "None"
    "weight" : weight of this unit. defaults to 1
    "resize_mode" : how to resize the input image so as to fit the output resolution of the generation. defaults to "Scale to Fit (Inner Fit)". Accepted values:
    0 or "Just Resize" : simply resize the image to the target width/height
    1 or "Scale to Fit (Inner Fit)" : scale and crop to fit smallest dimension. preserves proportions.
    2 or "Envelope (Outer Fit)" : scale to fit largest dimension. preserves proportions.
    "lowvram" : whether to compensate low GPU memory with processing time. defaults to false
    "processor_res" : resolution of the preprocessor. defaults to 64
    "threshold_a" : first parameter of the preprocessor. only takes effect when preprocessor accepts arguments. defaults to 64
    "threshold_b" : second parameter of the preprocessor, same as above for usage. defaults to 64
    "guidance_start" : ratio of generation where this unit starts to have an effect. defaults to 0.0
    "guidance_end" : ratio of generation where this unit stops to have an effect. defaults to 1.0
    "control_mode" : see the related issue for usage. defaults to 0. Accepted values:
    0 or "Balanced" : balanced, no preference between prompt and control model
    1 or "My prompt is more important" : the prompt has more impact than the model
    2 or "ControlNet is more important" : the controlnet model has more impact than the prompt
    "pixel_perfect" : enable pixel-perfect preprocessor. defaults to false
    """
    controlnet_unit = {
        "enabled": True,
        "input_image": None,
        "mask": None,
        "module": None,
        "model": None,
        "weight": 1.0,
        "resize_mode": 0,
        "lowvram": False,
        "processor_res": 64,
        "threshold_a": 64,
        "threshold_b": 64,
        "guidance_start": 0.0,
        "guidance_end": 1.0,
        "control_mode": 2,
        "pixel_perfect": True
    }
    return controlnet_unit

def sample_adetailer_unit():
    return  {
          "ad_model": "face_yolov8n.pt",
          "ad_prompt": "",
          "ad_negative_prompt": ""
        }

def controlnet_args(img:str):
    tile_args = sample_controlnet_unit()
    tile_args["module"] = "tile_resample"
    tile_args["model"] = "control_v11u_sd15_tile [1f041471]"
    tile_args["input_image"] = img
    tile_args["mask"] = None
    tile_args["weight"] = 1.5
    tile_args["resize_mode"] = 1
    tile_args["control_mode"] = 0

    ref_args = sample_controlnet_unit()
    ref_args["module"] = "reference_only"
    ref_args["model"] = ""
    ref_args["input_image"] = img
    ref_args["mask"] = None
    ref_args["weight"] = 1.0
    ref_args["resize_mode"] = 1
    ref_args["control_mode"] = 0

    params = {}
    params["args"] = [tile_args, ref_args]
     
    return params 

def adetailer_args(lora_model:str):

    person_args = sample_adetailer_unit()
    person_args["ad_model"] = "person_yolov8n-seg.pt"
    person_args["ad_prompt"] = ""

    face_args = sample_adetailer_unit()
    face_args["ad_model"] = "face_yolov8n.pt"
    face_args["ad_prompt"] = f"{lora_model},(best illustration),(best shadow),perfect face,finely detail,masterpiece,ultra-detailed,highres"

    hand_args = sample_adetailer_unit()
    hand_args["ad_model"] = "hand_yolov8n.pt"
    hand_args["ad_prompt"] = "perfect hands"

    params = {}
    params["args"] = [
        True,
        False,
        person_args,
        face_args,
        hand_args
    ]
    return params

def txt2img_with_contronnet(prompt:str, negative_prompt:str, outdir:str, img:str, name_prefix:str, seed:int = -1, lora_model:str = ""):

    params = simple_txt2img_request()
    params["prompt"] = prompt
    params["negative_prompt"] = negative_prompt
    params["seed"] = seed

    cntl_args = controlnet_args(img)
    params["alwayson_scripts"]["ControlNet"] = cntl_args
    
    adtl_args = adetailer_args(lora_model)
    params["alwayson_scripts"]["ADetailer"] = adtl_args

    params["steps"] = 20
    params["cfg_scale"] = 7

    resp = requests.post(url_txt2img(), json=params)
    resp_json = resp.json()
    save_img(resp_json["images"], resp_json["parameters"], outdir, name_prefix)

    # for cfg in [[20,7], [25,12]]:
    #     params["steps"] = cfg[0]
    #     params["cfg_scale"] = cfg[1]
    #     resp = requests.post(url_txt2img(), json=params)
    #     resp_json = resp.json()
    #     save_img(resp_json["images"], resp_json["parameters"], outdir, name_prefix)

def get_prompt(img:str):

    params = {"image" : img}
    resp = requests.post(url_interrogate(), json=params)
    resp_json = resp.json()
    prompt  = resp_json["caption"]
    return prompt

def gen_image(prompt:str, outdir:str, lora_model:str, img:str, name_prefix:str, seed:int = -1):

    prompt = f"{prompt}, (ultra high res),(highly detailed), {lora_model}"
    os.makedirs(outdir, exist_ok=True)
    txt2img_with_contronnet(prompt, common_negative, outdir, img, name_prefix, seed, lora_model)

def encode_image(image):

    with io.BytesIO() as output_bytes:
        metadata = None
        for key, value in image.info.items():
            if isinstance(key, str) and isinstance(value, str):
                if metadata is None:
                    metadata = PngImagePlugin.PngInfo()
                metadata.add_text(key, value)
        image.save(output_bytes, format="PNG", pnginfo=metadata)
        bytes_data = output_bytes.getvalue()
        
        ret = str(base64.b64encode(bytes_data), "utf-8")
        return ret 

def get_resolution(width, height):
    dest_width = 512
    dest_height = 768

    width = int(width)
    height = int(height)
    if width < height:
        dw = dest_width
        dh = int(height / (width / dw))
    else:
        dh = dest_height
        dw = int(width / (height / dh))

    return dw, dh

def resize(img:Image):
    (width, height) = img.size
    rwidth, rheight = get_resolution(width, height)
    img = img.resize((rwidth, rheight))
    (width, height) = img.size

    outputs = yolo_model(img)
    results = Detections.from_ultralytics(outputs[0])
    if len(results.xyxy) > 0:
        face_corner = results.xyxy[0].tolist()
        if width == 512:
            left = 0
            right = 512
            header = int(face_corner[1])
            if header > 384:
                upper = header - 384
                lower = upper + 768
            else:      
                upper = 0
                lower = 768 
        else:
            upper = 0
            lower = 768
            header = int(face_corner[0])
            if header > 256:
                left = header - 256 
                right = left + 512 
            else:
                left = 0
                right = 512
    else:
        if width > 512:
            left = (width - 512)//2
            right = left + 512 
        else:
            left = 0
            right = 512

        if height > 768:
            upper = (height - 768)//2
            lower = upper + 768
        else:    
            upper = 0
            lower = 768

    img = img.crop((left, upper, right, lower))
    return img 

def main(args):

    lora = args.lora 
    global base_url
    base_url = args.base_url

    for p, dirs, files in os.walk(args.indir):
        for name in files:
            if name.endswith(".png") or name.endswith(".jpg") or name.endswith("jpeg"):
                name_suffix = name.split(".")[-1]
                print(os.path.join(p, name))
                img = Image.open(os.path.join(p, name))
                img = resize(img)
                img = encode_image(img)
                name_prefix = name.replace(name_suffix, "")
                outdir = args.outdir
                p_last = os.path.basename(p)
                out_last = os.path.basename(outdir)
                
                if p_last != out_last:
                    outdir = os.path.join(outdir, p_last)

                prompt  = get_prompt(img)
                gen_image(prompt, outdir, lora, img, name_prefix, args.seed)
                #break
        #break

def setup_parser() -> argparse.ArgumentParser:

    parser = argparse.ArgumentParser()
    parser.add_argument("--indir", type=str, required=True, default=None, help="set controlnet within dir images")
    parser.add_argument("--outdir", type=str, required=True, default="outputs", help="dir to write results to")
    parser.add_argument("--lora", type=str, required=True, default=None, help="lora")
    parser.add_argument("--seed", type=int, required=False, default=-1, help="random seed for data generation")
    parser.add_argument("--base_url", type=str, required=False, default="http://127.0.0.1:7860", help="stable diffusion host url")

    return parser

if __name__ == "__main__":
    
    parser = setup_parser()
    args = parser.parse_args()
    main(args)


