#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import os
os.environ["HTTP_PROXY"]= "http://127.0.0.1:1080"
os.environ["HTTPS_PROXY"]= "http://127.0.0.1:1080"

import torch
from diffusers import StableDiffusionXLInstructPix2PixPipeline
from diffusers.utils import load_image

resolution = 768
image = load_image(
    "https://hf.co/datasets/diffusers/diffusers-images-docs/resolve/main/mountain.png"
).resize((resolution, resolution))
edit_instruction = "Turn sky into a cloudy one"

model_id = "diffusers/sdxl-instructpix2pix-768"
cache_dir = "./models/sdxl-instructpix2pix-768"

pipe = StableDiffusionXLInstructPix2PixPipeline.from_pretrained(
model_id, torch_dtype=torch.float16, cache_dir=cache_dir
).to("cuda")

edited_image = pipe(
    prompt=edit_instruction,
    image=image,
    height=resolution,
    width=resolution,
    guidance_scale=3.0,
    image_guidance_scale=1.5,
    num_inference_steps=30,
).images[0]

edited_image.save("edited_image.png")





