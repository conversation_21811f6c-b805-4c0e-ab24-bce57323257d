import cv2
import torch
import random
import numpy as np

import io 
import base64
from starlette.exceptions import HTTPException

import PIL
from PIL import Image
from typing import Tuple

import diffusers
from diffusers.models import ControlNetModel
from diffusers.pipelines.controlnet.multicontrolnet import MultiControlNetModel

from insightface.app import FaceAnalysis

from .instantid.style_template import styles
from .instantid.pipeline_stable_diffusion_xl_instantid_full import StableDiffusionXLInstantIDPipeline, draw_kps
from .instantid.depth_anything.dpt import DepthAnything
from .instantid.depth_anything.util.transform import Resize, NormalizeImage, PrepareForNet
from .exception import ImageFaceEmptyException
from .safety_checker_lib import check_safety, pil_to_numpy, numpy_to_pil, add_visible_tags

import torch.nn.functional as F
from torchvision.transforms import Compose

# global variable
MAX_SEED = np.iinfo(np.int32).max
device = "cuda" if torch.cuda.is_available() else "cpu"
dtype = torch.float16 if str(device).__contains__("cuda") else torch.float32
STYLE_NAMES = list(styles.keys())
DEFAULT_STYLE_NAME = "SpringFestival"

# Load face encoder
app = FaceAnalysis(
    name="antelopev2",
    root='./models/instantid', 
    providers=["CPUExecutionProvider"],
)

app.prepare(ctx_id=0, det_size=(640, 640))


# Path to InstantID models
face_adapter = f"./models/instantid/ip-adapter.bin"
controlnet_path = f"./models/instantid/ControlNetModel"

# Load pipeline face ControlNetModel
controlnet_identitynet = ControlNetModel.from_pretrained(
    controlnet_path, torch_dtype=dtype
)

# controlnet_canny_model = "./models/instantid/controlnet-canny-sdxl-1.0-small"
# depth_model = "./models/instantid/depth_anything_vitl14"
# controlnet_depth_model = "./models/instantid/controlnet-depth-sdxl-1.0-small"

pretrained_model_name_or_path = './models/instantid/SDXLRonghua_v40'

pipe = StableDiffusionXLInstantIDPipeline.from_pretrained(
    pretrained_model_name_or_path,
    controlnet=controlnet_identitynet,
    torch_dtype=dtype,
    variant="fp16",
    use_safetensors=True
).to(device)

pipe.enable_attention_slicing()
pipe.enable_model_cpu_offload()

pipe.scheduler = diffusers.DPMSolverMultistepScheduler.from_config(pipe.scheduler.config)

pipe.load_ip_adapter_instantid(face_adapter)
pipe.image_proj_model.to(device)
pipe.unet.to(device)

# depth_anything = DepthAnything.from_pretrained(depth_model).to(device).eval()


# pipe.enable_sequential_cpu_offload()

# transform = Compose([
#     Resize(
#         width=518,
#         height=518,
#         resize_target=False,
#         keep_aspect_ratio=True,
#         ensure_multiple_of=14,
#         resize_method='lower_bound',
#         image_interpolation_method=cv2.INTER_CUBIC,
#     ),
#     NormalizeImage(mean=[0.485, 0.456, 0.406], std=[0.229, 0.224, 0.225]),
#     PrepareForNet(),
# ])

# def get_depth_map(image):
#     image = np.array(image) / 255.0
#     h, w = image.shape[:2]
#     image = transform({'image': image})['image']
#     image = torch.from_numpy(image).unsqueeze(0).to("cuda")
#     with torch.no_grad():
#         depth = depth_anything(image)
#     depth = F.interpolate(depth[None], (h, w), mode='bilinear', align_corners=False)[0, 0]
#     depth = (depth - depth.min()) / (depth.max() - depth.min()) * 255.0
#     depth = depth.cpu().numpy().astype(np.uint8)
#     depth_image = Image.fromarray(depth)
#     return depth_image

# def get_canny_image(image, t1=100, t2=200):
#     image = cv2.cvtColor(np.array(image), cv2.COLOR_RGB2BGR)
#     edges = cv2.Canny(image, t1, t2)
#     return Image.fromarray(edges, "L")


def convert_from_cv2_to_image(img: np.ndarray) -> Image:
    return Image.fromarray(cv2.cvtColor(img, cv2.COLOR_BGR2RGB))

def convert_from_image_to_cv2(img: Image) -> np.ndarray:
    return cv2.cvtColor(np.array(img), cv2.COLOR_RGB2BGR)

def resize_img(
    input_image,
    max_side=1280,
    min_side=1024,
    size=None,
    pad_to_max_side=False,
    mode=PIL.Image.BILINEAR,
    base_pixel_number=64,
):
    w, h = input_image.size
    if size is not None:
        w_resize_new, h_resize_new = size
    else:
        ratio = min_side / min(h, w)
        w, h = round(ratio * w), round(ratio * h)
        ratio = max_side / max(h, w)
        input_image = input_image.resize([round(ratio * w), round(ratio * h)], mode)
        w_resize_new = (round(ratio * w) // base_pixel_number) * base_pixel_number
        h_resize_new = (round(ratio * h) // base_pixel_number) * base_pixel_number
    input_image = input_image.resize([w_resize_new, h_resize_new], mode)

    if pad_to_max_side:
        res = np.ones([max_side, max_side, 3], dtype=np.uint8) * 255
        offset_x = (max_side - w_resize_new) // 2
        offset_y = (max_side - h_resize_new) // 2
        res[
            offset_y : offset_y + h_resize_new, offset_x : offset_x + w_resize_new
        ] = np.array(input_image)
        input_image = Image.fromarray(res)
    return input_image

def apply_style(
    style_name: str, positive: str, negative: str = ""
) -> Tuple[str, str]:
    p, n = styles.get(style_name, styles[DEFAULT_STYLE_NAME])
    return p.replace("{prompt}", positive), n + " " + negative

def encode_pil_to_base64(image, ext:str):

    with io.BytesIO() as output_bytes:
        if ext.lower() == 'png':
            image.save(output_bytes, format="PNG")
        elif ext.lower() in ("jpg", "jpeg", "webp"):
            image.save(output_bytes, format="JPEG")
        else:
            raise HTTPException(status_code=500, detail="Invalid image format")

        bytes_data = output_bytes.getvalue()

    return base64.b64encode(bytes_data)

def style_image(
    face_image:bytes,
    pose_image:bytes,
    prompt:str,
    style_name = "SpringFestival",
    num_steps = 30,
    identitynet_strength_ratio = 0.5,
    adapter_strength_ratio = 0.5,
    canny_strength_ratio = 0.5,
    depth_strength_ratio = 0.5,
    guidance_scale = 5.0,
    enhance_face_region = True,
):
    
    if torch.cuda.is_available():
        torch.cuda.empty_cache()
        
    seed = random.randint(0, MAX_SEED)
    face_image = Image.open(io.BytesIO(face_image))
    pose_image = Image.open(io.BytesIO(pose_image))
    img_controlnet = pose_image
            
    face_img_b64 = encode_pil_to_base64(face_image, "png")
    pose_img_b64 = encode_pil_to_base64(pose_image, "png")
    
    if prompt is None or prompt == "":
        prompt = "a person"
    negative_prompt = "blur"
    # apply the style template
    prompt, negative_prompt = apply_style(style_name, prompt, negative_prompt)

    face_image = resize_img(face_image, max_side=1024)
    face_image_cv2 = convert_from_image_to_cv2(face_image)
    height, width, _ = face_image_cv2.shape

    # Extract face features
    face_info = app.get(face_image_cv2)

    if len(face_info) == 0:
        raise ImageFaceEmptyException()

    face_info = sorted(
        face_info,
        key=lambda x: (x["bbox"][2] - x["bbox"][0]) * x["bbox"][3] - x["bbox"][1],
    )[
        -1
    ]  # only use the maximum face
    face_emb = face_info["embedding"]
    face_kps = draw_kps(convert_from_cv2_to_image(face_image_cv2), face_info["kps"])
        
    pose_image = resize_img(pose_image, max_side=1024)
    pose_image_cv2 = convert_from_image_to_cv2(pose_image)

    face_info = app.get(pose_image_cv2)

    if len(face_info) == 0:
        raise ImageFaceEmptyException()

    face_info = face_info[-1]
    face_kps = draw_kps(pose_image, face_info["kps"])

    width, height = face_kps.size

    if enhance_face_region:
        control_mask = np.zeros([height, width, 3])
        x1, y1, x2, y2 = face_info["bbox"]
        x1, y1, x2, y2 = int(x1), int(y1), int(x2), int(y2)
        control_mask[y1:y2, x1:x2] = 255
        control_mask = Image.fromarray(control_mask.astype(np.uint8))
    else:
        control_mask = None
    
    # controlnet_canny = ControlNetModel.from_pretrained(
    #     controlnet_canny_model, torch_dtype=dtype
    # ).to(device)
    
    # controlnet_depth = ControlNetModel.from_pretrained(
    #     controlnet_depth_model, torch_dtype=dtype
    # ).to(device)
    
    # controlnet_map = {
    #     "canny": controlnet_canny,
    #     "depth": controlnet_depth,
    # }
    # controlnet_map_fn = {
    #     "canny": get_canny_image,
    #     "depth": get_depth_map,
    # }
    # controlnet_scales = {
    #     "canny": canny_strength_ratio,
    #     "depth": depth_strength_ratio,
    # }
    
    pipe.to("cuda")
    pipe.enable_xformers_memory_efficient_attention()

    # pipe.controlnet = MultiControlNetModel([controlnet_identitynet] + [controlnet_map[s] for s in controlnet_map.keys()])
    # control_scales = [float(identitynet_strength_ratio)] + [controlnet_scales[s] for s in controlnet_map.keys()]
    # control_images = [face_kps] + [controlnet_map_fn[s](img_controlnet).resize((width, height)) for s in controlnet_map.keys()]
    
    generator = torch.Generator(device=device).manual_seed(seed)

    pipe.set_ip_adapter_scale(adapter_strength_ratio)
    
    images = pipe(
        prompt=prompt,
        negative_prompt=negative_prompt,
        image_embeds=face_emb,
        image=face_kps,
        control_mask=control_mask,
        controlnet_conditioning_scale=float(identitynet_strength_ratio),
        num_inference_steps=num_steps,
        guidance_scale=guidance_scale,
        height=height,
        width=width,
        generator=generator,
    ).images

    x_checked_image, has_nsfw_concept, nsfw = check_safety(pil_to_numpy(images[0]))

    tagged_image = add_visible_tags(numpy_to_pil(x_checked_image)[0])
    
    return encode_pil_to_base64(tagged_image, "png").decode("utf-8"), nsfw, face_img_b64, pose_img_b64
