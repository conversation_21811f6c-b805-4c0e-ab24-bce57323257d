{"1": {"inputs": {"text": "现代，色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，背景人很多，倒着走", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["27", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "2": {"inputs": {"value": "a*16+1", "a": ["30", 0]}, "class_type": "SimpleMath+", "_meta": {"title": "🔧 Simple Math"}}, "4": {"inputs": {"text": ["121", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["27", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "5": {"inputs": {"samples": ["115", 0], "vae": ["28", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "8": {"inputs": {"unet_name": "wan2.2/Wan2.2-I2V-A14B-HighNoise-Q3_K_S.gguf"}, "class_type": "UnetLoaderGGUF", "_meta": {"title": "高噪声-<PERSON><PERSON> Loader (GGUF)"}}, "9": {"inputs": {"unet_name": "wan2.2/Wan2.2-I2V-A14B-LowNoise-Q3_K_S.gguf"}, "class_type": "UnetLoaderGGUF", "_meta": {"title": "低噪声-Unet Loader (GGUF)"}}, "10": {"inputs": {"blocks_to_swap": 40, "offload_img_emb": false, "offload_txt_emb": false, "use_non_blocking": false, "model": ["9", 0]}, "class_type": "wanBlockSwap", "_meta": {"title": "WanVideoBlockSwap"}}, "19": {"inputs": {"lora_name": "wan2.2/Wan21_T2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", "strength_model": 1.25, "model": ["10", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "20": {"inputs": {"width": 480, "height": ["132", 0], "length": ["2", 0], "batch_size": 1, "positive": ["4", 0], "negative": ["1", 0], "vae": ["28", 0], "start_image": ["130", 0]}, "class_type": "WanImageToVideo", "_meta": {"title": "WanImageToVideo"}}, "21": {"inputs": {"blocks_to_swap": 40, "offload_img_emb": false, "offload_txt_emb": false, "use_non_blocking": false, "model": ["8", 0]}, "class_type": "wanBlockSwap", "_meta": {"title": "WanVideoBlockSwap"}}, "22": {"inputs": {"lora_name": "wan2.2/Wan21_I2V_14B_lightx2v_cfg_step_distill_lora_rank64.safetensors", "strength_model": 3.5, "model": ["21", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "23": {"inputs": {"sage_attention": "auto", "model": ["22", 0]}, "class_type": "PathchSageAttentionKJ", "_meta": {"title": "高噪声-Patch Sage Attention KJ"}}, "24": {"inputs": {"enable_fp16_accumulation": true, "model": ["23", 0]}, "class_type": "ModelPatchTorchSettings", "_meta": {"title": "<PERSON> <PERSON>"}}, "25": {"inputs": {"sage_attention": "auto", "model": ["19", 0]}, "class_type": "PathchSageAttentionKJ", "_meta": {"title": "低噪声-Patch Sage Attention KJ"}}, "26": {"inputs": {"enable_fp16_accumulation": true, "model": ["25", 0]}, "class_type": "ModelPatchTorchSettings", "_meta": {"title": "<PERSON> <PERSON>"}}, "27": {"inputs": {"clip_name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "type": "wan", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "28": {"inputs": {"vae_name": "Wan2_1_VAE_bf16.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "30": {"inputs": {"Value": 5}, "class_type": "DF_Integer", "_meta": {"title": "时长(Integer)"}}, "114": {"inputs": {"add_noise": "enable", "noise_seed": 999, "steps": 4, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "start_at_step": 0, "end_at_step": 2, "return_with_leftover_noise": "enable", "model": ["24", 0], "positive": ["20", 0], "negative": ["20", 1], "latent_image": ["20", 2]}, "class_type": "KSamplerAdvanced", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Advanced)"}}, "115": {"inputs": {"add_noise": "disable", "noise_seed": 999, "steps": 4, "cfg": 1, "sampler_name": "euler", "scheduler": "normal", "start_at_step": 2, "end_at_step": 4, "return_with_leftover_noise": "disable", "model": ["26", 0], "positive": ["20", 0], "negative": ["20", 1], "latent_image": ["114", 0]}, "class_type": "KSamplerAdvanced", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Advanced)"}}, "121": {"inputs": {"api_baseurl": "https://dashscope.aliyuncs.com/compatible-mode/v1", "api_key": "sk-9502dc0b92784939b2f1986d897a41ea", "model": "qwen-vl-max", "role": ["134", 0], "prompt": ["127", 0], "temperature": 0.1, "seed": 1262, "speak_and_recognation": {"__value__": [false, true]}, "ref_image": ["130", 0]}, "class_type": "RH_LLMAPI_NODE", "_meta": {"title": "Runninghub LLM API Node"}}, "122": {"inputs": {"text": "### 视频场景描绘\n\n#### 场景类型\n这是一段乡村田园风光的视频，画面中展现了一位少女在金黄色的麦田间骑行自行车的场景。整体氛围宁静而美好，充满了自然的诗意。\n\n#### 运动场景\n少女骑着一辆白色的自行车，缓缓穿行在麦田小径上。她的动作轻盈，自行车的轮子在麦田间轻轻转动，带起一阵微风，麦穗随风摇曳，仿佛在为她伴舞。\n\n#### 人物情绪\n少女的表情显得轻松愉快，眼神中透露出一丝好奇与期待。她的嘴角微微上扬，似乎在享受这美好的时光。她的长发在风中飘扬，增添了几分动感与活力。\n\n#### 运镜方式\n镜头采用跟随拍摄的方式，平稳地跟随着少女的骑行轨迹。偶尔会有轻微的晃动，模拟出一种手持摄像机的真实感。镜头时而拉远，展示广阔的麦田和远处的树林；时而拉近，聚焦于少女的面部表情和细节动作。\n\n#### 光源类型\n光源为自然光，来自画面上方偏左的位置，模拟了清晨或傍晚的柔和阳光。这种光线不仅照亮了少女的脸庞，还为整个场景披上了一层温暖的金色光辉。\n\n#### 光线类型\n光线柔和且均匀，没有强烈的对比和阴影。这种散射光使得画面色调更加温馨，营造出一种宁静祥和的氛围。\n\n#### 镜头类型\n使用的是标准镜头（焦距约为50mm），能够真实地还原场景的比例和深度。标准镜头的视角接近人眼的正常视角，使得观众能够身临其境地感受到画面中的每一个细节。\n\n#### 焦距\n焦距适中，既能够清晰地捕捉到少女的特写，又能够兼顾背景的广阔视野。焦点主要集中在少女身上，背景则略微虚化，突出了主体。\n\n#### 色调\n整体色调偏向暖色系，以金黄色和柔和的绿色为主。这种色调不仅符合自然环境的特点，还能够传达出一种温暖、舒适的感觉。\n\n#### 视觉风格\n视觉风格清新自然，带有浓厚的田园风情。画面构图简洁明了，色彩搭配和谐统一，给人一种宁静、愉悦的视觉享受。\n\n#### 特效镜头\n在某些镜头中，可以加入一些轻柔的特效，如微风吹动麦穗的动态模糊效果，或是阳光透过树叶形成的斑驳光影。这些特效能够增强画面的层次感和立体感，使整个视频更加生动有趣。\n\n### 总结\n这段视频通过细腻的画面和流畅的运镜，生动地展现了一位少女在麦田间骑行的美好瞬间。无论是人物的表情动作，还是自然环境的细节描绘，都充满了生活的气息和艺术的美感。观众在欣赏这段视频时，仿佛能够感受到那份宁静与美好，仿佛自己也置身于那片金黄的麦田之中。", "anything": ["121", 0]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "127": {"inputs": {"text": "", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "UserPrompt"}}, "128": {"inputs": {"image": "微信图片_20250801163332.png"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "129": {"inputs": {"width": 1024, "height": 1024, "aspect_ratio": "3:4 portrait 896x1152", "swap_dimensions": "Off", "upscale_factor": 1, "batch_size": 1}, "class_type": "CR SDXL Aspect Ratio", "_meta": {"title": "AspectRatio"}}, "130": {"inputs": {"width": ["129", 0], "height": ["129", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 56, "crop": 0, "image": ["128", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "132": {"inputs": {"expression": "(480*a)/b", "speak_and_recognation": {"__value__": [false, true]}, "a": ["130", 2], "b": ["130", 1]}, "class_type": "MathExpression|pysssss", "_meta": {"title": "Math Expression 🐍"}}, "133": {"inputs": {"text": "600", "anything": ["132", 0]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "134": {"inputs": {"text": "你能够根据用户的输入，生成一段生动形象的视。在视频场景的描绘中，需要包括一些重要的信息，比如场景类型，运动场景，人物情绪，运镜方式，光源类型，光线类型，镜头类型，焦距，色调，视觉风格，特效镜头。", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "Text Multiline"}}, "135": {"inputs": {"filename_prefix": "wan/animie", "fps": 16.000000000000004, "lossless": true, "quality": 90, "method": "default", "images": ["5", 0]}, "class_type": "SaveAnimatedWEBP", "_meta": {"title": "SaveAnimatedWEBP"}}}