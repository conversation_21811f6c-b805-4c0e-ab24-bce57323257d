import shutil
import os
import uuid
import argparse

def parse_args():
    desc = "merge outputfile"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--indir', type=str, default='input file or path', help='[file or path]')
    parser.add_argument('--outdir', type=str, default='dataset/out', help='output data dir')
    return parser.parse_args()

def process_file(src_file:str, dst_dir:str):

    infile = os.path.basename(src_file)
    inpath = os.path.dirname(src_file)
    
    tailpath = os.path.split(inpath)[-1]
    output = os.path.join(dst_dir, tailpath)
    tailext = infile.split(".")[-1]
    tailext = tailext.lower()
    
    os.makedirs(output, exist_ok=True)
    
    if tailext in ["png", "jpg", "jpeg"]:   
        outfile = os.path.join(output, infile)
        if os.path.exists(outfile):
            suffix = str(uuid.uuid4())
            outpart = outfile.split(".")
            outfile = f"{outpart[0]}_{suffix}.{outpart[-1]}"
        # print(src_file, outfile)    
        shutil.copy(src_file, outfile)
        
def process_directory(indir:str, outdir:str):
       
    for dirpath, dirname, filenames in os.walk(indir):
        for filename in filenames:
            infile = os.path.join(dirpath, filename)
            process_file(infile, outdir)

def main():
    args = parse_args()
    os.makedirs(args.outdir, exist_ok=True)
    if os.path.isfile(args.indir):
        process_file(args.indir, args.outdir)
    else:
        process_directory(args.indir, args.outdir)
        
if __name__ == "__main__":
    main()



