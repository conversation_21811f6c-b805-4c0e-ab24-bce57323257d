#!/bin/bash

#docker pull nvcr.io/nvidia/pytorch:24.05-py3
#cd next-lamaindex
#docker build . -t next-lamaindex:latest
#docker run -it -v /root/next-lamaindex:/app/next-lamaindex -p 7862:3000 --name next-lamaindex next-lamaindex 
#docker update --restart=always 63956f647d42

#docker pull ollama/ollama:0.4.0-ci1
#docker run -d --gpus=all -v i/root/ollama:/root/.ollama -p 11434:11434 --name ollama ollama/ollama:0.4.0-ci1
#docker exec -it ollama /bin/bash
#    
#    ollama run --keepalive 48h llama3.2:1b
#     

#docker run --gpus all -it --ipc=host --ulimit memlock=-1 --ulimit stack=67108864 -v /root/ComfyUI:/workspace/ComfyUI -p 7861:8188 --name banyunjuhe-comfyui nvcr.io/nvidia/pytorch:24.05-py3
#docker run --gpus all -it --ipc=host --ulimit memlock=-1 --ulimit stack=67108864 -v /root/ComfyUI_test:/workspace/ComfyUI -p 7863:8188 --name banyunjuhe-comfyui-test gothz/banyunjuhe-comfyui:20241121
#docker run --gpus all -it --ipc=host --ulimit memlock=-1 --ulimit stack=67108864 -v /root/ai-data:/workspace/ai-data -p 8765:8765 --name banyunjuhe-ai-data gothz/banyunjuhe-comfyui


#docker run -d -p 9898:9898 -v $(pwd):/app --name bot-on-wxwork gothz/bot-on-wxwork:241111
#docker run -d -p 9899:9898 -v $(pwd):/app --name bot-on-wechat gothz/bot-on-wxwork:241111

docker run --gpus all -it --ipc=host --ulimit memlock=-1 --ulimit stack=67108864 -v /root/ComfyUI:/workspace/ComfyUI -p 7866:8188 --name banyunjuhe-comfyui-new gothz/banyunjuhe-comfyui:20241121
docker run --gpus all -it --ipc=host --ulimit memlock=-1 --ulimit stack=67108864 -v /root/ComfyUI_test:/workspace/ComfyUI -p 7863:8188 --name banyunjuhe-comfyui-test gothz/banyunjuhe-comfyui:20241121

