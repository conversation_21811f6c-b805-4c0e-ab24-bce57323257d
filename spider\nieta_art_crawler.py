import asyncio
from playwright.async_api import async_playwright
import logging
import json
import os
import aiohttp
import aiofiles
from pathlib import Path
import re
from urllib.parse import urlparse

log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"

logging.basicConfig(level=logging.INFO, format=log_format)
logger = logging.getLogger(__name__)

# 目标 URL
TARGET_URL = "https://app.nieta.art/character/discover"

# 数据集目录
DATASET_DIR = "dataset/nieta"

def sanitize_filename(filename):
    """清理文件名，移除不合法的字符"""
    # 移除或替换不合法的文件名字符
    illegal_chars = r'[<>:"/\\|?*]'
    filename = re.sub(illegal_chars, '_', filename)
    # 移除前后空格
    filename = filename.strip()
    # 如果文件名为空或只有点，使用默认名称
    if not filename or filename == '.' or filename == '..':
        filename = 'unknown_character'
    # 限制文件名长度
    if len(filename) > 100:
        filename = filename[:100]
    return filename

async def download_image(session, image_url, save_path):
    """下载图片到指定路径"""
    try:
        async with session.get(image_url) as response:
            if response.status == 200:
                # 确保目录存在
                os.makedirs(os.path.dirname(save_path), exist_ok=True)

                async with aiofiles.open(save_path, 'wb') as f:
                    async for chunk in response.content.iter_chunked(8192):
                        await f.write(chunk)

                logger.info(f"成功下载图片: {save_path}")
                return True
            else:
                logger.error(f"下载图片失败，状态码: {response.status}, URL: {image_url}")
                return False
    except Exception as e:
        logger.error(f"下载图片时出错: {e}, URL: {image_url}")
        return False

def get_image_extension(image_url):
    """从URL获取图片扩展名"""
    parsed_url = urlparse(image_url)
    path = parsed_url.path
    if '.' in path:
        extension = path.split('.')[-1].lower()
        # 常见图片格式
        if extension in ['jpg', 'jpeg', 'png', 'gif', 'webp', 'bmp']:
            return f".{extension}"
    return ".jpg"  # 默认扩展名

async def get_character_creation_images(page, session, character_dir, max_images=100):
    """获取角色创作页面的图片 - 只下载特定HTML结构中的图片"""
    # 不再进行页面跳转，直接在当前页面工作
    # 等待页面加载
    container = page.locator(".min-h-70vh").nth(0)
    await container.scroll_into_view_if_needed()
    await page.wait_for_load_state('networkidle')
    await page.wait_for_timeout(3000)

    try:
        downloaded_count = 0
        scroll_attempts = 0
        max_scroll_attempts = 10
        downloaded_urls = set()  # 记录已下载的URL，避免重复

        while downloaded_count < max_images and scroll_attempts < max_scroll_attempts:
            # 获取特定结构的创作图片链接
            # 查找包含 data-seo-link 属性且指向 /collection/profile 的 <a> 标签
            creation_links = await page.query_selector_all('a[data-seo-link*="/collection/profile"]')

            logger.info(f"第 {scroll_attempts + 1} 次扫描，找到 {len(creation_links)} 个创作链接")

            # 如果没有找到创作链接，尝试备用选择器
            if len(creation_links) == 0:
                backup_links = await page.query_selector_all('a[href*="/collection/profile"]')
                if len(backup_links) > 0:
                    creation_links = backup_links
                    logger.info(f"使用备用选择器找到 {len(creation_links)} 个创作链接")

            for link_element in creation_links:
                if downloaded_count >= max_images:
                    break

                try:
                    # 在每个链接内查找图片
                    img_element = await link_element.query_selector('img')
                    if not img_element:
                        continue

                    img_url = await img_element.get_attribute('src')
                    
                    if not img_url or 'data:' in img_url:
                        continue
                    img_url = img_url.split("?")[0]

                    # 跳过已下载的图片
                    if img_url in downloaded_urls:
                        continue

                    # 获取创作标题（如果有的话）
                    # 尝试多个可能的选择器来获取标题
                    title_selectors = [
                        '.w-full.text-14px.text-black.leading-20px.font-500.break-words',
                        '.text-14px.leading-20px.font-500.break-words'
                    ]

                    creation_title = ""
                    for selector in title_selectors:
                        title_element = await link_element.query_selector(selector)
                        if title_element:
                            title_text = await title_element.inner_text()
                            if title_text and title_text.strip():
                                creation_title = sanitize_filename(title_text.strip())
                                break

                    # 生成文件名
                    img_extension = get_image_extension(img_url)
                    if creation_title:
                        img_filename = f"creation_{downloaded_count + 1:03d}_{creation_title[:30]}{img_extension}"
                    else:
                        img_filename = f"creation_{downloaded_count + 1:03d}{img_extension}"

                    img_path = os.path.join(character_dir, img_filename)

                    # 检查文件是否已存在
                    if os.path.exists(img_path):
                        downloaded_urls.add(img_url)
                        continue

                    # 下载图片
                    success = await download_image(session, img_url, img_path)
                    if success:
                        downloaded_count += 1
                        downloaded_urls.add(img_url)
                        logger.info(f"已下载创作图片 {downloaded_count}/{max_images}: {img_filename}")

                except Exception as e:
                    logger.error(f"处理创作图片时出错: {e}")
                    continue

            # 滚动页面加载更多图片
            if downloaded_count < max_images:
                a_tag = page.locator('a[data-seo-link^="/collection/profile"]').nth(-1)
                await a_tag.scroll_into_view_if_needed()
                await page.wait_for_timeout(2000)
                scroll_attempts += 1

                # 如果连续几次滚动都没有新内容，提前退出
                if scroll_attempts > 3 and len(creation_links) == 0:
                    logger.info("连续滚动未发现新内容，提前结束")
                    break

        logger.info(f"角色创作页面共下载了 {downloaded_count} 张图片")
        return downloaded_count

    except Exception as e:
        logger.error(f"获取角色创作图片时出错: {e}")
        return 0

async def extract_characters_with_scrolling(page, session, category="默认"):
    """从发现页面提取角色信息并下载图片，带滚动功能"""
    characters = []
    
    # 等待角色卡片加载
    try:
        await page.wait_for_selector('div[data-testid="virtuoso-scroller"] .flex > .relative', timeout=10000)
    except:
        logger.warning("页面加载超时，继续提取数据")
    
    # 滚动页面以加载更多角色
    scroll_attempts = 0
    max_scroll_attempts = 5
    previous_count = 0
    
    while scroll_attempts < max_scroll_attempts:
        # 获取当前页面的角色卡片数量
        character_cards = await page.query_selector_all('div[data-testid="virtuoso-scroller"] .flex > .relative')
        current_count = len(character_cards)
        
        logger.info(f"第 {scroll_attempts + 1} 次滚动，当前角色卡片数量: {current_count}")
        
        # 如果没有新角色加载，增加滚动尝试次数
        if current_count == previous_count:
            scroll_attempts += 1
        else:
            scroll_attempts = 0  # 重置滚动尝试次数
            previous_count = current_count
            
        # 如果已经尝试了很多次还没有新内容，就退出
        if scroll_attempts >= 3:
            logger.info("连续几次滚动未发现新内容，提前结束")
            break
            
        # 定位到最后一个元素并滚动到视图中
        if character_cards:
            last_card = character_cards[-1]
            await last_card.scroll_into_view_if_needed()
            await page.wait_for_timeout(2000)  # 等待内容加载
            
        scroll_attempts += 1
    
    # 重新获取所有角色卡片
    character_cards = await page.query_selector_all('div[data-testid="virtuoso-scroller"] .flex > .relative')
    logger.info(f"总共找到 {len(character_cards)} 个角色卡片")
    
    # 使用正则表达式从页面HTML内容中提取角色信息
    html_content = await page.content()
    
    # 使用正则表达式提取角色信息
    import re
    
    # 匹配角色卡片的模式
    card_pattern = r'<div class="relative bg-#FFF/5[^"]*"[^>]*style="[^"]*">.*?<img class="object-cover[^"]*"[^>]*src="([^"]*?)".*?<div class="relative px-8px mb-6px line-clamp-2">([^<]*?)</div>.*?<div class="text-#FFF text-9px leading-11px font-600">([^<]*?)</div>'
    
    # 查找所有匹配的角色卡片
    matches = re.findall(card_pattern, html_content, re.DOTALL)
    
    logger.info(f"通过正则表达式在 {category} 分类中找到 {len(matches)} 个角色卡片")
    
    for img_url, name, heat_score in matches:
        try:
            # 清理角色名称和分类名称
            clean_name = sanitize_filename(name.strip())
            clean_category = sanitize_filename(category)

            # 创建角色目录 (category/character)
            character_dir = os.path.join(DATASET_DIR, clean_category, clean_name)
            os.makedirs(character_dir, exist_ok=True)
            img_url = img_url.split("?")[0]
            # 获取图片扩展名
            img_extension = get_image_extension(img_url)

            # 构建图片保存路径
            image_filename = f"avatar{img_extension}"
            image_path = os.path.join(character_dir, image_filename)

            # 下载头像图片
            download_success = await download_image(session, img_url, image_path)

            character_info = {
                "category": category,
                "clean_category": clean_category,
                "name": name.strip(),
                "clean_name": clean_name,
                "image_url": img_url,
                "heat_score": int(heat_score) if heat_score.isdigit() else 0,
                "character_dir": character_dir,
                "avatar_path": image_path if download_success else None,
                "download_success": download_success,
                "creation_images_count": 0,
            }
            characters.append(character_info)
        except Exception as e:
            logger.error(f"处理角色信息时出错: {e}")
            continue
        
    return characters

async def process_character_details(page, session, characters, category):
    """处理角色详情，点击角色卡片获取创作图片"""
    processed_characters = []

    for i, character in enumerate(characters):
        try:
            logger.info(f"正在处理角色 {i+1}/{len(characters)}: {character['name']}")

            # 重新获取角色卡片元素（因为页面可能已经刷新）
            character_cards = await page.query_selector_all('div[data-testid="virtuoso-scroller"] .flex > .relative')

            if i < len(character_cards):
                card = character_cards[i]

                # 点击角色卡片
                try:
                    await card.click()
                    await page.wait_for_load_state('networkidle')
                    await page.wait_for_timeout(2000)

                    # 下载创作页面的图片
                    creation_count = await get_character_creation_images(
                        page, session, character['character_dir']
                    )
                    character['creation_images_count'] = creation_count

                except Exception as e:
                    logger.error(f"点击角色卡片时出错: {e}")
                finally:
                    # 无论成功还是失败，都尝试返回到发现页面
                    try:
                        await page.go_back()
                        await page.wait_for_load_state('networkidle')
                        await page.wait_for_timeout(1000)
                    except Exception as e:
                        logger.error(f"返回发现页面时出错: {e}")
                        # 如果 go_back 失败，尝试直接导航到发现页面
                        try:
                            await page.goto("https://app.nieta.art/character/discover")
                            await page.wait_for_load_state('networkidle')
                            await page.wait_for_timeout(1000)
                        except:
                            pass

            processed_characters.append(character)

        except Exception as e:
            logger.error(f"处理角色详情时出错: {e}")
            processed_characters.append(character)
            continue

    return processed_characters

async def get_categories(page):
    """获取所有分类"""
    categories = []
    
    try:
        # 获取所有分类元素
        category_elements = await page.query_selector_all('.flex.flex-col .shrink-0.relative')
        
        logger.info(f"找到 {len(category_elements)} 个分类")
        
        for element in category_elements:
            try:
                # 提取分类名称
                name_element = await element.query_selector('.relative.text-12px.leading-18px.max-w-64.truncate')
                if name_element:
                    name = await name_element.inner_text()
                    if name and name.strip() != "":
                        categories.append(name.strip())
            except Exception as e:
                logger.error(f"提取分类名称时出错: {e}")
                continue
                
    except Exception as e:
        logger.error(f"获取分类时出错: {e}")
    
    # 如果没有获取到分类，使用默认分类
    if len(categories) == 0:
        categories = ["默认"]
        
    logger.info(f"最终获取到的分类: {categories}")
    return categories

async def crawl_nieta_art():
    """使用 Playwright 爬取 Nieta Art 网站内容"""
    # 创建数据集目录
    os.makedirs(DATASET_DIR, exist_ok=True)

    async with async_playwright() as p:
        # 启动浏览器 (默认使用 Chromium)
        browser = await p.chromium.launch(headless=False)  # headless=False 以便用户可以看到浏览器并进行登录操作
        page = await browser.new_page()

        # 创建 aiohttp session 用于下载图片
        async with aiohttp.ClientSession() as session:
            # 导航到目标页面
            logger.info(f"正在导航到: {TARGET_URL}")
            await page.goto(TARGET_URL)

            # 登录后获取页面信息
            logger.info("正在获取页面信息...")
            title = await page.title()
            current_url = page.url

            # 这里可以添加更多爬取逻辑
            # 例如获取特定元素的内容等
            logger.info(f"页面标题: {title}")
            logger.info(f"当前URL: {current_url}")

            # 定位到微信图标并点击
            wechat_icon = page.locator("._svg-comp_85lny_1.w-36px.h-36px")
            await wechat_icon.click()
            # 等待页面响应
            await page.wait_for_load_state("networkidle")            
            # 定位并点击同意按钮
            agree_button = page.locator("button[aria-label='confirm']").nth(-1)
            await agree_button.click()

           # 等待用户手动登录
            logger.info("请在浏览器中手动登录...")
            logger.info("登录完成后，请在终端中按 Enter 键继续...")
            input("按 Enter 键继续...")

            # 获取所有分类
            logger.info("正在获取所有分类...")
            categories = await get_categories(page)

            # 存储所有角色信息
            all_characters = []
        
            # 遍历每个分类提取数据
            for i, category in enumerate(categories[:6]):  # 前4个分类为空，直接从第5个分类开始 限制前5个分类以节省时间
                logger.info(f"正在处理第 {i+1} 个分类: {category}")
                # 前4个分类为空，直接跳过
                if i < 4:
                    continue
                try:
                    # 查找并点击分类
                    category_elements = await page.query_selector_all('.flex.flex-col .shrink-0.relative')
                    
                    if i < len(category_elements):
                        logger.info(f"点击分类 {category}")

                        b = category_elements[i]
                        await b.click()
                        await page.wait_for_load_state("networkidle")
                        await page.wait_for_timeout(2000)

                except Exception as e:
                    logger.error(f"点击分类 {category} 时出错: {e}")
                    continue

                # 提取当前分类的角色信息
                logger.info(f"正在提取 {category} 分类的角色信息...")
                characters = await extract_characters_with_scrolling(page, session, category)
            
                # 处理角色详情
                if characters:
                    logger.info(f"开始处理 {category} 分类的角色详情...")
                    processed_characters = await process_character_details(page, session, characters, category)
                    all_characters.extend(processed_characters)
                else:
                    logger.warning(f"{category} 分类没有找到角色")
        
            # 保存所有数据到JSON文件
            output_file = f"{DATASET_DIR}/nieta_all_characters.json"
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(all_characters, f, ensure_ascii=False, indent=2)

            # 按分类统计
            category_stats = {}
            download_stats = {"avatar_success": 0, "avatar_failed": 0, "creation_images": 0}

            for character in all_characters:
                category = character.get("category", "未知")
                if category in category_stats:
                    category_stats[category] += 1
                else:
                    category_stats[category] = 1

                # 统计头像下载成功/失败数量
                if character.get("download_success", False):
                    download_stats["avatar_success"] += 1
                else:
                    download_stats["avatar_failed"] += 1

                # 统计创作图片数量
                creation_count = character.get("creation_images_count", 0)
                download_stats["creation_images"] += creation_count

            logger.info(f"已保存 {len(all_characters)} 个角色信息到 {output_file}")
            logger.info("各分类角色数量统计:")
            for category, count in category_stats.items():
                logger.info(f"  {category}: {count} 个角色")

            logger.info("图片下载统计:")
            logger.info(f"  头像成功下载: {download_stats['avatar_success']} 张")
            logger.info(f"  头像下载失败: {download_stats['avatar_failed']} 张")
            logger.info(f"  创作图片总数: {download_stats['creation_images']} 张")

        # 关闭浏览器
        await browser.close()
        logger.info("爬取完成!")

if __name__ == "__main__":
    asyncio.run(crawl_nieta_art())
