#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import sys
from PIL import Image
from pathlib import Path
import os

import argparse

def parse_args():
    desc = "Convert JPEG to PNG"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--infile', type=str, required=True, help='Input JPEG file')
    parser.add_argument('--outfile', type=str, help='Output PNG file (optional)')
    return parser.parse_args()

def convert_jpeg_to_png(infile: str, outfile: str = None):
    # Open JPEG image
    img = Image.open(infile)
    
    # If no output file specified, use same name with .png extension
    if outfile is None:
        outfile = str(Path(infile).with_suffix('.png'))
        
    # Save as PNG
    img.save(outfile, 'PNG')
    print(f"Converted {infile} to {outfile}")

def main():
    args = parse_args()
    convert_jpeg_to_png(args.infile, args.outfile)

if __name__ == "__main__":
    main()
