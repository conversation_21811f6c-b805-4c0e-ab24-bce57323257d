# coding=utf-8
import cv2
import numpy as np
import random
import os
from argparse import ArgumentParser
 
from PIL import Image
from PIL import ImageDraw, ImageFont
  
ALPHA = 5
   
class BlindWaterMark():
    """盲水印加解密，无频移简单版"""
    def __init__(self):
        self.parser = ArgumentParser()
        self.parser.add_argument('--action', dest='action', required=True)
        self.parser.add_argument('--origin', dest='ori', required=True)
        self.parser.add_argument('--marker', dest='marker', required=True)
        self.parser.add_argument('--result', dest='res', required=True)
        self.parser.add_argument('--alpha', dest='alpha', default=ALPHA)
   
    def encode1(self, ori_path, wm_path, res_path, alpha):
        img = cv2.imread(ori_path)
        img_f = np.fft.fft2(img)  # 2维离散傅里叶变换
   
        height, width, channel = np.shape(img)
        watermark = cv2.imread(wm_path)
        wm_height, wm_width = watermark.shape[0], watermark.shape[1]
   
        # 水印随机编码
        x, y = list(range(height // 2)), list(range(width))
        random.seed(height + width)   # 随机数解码时可控
        random.shuffle(x)
        random.shuffle(y)
           
        # 按目标图片大小 对水印图进行对称
        tmp = np.zeros(img.shape)  # 根据图片形状，生成0填充的矩阵
   
        for i in range(height // 2):
            for j in range(width):
                if x[i] < wm_height and y[j] < wm_width:
                    tmp[i][j] = watermark[x[i]][y[j]]
                    tmp[height - 1 - i][width - 1 - j] = tmp[i][j]
   
        res_f = img_f + alpha * tmp  # 原图频域值  +  水印频域值
        res = np.fft.ifft2(res_f)      # 傅里叶逆变换
        res = np.real(res)  # 转换为实数
   
        cv2.imwrite(res_path, res, [int(cv2.IMWRITE_JPEG_QUALITY), 100])
   
 
    def encode(self, img_path, wm_path, res_path, alpha):
        img = cv2.imread(img_path)
        img_f = np.fft.fft2(img)
        height, width, channel = np.shape(img)
        watermark = cv2.imread(wm_path)
        wm_height, wm_width = watermark.shape[0], watermark.shape[1]
        x, y = list(range(int(height / 2))), list(range(width))
        random.seed(height + width)
        random.shuffle(x)
        random.shuffle(y)
        tmp = np.zeros(img.shape)
        for i in range(int(height / 2)):
            for j in range(width): 

                if x[i] < wm_height and y[j] < wm_width:
                    tmp[i][j] = watermark[x[i]][y[j]]
                    tmp[height - 1 - i][width - 1 - j] = tmp[i][j]
        res_f = img_f + alpha * tmp
        res = np.fft.ifft2(res_f)
        res = np.real(res)
        cv2.imwrite(res_path, res, [int(cv2.IMWRITE_JPEG_QUALITY), 100])

    def decode(self, ori_path, img_path, res_path, alpha):
        ori = cv2.imread(ori_path)
        img = cv2.imread(img_path)
        ori_f = np.fft.fft2(ori)
        img_f = np.fft.fft2(img)
        height, width = ori.shape[0], ori.shape[1]
        watermark = (ori_f - img_f) / alpha
        watermark = np.real(watermark)
        res = np.zeros(watermark.shape)
        random.seed(height + width)
        x = list(range(int(height / 2)))
        y = list(range(width))
        random.shuffle(x)
        random.shuffle(y)
        for i in range(int(height / 2)):
            for j in range(width):
                res[x[i]][y[j]] = watermark[i][j]
                
        cv2.imwrite(res_path, res, [int(cv2.IMWRITE_JPEG_QUALITY), 100])
          
    def decode1(self, ori_path, img_path, res_path, alpha):
        ori = cv2.imread(ori_path)
        img = cv2.imread(img_path)
   
        ori_f = np.fft.fft2(ori)
        img_f = np.fft.fft2(img)
   
        height, width = ori.shape[0], ori.shape[1]
        watermark = (ori_f - img_f) / alpha
   
        watermark = np.real(watermark)
        res = np.zeros(watermark.shape)
   
        random.seed(height + width)
   
        x = list(range(height / 2))
        y = list(range(width))
        random.shuffle(x)
        random.shuffle(y)
   
        for i in range(height / 2):
            for j in range(width):
                res[x[i]][y[j]] = watermark[i][j]
                res[height - i - 1][width - j - 1] = res[i][j]
   
        cv2.imwrite(res_path, res, [int(cv2.IMWRITE_JPEG_QUALITY), 100])
   
   
    def gen_marker_img(self, marker:str, ori:str):

        img = Image.open(ori)
        (width, height) = img.size
        img.close()
        
        image = Image.new('RGBA',(width,height),"white")
        font = ImageFont.truetype("static/fonts/SIMLI.TTF", 50)
        draw = ImageDraw.Draw(image)
        
        position = (5, 5)
        left, top, right, bottom  = draw.textbbox(position, marker, font=font)
        draw.text(position, marker, font=font, fill=(0,0,0,255))
        
        for i in range(55, height, 50):
            position = (5, i)
            left, top, right, bottom  = draw.textbbox(position, marker, font=font)
            draw.text(position, marker, font=font, fill=(0,0,0,255))   
                
        image.save("./media/marker.png")
        return "./media/marker.png"

    def run(self):
        options = self.parser.parse_args()
        action = options.action
        ori = options.ori
        marker = options.marker
        res = options.res
        alpha = float(options.alpha)
   
        if not os.path.isfile(ori):
            self.parser.error("image %s does not exist." % ori)
            
        marker_img = self.gen_marker_img(marker, ori)
        
        if action == "encode":
            self.encode(ori, marker_img, res, alpha)
        elif action == "decode":
            self.decode(ori, marker_img, res, alpha)
   
   
if __name__ == '__main__':
    bwm = BlindWaterMark()
    bwm.run()
    