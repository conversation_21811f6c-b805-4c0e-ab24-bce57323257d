#!/usr/bin/env python 
# -*- coding:utf-8 -*-
# 调用接口 一键换脸
#

import requests
import base64
import os
import re 
import json
import random
import argparse
import io


# curl -X POST \
# 	'http://127.0.0.1:7860/reactor/image' \
#     -H 'accept: application/json' \
#     -H 'Content-Type: application/json' \
#     -d '{
#     "source_image": "data:image/png;base64,/9j/4QAYRXhpZgAASUkqAAgAAAAAAAAAAAAAAP/sABFEdWNreQABAAQAAABQAAD/7g...",
#     "target_image": "data:image/png;base64,/9j/4QAYRXhpZgAASUkqAAgAAAAAAAAAAAAAAP/sABFEdWNreQABAAQAAABCAAD/7g...",
#     "source_faces_index": [0],
#     "face_index": [0],
#     "upscaler": "4x_Struzan_300000",
#     "scale": 2,
#     "upscale_visibility": 1,
#     "face_restorer": "CodeFormer",
#     "restorer_visibility": 1,
#     "restore_first": 1,
#     "model": "inswapper_128.onnx",
#     "gender_source": 0,
#     "gender_target": 0,
#     "save_to_file": 0,
#     "result_file_path": ""
# 	}'

from PIL import Image, PngImagePlugin
base_url="http://127.0.0.1:7860"

def url_reactor():
    return f"{base_url}/reactor/image"

def simple_img_reactor_request():
    return {
    "source_image": "data:image/png;base64,",
    "target_image": "data:image/png;base64,",
    "source_faces_index": [0],
    "face_index": [0],
    "upscaler": "R-ESRGAN 4x+",
    "scale": 1,
    "upscale_visibility": 1,
    "face_restorer": "CodeFormer",
    "restorer_visibility": 1,
    "restore_first": 1,
    "model": "inswapper_128.onnx",
    "gender_source": 0,
    "gender_target": 0,
    "save_to_file": 0,
    "result_file_path": ""
	}

def save_img(image:str, outdir: str, name_prefix: str):
    
    i = 0
    prefix = "%06d" % (i)
    outfile = os.path.join(outdir, f"{name_prefix}_{prefix}.png")
    while os.path.exists(outfile):
        i +=  1
        prefix = "%06d" % (i)
        outfile = os.path.join(outdir, f"{name_prefix}_{prefix}.png")

    with open(outfile, "wb") as f:
        f.write(base64.b64decode(image))

def img_reactor(outdir:str, src_img:str, tgt_img:str, name_prefix:str):
    
    params = simple_img_reactor_request()
    params["source_image"] = f"data:image/png;base64,{src_img}"
    params["target_image"] = f"data:image/png;base64,{tgt_img}"
    
    resp = requests.post(url_reactor(), json=params)
    resp_json = resp.json()
    save_img(resp_json["image"], outdir, name_prefix)

def gen_image(outdir:str, src_img : str, tgt_img : str, name_prefix:str):

    os.makedirs(outdir, exist_ok=True)
    img_reactor(outdir, src_img, tgt_img, name_prefix)

def encode_image(image):

    with io.BytesIO() as output_bytes:
        metadata = None
        for key, value in image.info.items():
            if isinstance(key, str) and isinstance(value, str):
                if metadata is None:
                    metadata = PngImagePlugin.PngInfo()
                metadata.add_text(key, value)
        image.save(output_bytes, format="PNG", pnginfo=metadata)
        bytes_data = output_bytes.getvalue()
        
        ret = str(base64.b64encode(bytes_data), "utf-8")
        return ret 

def main(args):

    outdir = args.outdir
    facedir = args.facedir
    faceList = []
    if os.path.isfile(facedir):
        name = facedir
        if name.endswith(".png") or name.endswith(".jpg") or name.endswith(".jpeg"):           
            img = Image.open(name)
            img = encode_image(img)
            faceList.append(img)
    else:
        for p, dirs, files in os.walk(facedir):
            for name in files:
                if name.endswith(".png") or name.endswith(".jpg") or name.endswith(".jpeg"):
                    print(os.path.join(p, name))
                    img = Image.open(os.path.join(p, name))
                    img = encode_image(img)
                    faceList.append(img)
                    
    if os.path.isfile(args.indir):
        name = args.indir
        if name.endswith(".png") or name.endswith(".jpg") or name.endswith(".jpeg"):
            img = Image.open(name)
            img = encode_image(img)
            
            name_prefix = os.path.split(name.split(".")[0])[1]
            for i, src_img  in enumerate(faceList):
                gen_image(outdir, src_img, img, f"{i}_{name_prefix}")
    else:
        for p, dirs, files in os.walk(args.indir):

            for name in files:
                if name.endswith(".png") or name.endswith(".jpg") or name.endswith(".jpeg"):
                    print(os.path.join(p, name))
                    img = Image.open(os.path.join(p, name))
                    img = encode_image(img)
                    name_prefix = name.split(".")[0]
                    for i, src_img  in enumerate(faceList):
                        gen_image(outdir, src_img, img, f"{i}_{name_prefix}")

def setup_parser() -> argparse.ArgumentParser:

    parser = argparse.ArgumentParser()
    parser.add_argument("--indir", type=str, required=True, default=None, help="sence to generate")
    parser.add_argument("--facedir", type=str, required=True, default=None, help="face to replace")
    parser.add_argument("--outdir", type=str, required=True, default="outputs", help="dir to write results to")
    return parser

if __name__ == "__main__":
    
    parser = setup_parser()
    args = parser.parse_args()
    main(args)


