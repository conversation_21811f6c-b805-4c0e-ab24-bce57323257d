#!/usr/bin/env python 
# -*- coding:utf-8 -*-
# 调用接口 
# sd 文生图
# lora  
# crontol net tile | tile difusion |ultimate SD upscale 
# target resolution 1080,1920 
#

import requests
import base64
import os
import re 
import json
import random
import argparse
from PIL import Image, PngImagePlugin
import io
import piexif
import piexif.helper

base_url="http://127.0.0.1:7860"
common_negative="EasyNegative, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry"

def url_txt2img():
    return f"{base_url}/sdapi/v1/txt2img"

def url_img2img():
    return f"{base_url}/sdapi/v1/img2img"

def url_interrogate():
    return f"{base_url}/sdapi/v1/interrogate"

def save_img(images:list[str], args: dict, outimg: str):
    
    img = images.pop()
    outpath = os.path.dirname(outimg)
    os.makedirs(outpath, exist_ok=True)

    with open(outimg, "wb") as f:
        f.write(base64.b64decode(img))
            
def simple_img2img_request():
    return {
        "batch_size": 1,
        "cfg_scale": 7,
        "denoising_strength": 0.75,
        "eta": 0,
        "height": 512,
        "include_init_images": True,
        "init_images": [],
        "inpaint_full_res": False,
        "inpaint_full_res_padding": 0,
        "inpainting_fill": 0,
        "inpainting_mask_invert": False,
        "mask": None,
        "mask_blur": 4,
        "n_iter": 1,
        "negative_prompt": "",
        "override_settings": {},
        "prompt": "",
        "resize_mode": 0,
        "restore_faces": True,
        "s_churn": 0,
        "s_noise": 1,
        "s_tmax": 0,
        "s_tmin": 0,
        "sampler_index": "DPM++ 2M SDE Karras",
        "seed": -1,
        "seed_resize_from_h": -1,
        "seed_resize_from_w": -1,
        "steps": 20,
        "styles": [],
        "subseed": -1,
        "subseed_strength": 0,
        "tiling": False,
        "width": 512,
    }

def img2img_with_complex_prompt(image:str, prompt:str, negative_prompt:str, outimg:str):
    params = simple_img2img_request()
    params["prompt"] = prompt
    params["negative_prompt"] = negative_prompt
    params["init_images"].append(image)

    resp = requests.post(url_img2img(), json=params)
    resp_json = resp.json()
    save_img(resp_json["images"], resp_json["parameters"], outimg)

def sample_prompt(prompt:str):
    
    prompt_list = prompt.split()
    sample_size = len(prompt_list) // 5
    sampled_prompt = random.sample(prompt_list, len(prompt_list) - sample_size)
    return ' '.join(sampled_prompt)

def encode_pil_to_base64(image, ext:str):

    with io.BytesIO() as output_bytes:

        if ext.lower() == 'png':
            use_metadata = False
            metadata = PngImagePlugin.PngInfo()
            for key, value in image.info.items():
                if isinstance(key, str) and isinstance(value, str):
                    metadata.add_text(key, value)
                    use_metadata = True
            image.save(output_bytes, format="PNG", pnginfo=(metadata if use_metadata else None), quality=80)

        elif ext.lower() in ("jpg", "jpeg", "webp"):
            parameters = image.info.get('parameters', None)
            exif_bytes = piexif.dump({
                "Exif": { piexif.ExifIFD.UserComment: piexif.helper.UserComment.dump(parameters or "", encoding="unicode") }
            })
            if opts.samples_format.lower() in ("jpg", "jpeg"):
                image.save(output_bytes, format="JPEG", exif = exif_bytes, quality=80)
            else:
                image.save(output_bytes, format="WEBP", exif = exif_bytes, quality=80)

        else:
            raise HTTPException(status_code=500, detail="Invalid image format")

        bytes_data = output_bytes.getvalue()

    return base64.b64encode(bytes_data)

def main(args):

    img = Image.open(args.inimg)
    ext = args.inimg.split(".")[-1]
    imgb64 = encode_pil_to_base64(img, ext)
    imgb64 = imgb64.decode("utf-8")
    params = {"image" : imgb64}
    resp = requests.post(url_interrogate(), json=params)
    resp_json = resp.json()
    prompt = resp_json["caption"]
    img2img_with_complex_prompt(imgb64, prompt, common_negative, args.outimg)

def setup_parser() -> argparse.ArgumentParser:

    parser = argparse.ArgumentParser()
    parser.add_argument("--inimg", type=str, default=None, help="input image name")
    parser.add_argument("--outimg", type=str, default=None, help="output image name")

    return parser

if __name__ == "__main__":
    parser = setup_parser()

    args = parser.parse_args()
    main(args)


