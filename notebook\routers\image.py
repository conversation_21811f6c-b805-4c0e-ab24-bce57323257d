#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from typing import Annotated, BinaryIO, Union
from fastapi import APIRouter, UploadFile, Body, Form, Request, Query
import numpy as np
from pyiqa import create_metric
import cv2 
import torch
from pyiqa.utils.img_util import img2tensor
import urllib.request
import random 
import oss2
import base64
import uuid 
import datetime
from fastapi.responses import StreamingResponse
import io
import time

from ..schemas.image import ImageScore, ImageInfo, ImageCompress
from ..utils import RespCode
from ..config import settings

from ..utils import DecryptRoute
from ..auth import decodeJWT
from ..schemas.user import UserJwt
from ..utils import log_csv 
from ..utils import translate_post, contains_zhCN
# from ..utils import sdxlpix2pix as pix2pix 
# from ..utils import sdxl_instantid as sdxl_instantid 
from ..utils import img2img as img2img
from ..utils.redis_stock import redisStock
from ..utils import PromptIsEmptyException, PromptTooLongException

from PIL import Image 

from ..utils.cipher_lib import CipherAgent
cipher_tool = CipherAgent().get_cipher()

import logging
img_logger = logging.getLogger("img")

router = APIRouter(prefix='/image', tags=['图像接口'])
router.route_class = DecryptRoute

def read_image(image: BinaryIO):
    image_bytes = image.read()
    nparr = np.fromstring(image_bytes, np.uint8)
    img = cv2.imdecode(nparr, cv2.IMREAD_COLOR)
    return img

def brisque(input_img : np.ndarray):
    
    img = cv2.resize(input_img, (512,384))
    img_tensor = img2tensor(img)
    img_tensor = img_tensor.unsqueeze(0)

    metric_name = "brisque"
    iqa_model = create_metric(metric_name, metric_mode="NR")
    score = iqa_model(img_tensor, None).cpu().item()
    score = 100 - score 
    if score < 30:
        score = 10 + random.random() * 20
    if score > 100:
        score = 100
    return score

def musiq_koniq(input_img: np.ndarray):

    img = cv2.resize(input_img, (512,384))
    img_tensor = img2tensor(img)
    img_tensor = img_tensor.unsqueeze(0)
    metric_name = "musiq-koniq"
    iqa_model = create_metric(metric_name, metric_mode="NR")
    score = iqa_model(img_tensor, None).cpu().item()
    return score

@router.post("/score_file",  summary="图片质量评分-文件上传")
async def score_image(upload_image: UploadFile):
    
    img_binary = read_image(upload_image.file)
    score = (brisque(img_binary) + musiq_koniq(img_binary)) // 2

    img_logger.info(f"img_logger, score_file:{upload_image.filename}, score:{score}")
    return RespCode.resp_ok({"filename": upload_image.filename, "score": score})

@router.post("/score_oss",  summary="图片质量评分-oss文件URL")
async def score_image(form_data: ImageScore = Body(...)):
    """
        图片质量评分接口, 分数分布范围0~100, 分越高代表质量越好
    """
    loc = form_data.loc
    with urllib.request.urlopen(loc) as response: 
        img_binary = read_image(response)
        score = (brisque(img_binary) + musiq_koniq(img_binary)) // 2
        return RespCode.resp_ok({"score": score})

@router.post("/img2img_oss",  summary="图生图-oss文件URL")
async def img2img_oss(request:Request, form_data: ImageInfo = Body(...), token:Union[str, None] = Query(default=None)):
    """
        图生图接口, 提供图片的url,返回生成图片的url
    """
    loc = form_data.loc
    prompt = form_data.prompt_en
    user = UserJwt.parse_obj(decodeJWT(token))

    with urllib.request.urlopen(loc) as response: 

        imgdata,imgraw = img2img.img2img_from_bytes(response.read(), prompt)
        # imgdata,imgraw = pix2pix.instruct_img_from_bytes(response, prompt)
        img_logger.info(log_csv([int(time.time()),request.client.host,user.uid,user.cid,user.app,user.ver,prompt,imgdata,imgraw]))

        now = datetime.datetime.now()
        year_month_day = now.strftime("%Y/%m/%d")
        fprefix = str(uuid.uuid4())

        oss_path = "/".join([settings.OSS_IMAGE_PREFIX, year_month_day, f"{fprefix}.png"])
        bucket = oss2.Bucket(oss2.Auth(settings.OSS_SECRETKEY, settings.OSS_SECRETPASS), settings.OSS_ENDPOINT, settings.OSS_BUCKET)
        bucket.put_object(oss_path, base64.b64decode(imgdata))
        location = f"https://{settings.OSS_BUCKET}.{settings.OSS_ENDPOINT}/{settings.OSS_IMAGE_PREFIX}/{year_month_day}/{fprefix}.png"

        return RespCode.resp_ok({"location": location})

@router.post("/img2img_file",  summary="图生图-post上传文件")
async def img2img_file(request:Request, upload_image: UploadFile, prompt:str = Form(..., example="one beautify girl"),token:Union[str, None] = Query(default=None)):
    """
        图生图接口, 上传文件, 返回生成图片的url
    """
    user = UserJwt.parse_obj(decodeJWT(token))

    if len(prompt) == 0:
        raise PromptIsEmptyException()
    if len(prompt) > 100:
        raise PromptTooLongException()
    
    if(contains_zhCN(prompt)):
        prompt = redisStock.stripSensitive(prompt)
        prompt = translate_post("en", prompt)

    imgdata, imgraw = img2img.img2img_from_bytes(upload_image.file.read(), prompt)
    # imgdata,imgraw = pix2pix.instruct_img_from_bytes(upload_image.file, prompt)
    img_logger.info(log_csv([int(time.time()),request.client.host,user.uid,user.cid,user.app,user.ver,prompt,imgdata,imgraw]))

    now = datetime.datetime.now()
    year_month_day = now.strftime("%Y/%m/%d")
    fprefix = str(uuid.uuid4())
    
    oss_path = "/".join([settings.OSS_IMAGE_PREFIX, year_month_day, f"{fprefix}.png"])
    bucket = oss2.Bucket(oss2.Auth(settings.OSS_SECRETKEY, settings.OSS_SECRETPASS), settings.OSS_ENDPOINT, settings.OSS_BUCKET)
    bucket.put_object(oss_path, base64.b64decode(imgdata))
    location = f"https://{settings.OSS_BUCKET}.{settings.OSS_ENDPOINT}/{settings.OSS_IMAGE_PREFIX}/{year_month_day}/{fprefix}.png"

    return RespCode.resp_ok({"location": location})

@router.post("/img2img_file_save",  summary="图生图-post上传文件，本地保存")
async def img2img_file_save(request:Request, upload_image: UploadFile, 
                            prompt:str = Form(..., example="one beautify girl"), 
                            enc:bool = Form(default=True, example=True),
                            token:Union[str, None] = Query(default=None)):
    """
        图生图接口, 上传文件, 返回生成下载图片
    """

    if len(prompt) == 0:
        raise PromptIsEmptyException()
    if len(prompt) > 100:
        raise PromptTooLongException()
    
    user = UserJwt.parse_obj(decodeJWT(token))
    
    if(contains_zhCN(prompt)):
        prompt = redisStock.stripSensitive(prompt)
        prompt = translate_post("en", prompt)

    if enc:
        imgdata, nsfw, imgraw = img2img.img2img_from_bytes(cipher_tool.decrypt(upload_image.file.read()), prompt) 
    else:
        imgdata, nsfw, imgraw = img2img.img2img_from_bytes(upload_image.file.read(), prompt)

    # imgdata,imgraw = pix2pix.instruct_img_from_bytes(upload_image.file, prompt)
    img_logger.info(log_csv([int(time.time()),request.client.host,user.uid,user.cid,user.app,user.ver,prompt,imgdata,imgraw]))

    
    fprefix = str(uuid.uuid4())
    output = io.BytesIO()
    output.write(base64.b64decode(imgdata))
    output.seek(0)
    if nsfw:
        headers = {
            'NSFW': 'true',
            'Content-type': 'image/png',
            'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
        }
    else:
        headers = {
            'Content-type': 'image/png',
            'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
        }

    return StreamingResponse(content=output, headers=headers, media_type="image/png")

@router.post("/img2img_compress",  summary="图生图-base64提交-base64返回")
async def img2img_compress(request:Request, form_data: ImageCompress = Body(...), token:Union[str, None] = Query(default=None)):
    """
        图生图接口, 上传文件, 返回生成下载图片
    """

    user = UserJwt.parse_obj(decodeJWT(token))

    imgraw = form_data.img
    prompt = form_data.prompt_en

    imgdata, nsfw, imgraw = img2img.img2img_from_base64(imgraw, prompt) 
    # imgdata,imgraw = pix2pix.instruct_img_from_base64(imgraw, prompt)
    img_logger.info(log_csv([int(time.time()),request.client.host,user.uid,user.cid,user.app,user.ver,prompt,imgdata,imgraw]))

    return RespCode.resp_ok({"img":imgdata, "NFSW": nsfw})
    
@router.post("/img_reactor",  summary="换脸-post上传文件，本地保存")
async def img2img_reactor(request:Request, src_image: UploadFile, tgt_image: UploadFile,
                            src_face:int = Form(default=0),
                            tgt_face:int = Form(default=0),
                            enc:bool = Form(default=True, example=True),
                            token:Union[str, None] = Query(default=None)):
    """
        换脸接口, 上传文件, 返回生成下载图片
    """
        
    user = UserJwt.parse_obj(decodeJWT(token))
    
    if enc:
        imgdata, nsfw, imgsrc, imgtgt = img2img.img2img_reactor(cipher_tool.decrypt(src_image.file.read()), cipher_tool.decrypt(tgt_image.file.read()), src_face, tgt_face) 
    else:
        imgdata, nsfw, imgsrc, imgtgt = img2img.img2img_reactor(src_image.file.read(), tgt_image.file.read(), src_face, tgt_face)

    img_logger.info(log_csv([int(time.time()),request.client.host,user.uid,user.cid,user.app,user.ver,"",imgdata,imgsrc,imgtgt]))

    
    fprefix = str(uuid.uuid4())
    output = io.BytesIO()
    output.write(base64.b64decode(imgdata))
    output.seek(0)

    if nsfw:
        headers = {
            'NSFW': 'true',
            'Content-type': 'image/png',
            'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
        }
    else:
        headers = {
            'Content-type': 'image/png',
            'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
        }
        
    return StreamingResponse(content=output, headers=headers, media_type="image/png")


# from enum import Enum
# class StyleModel(str, Enum):
#     SpringFestival = "春节"
#     Watercolor = "水彩"
#     FilmNoir = "黑白电影"
#     Neon = "元素"
#     Jungle = "丛林"
#     Mars = "火星"
#     VibrantColor = "油彩"
#     Snow = "雪花"
#     LineArt = "线稿"

# @router.post("/img_style",  summary="风格化-post上传文件，本地保存")
# async def img2img_style(request:Request, face_image: UploadFile, pose_image: UploadFile,
#                             prompt:str = Form(default="a person"),
#                             style:StyleModel = Form(default=StyleModel.SpringFestival),
#                             enc:bool = Form(default=True, example=True),
#                             token:Union[str, None] = Query(default=None)):
#     """
#         风格化接口, 上传文件, 返回生成下载图片
#     """
      
#     if len(prompt) == 0:
#         raise PromptIsEmptyException()
#     if len(prompt) > 200:
#         raise PromptTooLongException()
    
#     user = UserJwt.parse_obj(decodeJWT(token))
    
#     if(contains_zhCN(prompt)):
#         prompt = redisStock.stripSensitive(prompt)
#         prompt = translate_post("en", prompt)
    
#     style = str(style).split(".")[-1]
       
#     if enc:
#         imgdata, nsfw, imgsrc, imgtgt = sdxl_instantid.style_image(cipher_tool.decrypt(face_image.file.read()), cipher_tool.decrypt(pose_image.file.read()), prompt, style_name=style) 
#     else:
#         imgdata, nsfw, imgsrc, imgtgt = sdxl_instantid.style_image(face_image.file.read(), pose_image.file.read(), prompt, style_name=style)

#     img_logger.info(log_csv([int(time.time()),request.client.host,user.uid,user.cid,user.app,user.ver,f"{style},{prompt}",imgdata,imgsrc,imgtgt]))
    
#     fprefix = str(uuid.uuid4())
#     output = io.BytesIO()
#     output.write(base64.b64decode(imgdata))
#     output.seek(0)

#     if nsfw:
#         headers = {
#             'NSFW': 'true',
#             'Content-type': 'image/png',
#             'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
#         }
#     else:
#         headers = {
#             'Content-type': 'image/png',
#             'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
#         }

#     return StreamingResponse(content=output, headers=headers, media_type="image/png")


