{"1": {"inputs": {"vae_name": "flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "12": {"inputs": {"image": "hug_ai.png"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "16": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5-v1_1-xxl-encoder-Q5_K_S.gguf", "type": "flux"}, "class_type": "DualCLIPLoaderGGUF", "_meta": {"title": "DualCLIPLoader (GGUF)"}}, "17": {"inputs": {"unet_name": "flux1-dev-Q4_K_S.gguf", "dequant_dtype": "bfloat16", "patch_dtype": "bfloat16", "patch_on_device": false}, "class_type": "UnetLoaderGGUFAdvanced", "_meta": {"title": "Unet Loader (GGUF/Advanced)"}}, "18": {"inputs": {"width": ["62", 0], "height": ["62", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": 0, "image": ["224", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "42": {"inputs": {"clip_l": ["440", 0], "t5xxl": ["440", 0], "guidance": 3.5, "speak_and_recognation": {"__value__": [false, true]}, "clip": ["455", 1]}, "class_type": "CLIPTextEncodeFlux", "_meta": {"title": "CLIPTextEncodeFlux"}}, "62": {"inputs": {"width": 1024, "height": 1024, "aspect_ratio": "3:4 portrait 896x1152", "swap_dimensions": "Off", "upscale_factor": 1, "batch_size": 1}, "class_type": "CR SDXL Aspect Ratio", "_meta": {"title": "AspectRatio"}}, "68": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["18", 1], "height": ["18", 2], "model": ["202", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "86": {"inputs": {"lora_name": "flux/Hyper-FLUX.1-dev-8steps-lora.safetensors", "strength_model": 0.13, "strength_clip": 0.13, "model": ["17", 0], "clip": ["16", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "145": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "146": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "147": {"inputs": {"provider": "CPU"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "148": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "model": ["455", 0], "pulid_flux": ["145", 0], "eva_clip": ["146", 0], "face_analysis": ["147", 0], "image": ["438", 1], "attn_mask": ["438", 2]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "197": {"inputs": {"from_translate": "auto", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "MyMemoryTranslator [free]", "text": "柔和的自然光透过窗户，洒在她精致的脸上，记录下她沉思的瞬间。", "hide_proxy": "proxy_hide", "hide_authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "UserPrompt"}}, "202": {"inputs": {"lora_name": "flux/FLUXpro1.1-FLUX-V0.2_aidmafluxpro1.1.safetensors", "strength_model": 0.9, "strength_clip": 0.9, "model": ["86", 0], "clip": ["86", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "205": {"inputs": {"samples": ["206", 0], "vae": ["1", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "206": {"inputs": {"noise": ["210", 0], "guider": ["209", 0], "sampler": ["207", 0], "sigmas": ["208", 0], "latent_image": ["232", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "207": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "208": {"inputs": {"scheduler": "simple", "steps": 8, "denoise": 1, "model": ["276", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "209": {"inputs": {"model": ["276", 0], "conditioning": ["457", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "210": {"inputs": {"noise_seed": 1043353764311724}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "215": {"inputs": {"anything": ["205", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "217": {"inputs": {"pixels": ["426", 1], "vae": ["1", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "224": {"inputs": {"image": "input/template/refer_16.jpg", "custom_width": 0, "custom_height": 0}, "class_type": "VHS_LoadImagePath", "_meta": {"title": "LoadImageFromPath"}}, "232": {"inputs": {"samples": ["217", 0], "mask": ["472", 0]}, "class_type": "SetLatentNoiseMask", "_meta": {"title": "Set Latent Noise Mask"}}, "275": {"inputs": {"model": ["148", 0]}, "class_type": "FluxForwardOverrider", "_meta": {"title": "FluxForwardOverrider"}}, "276": {"inputs": {"rel_l1_thresh": 0.25, "cache_device": "offload_device", "wan_coefficients": "disabled", "model": ["275", 0]}, "class_type": "ApplyTeaCachePatch", "_meta": {"title": "ApplyTeaCachePatch"}}, "357": {"inputs": {"filename_prefix": "refer/flux", "images": ["444", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "362": {"inputs": {"segs": ["451", 0]}, "class_type": "ImpactSEGSToMaskBatch", "_meta": {"title": "SEGS to Mask Batch"}}, "363": {"inputs": {"segs": ["448", 0]}, "class_type": "ImpactSEGSToMaskBatch", "_meta": {"title": "SEGS to Mask Batch"}}, "367": {"inputs": {"start": ["443", 1], "length": 1, "mask": ["363", 0]}, "class_type": "MaskFromBatch+", "_meta": {"title": "🔧 Mask From Batch"}}, "369": {"inputs": {"start": ["443", 1], "length": 1, "mask": ["362", 0]}, "class_type": "MaskFromBatch+", "_meta": {"title": "🔧 Mask From Batch"}}, "388": {"inputs": {"expand": -5, "tapered_corners": true, "mask": ["367", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "391": {"inputs": {"model": "promptgen_base_v2.0", "folder_path": "Path to your image folder", "caption_method": "detailed", "max_new_tokens": 1024, "num_beams": 4, "random_prompt": "never", "prefix_caption": "", "suffix_caption": "", "replace_tags": "", "speak_and_recognation": {"__value__": [false, true]}, "images": ["438", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Tagger", "_meta": {"title": "🐾MiaoshouAI Tagger"}}, "394": {"inputs": {"width": ["62", 0], "height": ["62", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": 0, "image": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "395": {"inputs": {"detail_method": "PyMatting", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.01, "white_point": 0.99, "process_detail": true, "device": "cuda", "max_megapixels": 2, "image": ["414", 0]}, "class_type": "LayerMask: RmBgUltra V2", "_meta": {"title": "LayerMask: RmBgUltra V2"}}, "414": {"inputs": {"upscale_model": ["415", 0], "image": ["394", 0]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "Upscale Image (using Model)"}}, "415": {"inputs": {"model_name": "4x-UltraSharp.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "Load Upscale Model"}}, "426": {"inputs": {"context_expand_pixels": 20, "context_expand_factor": 1, "fill_mask_holes": true, "blur_mask_pixels": 16, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "ranged size", "force_width": 1024, "force_height": 1024, "rescale_factor": 1, "min_width": 384, "min_height": 384, "max_width": 512, "max_height": 512, "padding": 32, "image": ["443", 2], "mask": ["388", 0]}, "class_type": "InpaintCrop", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"}}, "427": {"inputs": {"rescale_algorithm": "bislerp", "stitch": ["426", 0], "inpainted_image": ["215", 0]}, "class_type": "InpaintStitch", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Stitch node)"}}, "433": {"inputs": {"expand": -5, "tapered_corners": true, "mask": ["369", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "438": {"inputs": {"context_expand_pixels": 20, "context_expand_factor": 1, "fill_mask_holes": true, "blur_mask_pixels": 16, "invert_mask": false, "blend_pixels": 16, "rescale_algorithm": "bicubic", "mode": "ranged size", "force_width": 1024, "force_height": 1024, "rescale_factor": 1, "min_width": 384, "min_height": 384, "max_width": 512, "max_height": 512, "padding": 32, "image": ["452", 0], "mask": ["433", 0]}, "class_type": "InpaintCrop", "_meta": {"title": "(OLD 💀, use the new ✂️ Inpaint Crop node)"}}, "440": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": ["391", 2], "text_b": "face the viewer ", "text_c": "", "speak_and_recognation": {"__value__": [false, true]}, "result": "A close-up of a young Asian woman with long, straight black hair and fair skin. She is looking directly at the camera with a neutral expression. The background features a red and white Coca-Cola sign with the word \"Coca-Cola\" written in bold, white letters. The woman's lips are slightly parted, revealing a hint of pink lipstick. The lighting is soft and natural, highlighting her smooth skin. The image has a high-quality, professional feel., face the viewer"}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "443": {"inputs": {"total": ["446", 3], "initial_value1": ["18", 0]}, "class_type": "easy forLoopStart", "_meta": {"title": "For Loop Start"}}, "444": {"inputs": {"flow": ["443", 0], "initial_value1": ["427", 0]}, "class_type": "easy forLoopEnd", "_meta": {"title": "For Loop End"}}, "446": {"inputs": {"mask": ["363", 0]}, "class_type": "GetMaskSizeAndCount", "_meta": {"title": "Get Mask Size & Count"}}, "448": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["449", 0], "image": ["18", 0], "sam_model_opt": ["450", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "449": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "450": {"inputs": {"model_name": "sam_vit_b_01ec64.pth", "device_mode": "AUTO"}, "class_type": "SAMLoader", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}}, "451": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["449", 0], "image": ["452", 0], "sam_model_opt": ["450", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "452": {"inputs": {"width": ["62", 0], "height": ["62", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": 0, "image": ["453", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "453": {"inputs": {"fill_background": false, "background_color": "#000000", "RGBA_image": ["395", 0]}, "class_type": "LayerUtility: ImageRemoveAlpha", "_meta": {"title": "LayerUtility: ImageRemoveAlpha"}}, "455": {"inputs": {"lora_name": "flux/flux1-depth-dev-lora.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["68", 0], "clip": ["202", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "457": {"inputs": {"positive": ["42", 0], "negative": ["460", 0], "vae": ["1", 0], "pixels": ["459", 0]}, "class_type": "InstructPixToPixConditioning", "_meta": {"title": "InstructPixToPixConditioning"}}, "459": {"inputs": {"preprocessor": "DepthAnythingV2Preprocessor", "resolution": 512, "image": ["426", 1]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "460": {"inputs": {"conditioning": ["42", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "461": {"inputs": {"a": ["446", 3], "b": ["443", 1], "operation": "subtract"}, "class_type": "easy mathInt", "_meta": {"title": "Math Int"}}, "462": {"inputs": {"a": ["461", 0], "b": 1, "operation": "subtract"}, "class_type": "easy mathInt", "_meta": {"title": "Math Int"}}, "463": {"inputs": {"rgthree_comparer": {"images": [{"name": "A", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_fcxcf_00001_.png&type=temp&subfolder=&rand=0.0519771475643912"}, {"name": "B", "selected": true, "url": "/api/view?filename=rgthree.compare._temp_fcxcf_00002_.png&type=temp&subfolder=&rand=0.8126225031359765"}]}, "image_a": ["444", 0], "image_b": ["18", 0]}, "class_type": "Image Comparer (rgthree)", "_meta": {"title": "Image Comparer (rgthree)"}}, "472": {"inputs": {"invert_mask": false, "grow": 10, "blur": 4, "mask": ["426", 2]}, "class_type": "LayerMask: Mask<PERSON>row", "_meta": {"title": "LayerMask: Mask<PERSON>row"}}}