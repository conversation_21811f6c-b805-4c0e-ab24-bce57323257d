import asyncio
from playwright.async_api import async_playwright
import logging
import json
import os
import aiohttp
import aiofiles
from pathlib import Path
import re
from urllib.parse import urlparse

log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"

logging.basicConfig(level=logging.INFO, format=log_format)
logger = logging.getLogger(__name__)

# 目标 URL
TARGET_URL = "https://app.nieta.art/character/discover"

async def main():
    # 启动 Playwright
    async with async_playwright() as p:
        # 启动浏览器
        browser = await p.chromium.launch(headless=False)
        page = await browser.new_page()
        
        # 打开目标页面
        await page.goto(TARGET_URL)
        logger.info(f"已打开页面: {TARGET_URL}")
        
        # 等待用户输入
        input("请检查页面加载情况，按回车继续...")
        
        try:
            # 定位到微信图标并点击
            wechat_icon = page.locator("._svg-comp_85lny_1.w-36px.h-36px")
            await wechat_icon.click()
            # 等待页面响应
            await page.wait_for_load_state("networkidle")            
            # 定位并点击同意按钮
            agree_button = page.locator("button[aria-label='confirm']").nth(1)
            await agree_button.click()
            
            # 等待用户输入
            input("请登录微信...")
            
            # 等待页面响应
            await page.wait_for_load_state("networkidle")
            await page.wait_for_timeout(2000)
            # 等待用户输入
            input("请检查页面...")

            left_buttons = await page.query_selector_all('.flex.flex-col .shrink-0.relative')
            b = left_buttons[8]
            
            c = await b.inner_html()
            await b.click()
            await page.wait_for_load_state("networkidle")
            await page.wait_for_timeout(2000)
            input(f"请检查是否已点击{c}")

            card = await page.query_selector_all('.flex > .relative')
            await card[0].click()
            await page.wait_for_load_state("networkidle")
            await page.wait_for_timeout(2000)
            input(f"请检查页面已刷新")

            container = page.locator(".min-h-70vh").nth(0)
            await container.scroll_into_view_if_needed()

            await page.wait_for_timeout(2000)
            input(f"请检查页面已滚动")
            
            a_tag = page.locator('a[data-seo-link^="/collection/profile"]').nth(-1)
            await a_tag.scroll_into_view_if_needed()

            await page.wait_for_timeout(2000)
            input(f"请检查页面已刷新内容")

            await page.go_back()
            await page.wait_for_load_state("networkidle")
            await page.wait_for_timeout(2000)
            input(f"请检查页面已返回")

        except Exception as e:
            logger.error(f"操作过程中出现错误: {e}")
        
        # 关闭浏览器
        await browser.close()
        logger.info("浏览器已关闭")

if __name__ == "__main__":
    asyncio.run(main())
