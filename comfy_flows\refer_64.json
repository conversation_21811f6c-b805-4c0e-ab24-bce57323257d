{"1": {"inputs": {"vae_name": "flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "12": {"inputs": {"image": "微信图片_20250316120351.jpg"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "16": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5-v1_1-xxl-encoder-Q5_K_S.gguf", "type": "flux"}, "class_type": "DualCLIPLoaderGGUF", "_meta": {"title": "DualCLIPLoader (GGUF)"}}, "17": {"inputs": {"unet_name": "flux1-kontext-dev-Q5_K_M.gguf", "dequant_dtype": "bfloat16", "patch_dtype": "bfloat16", "patch_on_device": false}, "class_type": "UnetLoaderGGUFAdvanced", "_meta": {"title": "Unet Loader (GGUF/Advanced)"}}, "18": {"inputs": {"width": ["62", 0], "height": ["62", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": 0, "image": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "62": {"inputs": {"width": 1024, "height": 1024, "aspect_ratio": "3:4 portrait 896x1152", "swap_dimensions": "Off", "upscale_factor": 1, "batch_size": 1}, "class_type": "CR SDXL Aspect Ratio", "_meta": {"title": "AspectRatio"}}, "197": {"inputs": {"from_translate": "chinese simplified", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "MyMemoryTranslator [free]", "text": "", "Show proxy": "proxy_hide", "Show authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "UserPrompt"}}, "205": {"inputs": {"samples": ["313", 0], "vae": ["1", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "214": {"inputs": {"filename_prefix": "refer/kontext", "images": ["339", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "271": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "transform to Cute <PERSON> photobooth pic,  while keeping the same person.", "text_b": ["197", 0], "text_c": "", "speak_and_recognation": {"__value__": [false, true]}, "result": "transform to Cute <PERSON> photobooth pic, while keeping the same person."}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "308": {"inputs": {"conditioning": ["312", 0], "latent": ["309", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "309": {"inputs": {"pixels": ["322", 1], "vae": ["1", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "310": {"inputs": {"guidance": 2.5, "conditioning": ["308", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "312": {"inputs": {"text": ["271", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["16", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "313": {"inputs": {"seed": 157864116033078, "steps": 8, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["318", 0], "positive": ["310", 0], "negative": ["314", 0], "latent_image": ["309", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "314": {"inputs": {"conditioning": ["310", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "315": {"inputs": {"model_type": "flux", "rel_l1_thresh": 0.4, "start_percent": 0, "end_percent": 1, "cache_device": "cuda", "model": ["317", 0]}, "class_type": "TeaCache", "_meta": {"title": "TeaCache"}}, "317": {"inputs": {"lora_name": "flux/alimama-Flux.1-8steps-Turbo-Alpha.safetensors", "strength_model": 1, "model": ["17", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "318": {"inputs": {"lora_name": "kontext/facezoom.safetensors", "strength_model": 1, "model": ["315", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "322": {"inputs": {"downscale_algorithm": "bilinear", "upscale_algorithm": "bicubic", "preresize": true, "preresize_mode": "ensure minimum resolution", "preresize_min_width": 1024, "preresize_min_height": 1024, "preresize_max_width": 16384, "preresize_max_height": 16384, "mask_fill_holes": true, "mask_expand_pixels": 10, "mask_invert": false, "mask_blend_pixels": 64, "mask_hipass_filter": 0.1, "extend_for_outpainting": false, "extend_up_factor": 1, "extend_down_factor": 1, "extend_left_factor": 1, "extend_right_factor": 1, "context_from_mask_extend_factor": 1, "output_resize_to_target_size": true, "output_target_width": 512, "output_target_height": 512, "output_padding": "0", "image": ["340", 2], "mask": ["358", 0], "optional_context_mask": ["387", 1]}, "class_type": "InpaintCropImproved", "_meta": {"title": "✂️ Inpaint Crop (Improved)"}}, "334": {"inputs": {"bbox_threshold": 0.5, "bbox_dilation": 0, "crop_factor": 3, "drop_size": 10, "sub_threshold": 0.5, "sub_dilation": 0, "sub_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "post_dilation": 0, "bbox_detector": ["335", 0], "image": ["18", 0], "sam_model_opt": ["336", 0]}, "class_type": "ImpactSimpleDetectorSEGS", "_meta": {"title": "Simple Detector (SEGS)"}}, "335": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "336": {"inputs": {"model_name": "sam_vit_h_4b8939.pth", "device_mode": "AUTO"}, "class_type": "SAMLoader", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Impact)"}}, "337": {"inputs": {"segs": ["334", 0]}, "class_type": "ImpactSEGSToMaskBatch", "_meta": {"title": "SEGS to Mask Batch"}}, "338": {"inputs": {"mask": ["337", 0]}, "class_type": "GetMaskSizeAndCount", "_meta": {"title": "Get Mask Size & Count"}}, "339": {"inputs": {"flow": ["340", 0], "initial_value1": ["382", 0], "initial_value2": ["358", 0]}, "class_type": "easy forLoopEnd", "_meta": {"title": "For Loop End"}}, "340": {"inputs": {"total": ["338", 3], "initial_value1": ["18", 0]}, "class_type": "easy forLoopStart", "_meta": {"title": "For Loop Start"}}, "343": {"inputs": {"start": ["340", 1], "length": 1, "mask": ["337", 0]}, "class_type": "MaskFromBatch+", "_meta": {"title": "🔧 Mask From Batch"}}, "358": {"inputs": {"invert_mask": false, "grow": 10, "blur": 0, "mask": ["343", 0]}, "class_type": "LayerMask: Mask<PERSON>row", "_meta": {"title": "LayerMask: Mask<PERSON>row"}}, "359": {"inputs": {"anything": ["339", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "364": {"inputs": {"images": ["205", 0]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "366": {"inputs": {"mask": ["322", 2]}, "class_type": "MaskPreview", "_meta": {"title": "MaskPreview"}}, "367": {"inputs": {"images": ["322", 1]}, "class_type": "PreviewImage", "_meta": {"title": "Preview Image"}}, "375": {"inputs": {"mask": ["358", 0]}, "class_type": "MaskPreview", "_meta": {"title": "MaskPreview"}}, "382": {"inputs": {"upscale_ratio": 1.2, "stitcher": ["322", 0], "inpainted_image": ["205", 0], "mask": ["384", 1]}, "class_type": "InpaintStitchImprovedUpScale", "_meta": {"title": "✂️ Inpaint Stitch UpScale(Improved)"}}, "384": {"inputs": {"threshold": 0.3, "detail_method": "VITMatte", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.15, "white_point": 0.99, "process_detail": true, "prompt": "head, face, hair, neck, glass", "device": "cuda", "max_megapixels": 2, "image": ["205", 0], "sam_models": ["385", 0]}, "class_type": "LayerMask: SegmentAnythingUltra V3", "_meta": {"title": "LayerMask: SegmentAnythingUltra V3(Advance)"}}, "385": {"inputs": {"sam_model": "sam_vit_h (2.56GB)", "grounding_dino_model": "GroundingDINO_SwinT_OGC (694MB)"}, "class_type": "LayerMask: LoadSegmentAnythingModels", "_meta": {"title": "LayerMask: Load SegmentAnything Models(Advance)"}}, "386": {"inputs": {"mask": ["384", 1]}, "class_type": "MaskPreview", "_meta": {"title": "MaskPreview"}}, "387": {"inputs": {"threshold": 0.3, "detail_method": "VITMatte", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.15, "white_point": 0.99, "process_detail": true, "prompt": "head, face, hair, glass", "device": "cuda", "max_megapixels": 2, "image": ["340", 2], "sam_models": ["385", 0]}, "class_type": "LayerMask: SegmentAnythingUltra V3", "_meta": {"title": "LayerMask: SegmentAnythingUltra V3(Advance)"}}}