{"16": {"inputs": {"text": ["17", 0], "seed": 441719666645798}, "class_type": "Text Random Line", "_meta": {"title": "Text Random Line"}}, "17": {"inputs": {"string": "Play basketball,basketball jerseys,cry out\nPirate captain with a sword\nDrinking coffee from a coffee cup and smiling\nDressed in a spacesuit and wearing a glass helmet \nWearing luxurious clothes, holding a red heart-shaped balloon and smiling \n<PERSON><PERSON>, with red boxing gloves, swinging their fists \nDressed in a Superman costume \nDress up as <PERSON> with a rattle in your hand \nHolding a bouquet of roses in his hand and wearing a gown \nDressed in a wide hip hop suit,Playing the guitar,tattooing，denim，street dance，dancing ", "strip_newlines": false}, "class_type": "StringConstantMultiline", "_meta": {"title": "String Constant Multiline"}}, "18": {"inputs": {"text_positive": "STICKER，Flat:1.5,(full body:2,Simple details，(clean solid color background:2）", "text_negative": "Realism, photo-realism,embedding:ng_deepnegative_v1_75t", "style": "base", "log_prompt": true, "style_positive": true, "style_negative": true}, "class_type": "SDXLPromptStyler", "_meta": {"title": "SDXL Prompt Styler"}}, "19": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": ["18", 0], "text_b": ["69", 2], "text_c": ["16", 0], "result": "STICKER，Flat:1.5,(full body:2,Simple details，(clean solid color background:2）, A digital portrait of a young woman with long brown hair, looking directly at the viewer, Dress up as <PERSON> Claus with a rattle in your hand"}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "20": {"inputs": {"image": "v0d00fg10000cm5pr2rc77u7tcjikdk0[(000243)2024-01-13-20-48-06.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "24": {"inputs": {"text": ["19", 0], "clip": ["102", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "30": {"inputs": {"feathering": 120, "feather_second_pass": "true", "left_padding": 50, "right_padding": 50, "top_padding": 50, "bottom_padding": 50, "image": ["68", 0]}, "class_type": "Image Padding", "_meta": {"title": "Image Padding"}}, "40": {"inputs": {"detail_method": "PyMatting", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.01, "white_point": 0.99, "process_detail": true, "device": "cuda", "max_megapixels": 2, "image": ["20", 0]}, "class_type": "LayerMask: RmBgUltra V2", "_meta": {"title": "LayerMask: RmBgUltra V2"}}, "54": {"inputs": {"images": ["30", 0]}, "class_type": "AlphaChanelRemove", "_meta": {"title": "AlphaChanelRemove"}}, "68": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["40", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "69": {"inputs": {"model": "promptgen_base_v2.0", "folder_path": "Path to your image folder", "caption_method": "simple", "max_new_tokens": 128, "num_beams": 4, "random_prompt": "never", "prefix_caption": "", "suffix_caption": "", "replace_tags": "replace_tags eg:search1:replace1;search2:replace2", "images": ["54", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Tagger", "_meta": {"title": "🐾MiaoshouAI Tagger"}}, "72": {"inputs": {"vae_name": "flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "73": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5-v1_1-xxl-encoder-Q5_K_S.gguf", "type": "flux"}, "class_type": "DualCLIPLoaderGGUF", "_meta": {"title": "DualCLIPLoader (GGUF)"}}, "74": {"inputs": {"unet_name": "flux1-dev-Q4_K_S.gguf", "dequant_dtype": "bfloat16", "patch_dtype": "bfloat16", "patch_on_device": false}, "class_type": "UnetLoaderGGUFAdvanced", "_meta": {"title": "Unet Loader (GGUF/Advanced)"}}, "76": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": 768, "height": 1024, "model": ["102", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "77": {"inputs": {"lora_name": "flux/Hyper-FLUX.1-dev-8steps-lora.safetensors", "strength_model": 0.13, "strength_clip": 0.13, "model": ["81", 0], "clip": ["73", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "79": {"inputs": {"lora_name": "flux/FLUXpro1.1-FLUX-V0.2_aidmafluxpro1.1.safetensors", "strength_model": 0.9, "strength_clip": 0.9, "model": ["77", 0], "clip": ["77", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "81": {"inputs": {"model": ["74", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "83": {"inputs": {"pulid_file": "pulid_flux_v0.9.0.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "84": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "85": {"inputs": {"provider": "CPU"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "87": {"inputs": {"samples": ["88", 0], "vae": ["72", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "88": {"inputs": {"noise": ["92", 0], "guider": ["91", 0], "sampler": ["93", 0], "sigmas": ["90", 0], "latent_image": ["94", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "89": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "90": {"inputs": {"scheduler": "simple", "steps": 8, "denoise": 1, "model": ["109", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "91": {"inputs": {"model": ["109", 0], "conditioning": ["24", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "92": {"inputs": {"noise_seed": 770669818512401}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "93": {"inputs": {"detail_amount": 0.5, "start": 0.2, "end": 1, "bias": 0.5, "exponent": 0.5, "start_offset": 0, "end_offset": 0, "fade": 0, "smooth": true, "cfg_scale_override": 0, "sampler": ["89", 0]}, "class_type": "DetailDaemonSamplerNode", "_meta": {"title": "Detail <PERSON>"}}, "94": {"inputs": {"width": 768, "height": 1024, "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "97": {"inputs": {"anything": ["101", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "98": {"inputs": {"filename_prefix": "sticker/sticker", "images": ["101", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "99": {"inputs": {"detail_method": "PyMatting", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.01, "white_point": 0.99, "process_detail": true, "device": "cuda", "max_megapixels": 2, "image": ["87", 0]}, "class_type": "LayerMask: RmBgUltra V2", "_meta": {"title": "LayerMask: RmBgUltra V2"}}, "100": {"inputs": {"width": 768, "height": 1024, "red": 255, "green": 255, "blue": 255}, "class_type": "Image Blank", "_meta": {"title": "Image Blank"}}, "101": {"inputs": {"alpha_a": 1, "alpha_b": 1, "images_a_x": 0, "images_a_y": 0, "images_b_x": 0, "images_b_y": 0, "container_width": 0, "container_height": 0, "background": "images_b", "method": "pair", "images_a": ["99", 0], "images_b": ["100", 0]}, "class_type": "ImageComposite_Zho", "_meta": {"title": "ImageComposite_Zho"}}, "102": {"inputs": {"lora_name": "flux/Cartoon-monster-01.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["79", 0], "clip": ["79", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "107": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "model": ["76", 0], "pulid_flux": ["83", 0], "eva_clip": ["84", 0], "face_analysis": ["85", 0], "image": ["54", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "108": {"inputs": {"model": ["107", 0]}, "class_type": "FluxForwardOverrider", "_meta": {"title": "FluxForwardOverrider"}}, "109": {"inputs": {"rel_l1_thresh": 0.25, "model": ["108", 0]}, "class_type": "ApplyTeaCachePatch", "_meta": {"title": "ApplyTeaCachePatch"}}}