#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import uvicorn
from notebook.config import settings

if __name__ == '__main__':

    uvicorn.run("notebook.server:app",
                host=settings.server_host,
                port=settings.server_port,
                workers=4,
                reload=settings.debug,
                proxy_headers=True,
                forwarded_allow_ips="*",
                reload_dirs=["notebook"], log_level="info")

