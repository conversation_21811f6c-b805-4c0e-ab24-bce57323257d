import shutil
import os

import argparse

def parse_args():
    desc = "extact frame"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--indir', type=str, default='input file or path', help='[file or path]')
    parser.add_argument('--outdir', type=str, default='dataset/out', help='output data dir')
    return parser.parse_args()

def process_file(src_file:str, dst_dir:str):

    infile = os.path.basename(src_file)
    tailpath = infile.split(".")[0]
    output = os.path.join(dst_dir, tailpath)
    tailext = infile.split(".")[-1]
    tailext = tailext.lower()
    
    os.makedirs(output, exist_ok=True)
    if tailext == "mp4":
        # os.system(f"ffmpeg -i {src_file} -r 10 -qscale:v 2 -f image2 {output}\\%08d.png")
        # os.system(f"ffmpeg -i {src_file} -vf \"fps=24\" -qscale:v 2 -f image2 {output}\\%08d.png")
        #每秒2帧        
        os.system(f"ffmpeg -i {src_file} -vf \"fps=4\" -qscale:v 2 -f image2 {output}\\%08d.png")

    if tailext in ["png", "jpg", "jpeg"]:    
        shutil.copy(src_file, output)
        
def process_directory(indir:str, outdir:str):
       
    for dirpath, dirname, filenames in os.walk(indir):
        for filename in filenames:
            infile = os.path.join(dirpath, filename)
            process_file(infile, outdir)

def main():
    args = parse_args()
    os.makedirs(args.outdir, exist_ok=True)
    if os.path.isfile(args.indir):
        process_file(args.indir, args.outdir)
    else:
        process_directory(args.indir, args.outdir)
        
if __name__ == "__main__":
    main()



