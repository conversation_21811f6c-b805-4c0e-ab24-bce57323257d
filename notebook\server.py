#!/usr/bin/env python 
# -*- coding:utf-8 -*-

# import os
# os.environ["HTTP_PROXY"]= "http://127.0.0.1:1080"
# os.environ["HTTPS_PROXY"]= "http://127.0.0.1:1080"

import logging.config
from .utils import custom_logger, custom_middleware
from .utils import RespCode, AIException
import sys

logging.config.fileConfig("./notebook/config/log.ini", disable_existing_loggers=False)

from fastapi import FastAPI, Request
from fastapi.exceptions import HTTPException, RequestValidationError, WebSocketRequestValidationError
from fastapi.staticfiles import StaticFiles
from starlette.templating import Jinja2Templates
from starlette.exceptions import HTTPException as StarletteHTTPException

from fastapi.encoders import jsonable_encoder
from fastapi.responses import JSONResponse

from .config import settings
from .routers import api_router, custom_docs
from .views import views_router

# 实例化 fastapi 对象
app = FastAPI(docs_url=None, redoc_url=None,
              title=settings.project_title,
              description=settings.project_description,
              version=settings.project_version)

#自定义中间件
custom_middleware(app)

# 自定义 docs 界面
custom_docs(app)

@app.exception_handler(404)
async def internal_error_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=200,
        content=jsonable_encoder(RespCode.resp_err(RespCode.COMMON_ERROR, "Page Not Found")),
    )

@app.exception_handler(500)
async def internal_error_handler(request: Request, exc: Exception):
    return JSONResponse(
        status_code=200,
        content=jsonable_encoder(RespCode.resp_err(RespCode.COMMON_ERROR, "Internal Server Error")),
    )

@app.exception_handler(StarletteHTTPException)
async def starlette_exception_handler(request: Request, exc: StarletteHTTPException):
       return JSONResponse(
        status_code=200,
        content=jsonable_encoder(RespCode.resp_err(RespCode.COMMON_ERROR, str(exc))),
    )

@app.exception_handler(HTTPException)
async def http_exception_handler(request: Request, exc: HTTPException):
    return JSONResponse(
        status_code=200,
        content=jsonable_encoder(RespCode.resp_err(RespCode.COMMON_ERROR, str(exc))),
    )

@app.exception_handler(RequestValidationError)
async def validation_exception_handler(request: Request, exc: RequestValidationError):
    return JSONResponse(
        status_code=200,
        content=jsonable_encoder(RespCode.resp_err(RespCode.COMMON_ERROR, str(exc))),
    )

@app.exception_handler(WebSocketRequestValidationError)
async def websocket_exception_handler(request: Request, exc: WebSocketRequestValidationError):
    return JSONResponse(
        status_code=200,
        content=jsonable_encoder(RespCode.resp_err(RespCode.COMMON_ERROR, str(exc))),
    )

@app.exception_handler(AIException)
async def ai_exception_handler(request: Request, exc: AIException):
    return JSONResponse(
        status_code=200,
        content=jsonable_encoder(RespCode.resp_err(exc.code, exc.detail)),
    )

# 挂载 api 路由
app.include_router(api_router)
# 挂载 view 路由
app.include_router(views_router)

# 挂载静态文件目录
app.mount(settings.static_url_prefix, StaticFiles(directory=settings.static_dir), name="static")
# 用户上传的文件
app.mount(settings.media_url_prefix, StaticFiles(directory=settings.media_dir), name="media")

# 挂载 jinja2 模板引擎
app.state.jinja = Jinja2Templates(directory=settings.jinja2_templates_dir)

