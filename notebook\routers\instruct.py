#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from typing import Annotated, BinaryIO
from fastapi import APIRouter, UploadFile, Body, Form, File

import urllib.request
import random 
import oss2
import base64
import uuid 
import datetime
from fastapi.responses import StreamingResponse
import io
import numpy as np

from ..schemas.image import InstructInfo
from ..utils import RespCode
from ..config import settings
from ..utils import img2img as img2img
from ..utils import instructpix2pix as instructpix2pix
from ..utils import DecryptRoute

router = APIRouter(prefix='/instruct', tags=['图像编辑接口'])
router.route_class = DecryptRoute

@router.post("/img2img_oss",  summary="图生图-oss文件URL")
async def img2img_oss(form_data: InstructInfo = Body(...)):
    """
        图生图接口, 提供图片的url,返回生成图片的url
    """
    
    loc = form_data.loc
    prompt = form_data.prompt
    infer_steps = form_data.infer_steps 
    guidance_scale = form_data.guidance_scale

    with urllib.request.urlopen(loc) as response: 

        imgdata = instructpix2pix.instruct_img_from_bytes(response, prompt, infer_steps, guidance_scale)
        now = datetime.datetime.now()
        year_month_day = now.strftime("%Y/%m/%d")
        fprefix = str(uuid.uuid4())

        oss_path = "/".join([settings.OSS_IMAGE_PREFIX, year_month_day, f"{fprefix}.png"])
        bucket = oss2.Bucket(oss2.Auth(settings.OSS_SECRETKEY, settings.OSS_SECRETPASS), settings.OSS_ENDPOINT, settings.OSS_BUCKET)
        bucket.put_object(oss_path, base64.b64decode(imgdata))
        location = f"https://{settings.OSS_BUCKET}.{settings.OSS_ENDPOINT}/{settings.OSS_IMAGE_PREFIX}/{year_month_day}/{fprefix}.png"

        return RespCode.resp_ok({"location": location})

@router.post("/img2img_file",  summary="图生图-post上传文件")
async def img2img_file(upload_image: UploadFile = File(), 
    prompt: str = Form(default="change the sky red"),
    infer_steps: int = Form(default=10), 
    guidance_scale: int = Form(default=1)):
    """
        图生图接口, 上传文件, 返回生成图片的url
    """

    imgdata = instructpix2pix.instruct_img_from_bytes(upload_image.file, prompt, infer_steps,  guidance_scale)
    now = datetime.datetime.now()
    year_month_day = now.strftime("%Y/%m/%d")
    fprefix = str(uuid.uuid4())
    
    oss_path = "/".join([settings.OSS_IMAGE_PREFIX, year_month_day, f"{fprefix}.png"])
    bucket = oss2.Bucket(oss2.Auth(settings.OSS_SECRETKEY, settings.OSS_SECRETPASS), settings.OSS_ENDPOINT, settings.OSS_BUCKET)
    bucket.put_object(oss_path, base64.b64decode(imgdata))
    location = f"https://{settings.OSS_BUCKET}.{settings.OSS_ENDPOINT}/{settings.OSS_IMAGE_PREFIX}/{year_month_day}/{fprefix}.png"

    return RespCode.resp_ok({"location": location})

@router.post("/img2img_file_save",  summary="图生图-post上传文件，本地保存")
async def img2img_file_savev(upload_image: UploadFile = File(), 
    prompt: str = Form(default="change the sky red"),
    infer_steps: int = Form(default=10), 
    guidance_scale: int = Form(default=1)):
    """
        图生图接口, 上传文件, 返回生成下载图片
    """
 
    imgdata = instructpix2pix.instruct_img_from_bytes(upload_image.file, prompt, infer_steps,  guidance_scale)
    fprefix = str(uuid.uuid4())
    output = io.BytesIO()
    output.write(base64.b64decode(imgdata))
    output.seek(0)
    
    headers = {
        'Content-Disposition': 'attachment; filename="' + fprefix + '.png"'
    }

    return StreamingResponse(content=output, headers=headers)



