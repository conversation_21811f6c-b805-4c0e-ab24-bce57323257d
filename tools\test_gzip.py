#!/usr/bin/env python 
# -*- coding:utf-8 -*-
import uvicorn
import gzip
from typing import Callable, List

from fastapi import Body, Query, FastAPI
from fastapi.routing import APIRoute
from starlette.requests import Request
from starlette.responses import Response
from py4j.java_gateway import JavaGateway
from pydantic import BaseModel, Field, validator

class UserJwt(BaseModel):
    uid:str = Field(..., example = "123")
    cid:str = Field(..., example = "1")
    app:str = Field(..., example = "apct")
    ver:str = Field(..., example = "ver")

class CipherAgent(object):
    def __init__(self):
        self.gateway = JavaGateway()
        self.cipher = self.gateway.entry_point.getCipher()

    def get_cipher(self):
        return self.cipher


cipher_tool = CipherAgent().get_cipher()

class GzipRequest(Request):
    async def body(self) -> bytes:
        if not hasattr(self, "_body"):
            body = await super().body()
            body = cipher_tool.decrypt(body.decode(encoding="utf-8"))
            print(body)
            self._body = body.encode("utf-8")
        return self._body

class GzipRoute(APIRoute):
    def get_route_handler(self) -> Callable:
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            request = GzipRequest(request.scope, request.receive)
            return await original_route_handler(request)

        return custom_route_handler

app = FastAPI()
app.router.route_class = GzipRoute

@app.post("/sum")
async def sum_numbers(token: str = Query(...), data:UserJwt = Body()):
    print(data)
    return ""

if __name__ == "__main__":
    uvicorn.run(app)
