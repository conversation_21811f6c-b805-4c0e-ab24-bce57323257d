#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import sys, getopt
from PIL import Image
from pathlib import Path
import os

import argparse

def parse_args():
    desc = "prepare microscope dataset"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--indir', type=str, default='input file or path', help='[file or path]')
    parser.add_argument('--split_x', type=int, default=4, help='split current image to how much sub image in horizontal direction')
    parser.add_argument('--split_y', type=int, default=4, help='split current image to how much sub image in vertical direction')
    parser.add_argument('--outdir', type=str, default='dataset/out', help='output data dir')
    parser.add_argument('--img_size', type=int, default=512, help='output image size')
    return parser.parse_args()

def process_file(indir:str, outdir:str, split_x:int = 4,  split_y:int = 4, img_size:int = 512):

    img = Image.open(indir)
    img = img.resize((1024, 1024))

    width, height = img.size
    split_width = width // split_x
    split_height = height // split_y

    idx = 0
    for j in range(split_y):
        for i in range(split_x):
            box = (i*split_width, j*split_height, (i+1)*split_width, (j+1)*split_height)
            cropped_img = img.crop(box)
            resized_img = cropped_img.resize((img_size, img_size))
            idx = j * split_x + i
            outfile = f"{outdir}/{idx}.png"
            resized_img.save(outfile)

def process_directory(indir:str, outdir:str, split_x:int = 4, split_y:int = 4, img_size:int = 512):
       
    for dirpath, dirname, filenames in os.walk(indir):
        for filename in filenames:
            infile = os.path.join(dirpath, filename)
            process_file(infile, outdir, split_x, split_y, img_size)

def main():
    args = parse_args()
    
    os.makedirs(args.outdir, exist_ok=True)
    if os.path.isfile(args.indir):
        process_file(args.indir, args.outdir, args.split_x, args.split_y, args.img_size)
    else:
        process_directory(args.indir, args.outdir, args.split_x, args.split_y, args.img_size)
        
if __name__ == "__main__":
    main()
