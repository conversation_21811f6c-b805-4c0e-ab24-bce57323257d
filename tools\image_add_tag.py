#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import sys, getopt
from PIL import Image, PngImagePlugin, ImageDraw, ImageFont
import base64
from pathlib import Path
import os
import io 
import argparse
import piexif
import piexif.helper

def parse_args():
    desc = "encode image to base64 string format"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--img', type=str, default='image file', help='[image file to be encoded]')
    parser.add_argument('--out', type=str, default='base64 result to a file', help='[base64 str to be saved]')
    return parser.parse_args()

def add_visible_tags(base: Image):

    out = base.convert(mode="RGBA")    
    (width, height) = base.size 
    font = ImageFont.truetype("static/fonts/SIMLI.TTF", 20)
    draw = ImageDraw.Draw(out)
    position = (5, height - 25)
    text = "般芸聚合"

    left, top, right, bottom  = draw.textbbox(position, text, font=font)
    draw.rectangle((left -5, top -5, right + 5, bottom + 5), fill=(255,255,255,200))
    draw.text(position, text, font=font, fill=(255,255,255,100))

    return out

def process_file(image:str, out:str):

    img = Image.open(image)
    img = add_visible_tags(img)
    img.save(out)

def main():
    args = parse_args()
    process_file(args.img, args.out)

if __name__ == "__main__":
    #python .\tools\image_add_tag.py --img D:\train-material\a.png --out D:\train-material\b.png
    main()
