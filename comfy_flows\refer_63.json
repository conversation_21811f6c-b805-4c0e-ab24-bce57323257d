{"1": {"inputs": {"vae_name": "flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "12": {"inputs": {"image": "12.jpg"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "16": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5-v1_1-xxl-encoder-Q5_K_S.gguf", "type": "flux"}, "class_type": "DualCLIPLoaderGGUF", "_meta": {"title": "DualCLIPLoader (GGUF)"}}, "17": {"inputs": {"unet_name": "flux1-kontext-dev-Q5_K_M.gguf", "dequant_dtype": "bfloat16", "patch_dtype": "bfloat16", "patch_on_device": false}, "class_type": "UnetLoaderGGUFAdvanced", "_meta": {"title": "Unet Loader (GGUF/Advanced)"}}, "18": {"inputs": {"width": ["62", 0], "height": ["62", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": 0, "image": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "62": {"inputs": {"width": 1024, "height": 1024, "aspect_ratio": "3:4 portrait 896x1152", "swap_dimensions": "Off", "upscale_factor": 1, "batch_size": 1}, "class_type": "CR SDXL Aspect Ratio", "_meta": {"title": "AspectRatio"}}, "197": {"inputs": {"from_translate": "chinese simplified", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "MyMemoryTranslator [free]", "text": "", "Show proxy": "proxy_hide", "Show authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "UserPrompt"}}, "205": {"inputs": {"samples": ["313", 0], "vae": ["1", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "214": {"inputs": {"filename_prefix": "refer/kontext", "images": ["205", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "215": {"inputs": {"anything": ["205", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "271": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "Transforming to Conan art style,  while keeping the same person.", "text_b": ["197", 0], "text_c": "", "speak_and_recognation": {"__value__": [false, true]}, "result": "Transforming to Conan art style, while keeping the same person."}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "308": {"inputs": {"conditioning": ["312", 0], "latent": ["309", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "309": {"inputs": {"pixels": ["311", 0], "vae": ["1", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "310": {"inputs": {"guidance": 2.5, "conditioning": ["308", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "311": {"inputs": {"image": ["18", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "312": {"inputs": {"text": ["271", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["16", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "313": {"inputs": {"seed": 836218681883698, "steps": 8, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["315", 0], "positive": ["310", 0], "negative": ["314", 0], "latent_image": ["309", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "314": {"inputs": {"conditioning": ["310", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "315": {"inputs": {"model_type": "flux", "rel_l1_thresh": 0.4, "start_percent": 0, "end_percent": 1, "cache_device": "cuda", "model": ["317", 0]}, "class_type": "TeaCache", "_meta": {"title": "TeaCache"}}, "317": {"inputs": {"lora_name": "flux/alimama-Flux.1-8steps-Turbo-Alpha.safetensors", "strength_model": 1, "model": ["17", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}}