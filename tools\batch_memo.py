import shutil
import os

import argparse

def parse_args():
    desc = "batch memo"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--indir', type=str, default='input file or path', help='[file or path]')
    parser.add_argument('--memo', type=str, default='1girl', help='image blip memo')
    return parser.parse_args()

def process_file(src_file:str, memo:str):

    print(src_file)
    infile = os.path.basename(src_file)
    tailext = infile.split(".")[-1]

    if tailext.lower() == "txt":
        with open(src_file, "w") as f:
            f.write(memo)
    else:
        dstfile = src_file.replace(tailext, ".txt")
        with open(dstfile, "w") as f:
            f.write(memo)
        
def process_directory(indir:str, memo:str):
       
    for dirpath, dirname, filenames in os.walk(indir):
        for filename in filenames:
            infile = os.path.join(dirpath, filename)
            process_file(infile, memo)

def main():
    
    args = parse_args()
    if os.path.isfile(args.indir):
        process_file(args.indir, args.memo)
    else:
        process_directory(args.indir, args.memo)
        
if __name__ == "__main__":
    main()



