#!/usr/bin/env python 
# -*- coding:utf-8 -*-

# 调用接口 
# sd 文生图
# lora  
# crontol net tile | tile difusion |ultimate SD upscale 
# target resolution 1080,1920 
#

import re
import argparse
import os

def gen_image(prompt, args):

    base_model = ["notebook/model/model/chilloutmix_NiPrunedFp32Fix.safetensors", 
    "notebook/model/model/majicmixRealistic_v5.safetensors", 
    "notebook/model/model/realdosmix_.safetensors", 
    "notebook/model/model/realisticVisionV20_v20NoVAE.safetensors"]
    lora_model = ["notebook/model/lora/character/BeautyNwsjMajic2-01.safetensors", 
    "notebook/model/lora/character/koreaface15.safetensors",
    "notebook/model/lora/character/lora_azhu.safetensors",
    "notebook/model/lora/character/lora_linleyi.safetensors",
    "notebook/model/lora/character/lora_wangxinyi.safetensors"]

    for model in base_model:
        for lora in lora_model:
            base_cmd=f"""
                python notebook\gen_img_diffusers.py 
                --network_module="networks.lora" 
                --network_weight="{lora}" 
                --network_mul=0.7 
                --ckpt="{model}" 
                --vae="notebook/model/vae/vae-ft-ema-560000-ema-pruned.ckpt" 
                --outdir="{args.outdir}" 
                --xformers 
                --fp16 
                --W=512 
                --H=512 
                --scale=7 
                --sampler="k_dpm_2_a" 
                --steps={args.steps} 
                --max_embeddings_multiples=3 
                --batch_size={args.batch_size} 
                --images_per_prompt=20 
                --clip_skip=1 
                --textual_inversion_embeddings="notebook/model/embeddings/EasyNegative.safetensors"
                --prompt="%s --n EasyNegative, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry" 
            """ 
            cmd = base_cmd % (prompt)
            cmd = re.sub("\\s+", " ", cmd)
            print(cmd)
            os.system(cmd)
            
def main(args):

    for p, dirs, files in os.walk(args.indir):
        for name in files:
            if name.endswith(".txt"):
                print(os.path.join(p, name))
                prompt = open(os.path.join(p, name)).read().strip()
                gen_image(prompt, args)


def setup_parser() -> argparse.ArgumentParser:

    parser = argparse.ArgumentParser()
    parser.add_argument("--indir", type=str, default=None, help="if specified, load prompts from this file / 指定時はプロンプトをファイルから読み込む" )
    parser.add_argument("--outdir", type=str, default="outputs", help="dir to write results to")
    parser.add_argument("--batch_size", type=int, default=1, help="batch size / バッチサイズ")
    parser.add_argument("--steps", type=int, default=50, help="number of ddim sampling steps / サンプリングステップ数")
    return parser

if __name__ == "__main__":
    parser = setup_parser()

    args = parser.parse_args()
    main(args)


       
