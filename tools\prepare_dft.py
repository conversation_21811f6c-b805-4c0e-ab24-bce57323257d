#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import sys, getopt
from PIL import Image
from pathlib import Path
import os
import argparse
import requests
from datetime import datetime, timedelta
import pymysql
from pymysql.cursors import DictCursor
import hashlib
import traceback
import shutil
import re

mail_to="<EMAIL>,<EMAIL>,<EMAIL>"
ai_api="http://**********:8765/api/v1/styles"


from parse_log import encode_pil_to_base64, get_resolution, resize_img

def get_now():
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def send_mail(msg:str):
    print(msg)
    os.system(f"/root/miniconda3/bin/python /root/bin/mail.py {mail_to} 'AI默认图准备错误' 'Dear,\n {msg}'") 

def parse_args():
    desc = "prepare microscope dataset"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--data_date', type=str, default='', help='current datetime')
    return parser.parse_args()

def md5(dt):
    md5 = hashlib.md5()
    md5.update(dt)
    md5text = md5.hexdigest().lower()
    return md5text

def extract_file_ext(response):
    # Extract filename from Content-Disposition header
    content_disposition = response.headers.get("Content-Disposition")
    filename = None
    if content_disposition:
        match = re.search(r'filename\*?=(?:UTF-8\'\')?["\']?([^"\';]+)', content_disposition)
        if match:
            filename = match.group(1)
    file_ext = "png"
    if filename:
        file_ext = filename.split(".")[-1]
    
    return file_ext

def process_ai_request(api_url:str, img_path:str, params:dict):
      
    files = {'srcImage': open(img_path, 'rb').read()}
    print(api_url, params)
    res = requests.post(api_url, data=params, files=files,  timeout=1200)

    if res.status_code != 200:
        return None
    
    file_ext = extract_file_ext(res)
    img_ext = img_path.split(".")[-1]
    
    local_img_path = img_path.replace(f".{img_ext}", f"_result.{file_ext}")
    print(local_img_path)

    with open(local_img_path, 'wb') as f:
        f.write(res.content)
    return local_img_path

def get_conn(host="rm-2zez7x3o3wp4096bm.mysql.rds.aliyuncs.com", port=3306, user="crius", passwd="Crius!@##@!", database="fuxi"):
    try:
        conn = pymysql.connect(host=host, port=port, user=user, passwd=passwd, db=database, charset="utf8", autocommit=True,  cursorclass=DictCursor)
        return conn
    except Exception as e:
        #exstr = traceback.format_exc()
        #print(exstr)
        return None

def get_conn_microlens(host="rm-2ze2h0oa62u0cx5w2.mysql.rds.aliyuncs.com", port=3306, user="microlens", passwd="m7i*C#r4o9", database="microlens"):
    try:
        conn = pymysql.connect(host=host, port=port, user=user, passwd=passwd, db=database, charset="utf8", autocommit=True,  cursorclass=DictCursor)
        return conn
    except Exception as e:
        #exstr = traceback.format_exc()
        #print(exstr)
        return None

def save_img_to_db(mat_id, img_src, img_result):
    try:
        conn = get_conn_microlens()
        if not conn:
            return False

        # Load and resize images
        src_img = Image.open(img_src)
        result_img = Image.open(img_result)
        
        src_img = resize_img(src_img, 128, 128)
        result_img = resize_img(result_img, 128, 128)

        # Encode to base64
        src_b64 = encode_pil_to_base64(src_img, 'png')
        result_b64 = encode_pil_to_base64(result_img, 'png')

        # Save to database
        cursor = conn.cursor()
        sql = """INSERT INTO stg_ipct_material_default 
                 (mat_id, img_src, img_result) 
                 VALUES (%s, %s, %s)"""
        cursor.execute(sql, (mat_id, src_b64, result_b64))
        cursor.close()
        conn.close()

    except Exception as e:
        print(f"Error saving images to db: {str(e)}")

def save_img_to_comfyui(img_src):
    try:
        shutil.copy(img_src, "/root/ComfyUI/input/template")
    except Exception as e:
        print(f"Error saving images to ComfyUI: {str(e)}")
        
def get_image_factor(factor:str):
    
    ratioDict = {
        "1:1":"1:1 square 1024x1024",
        "3:4":"3:4 portrait 896x1152",
        "5:8":"5:8 portrait 832x1216",
        "9:16":"9:16 portrait 768x1344",
        "9:21":"9:21 portrait 640x1536",
        "4:3":"4:3 landscape 1152x896",
        "3:2":"3:2 landscape 1216x832",
        "16:9":"16:9 landscape 1344x768",
        "21:9":"21:9 landscape 1536x640"   
    }
    
    if factor in ratioDict:
        return ratioDict[factor]
    else:
        return "1:1 square 1024x1024"
    
def get_image_detail(detail:str):
    
    dnum = int(detail)  
    if dnum < 1 or dnum > 10:
        dnum = 5
    return detail

def get_image_style(style_id:str):

    snum = int(style_id)
    if snum == 1:
        return "comics"
    elif snum == 2:
        return "cyberpunk"
    elif snum == 3:
        return "chinese"
    elif snum == 4:
        return "anime"
    elif snum == 5:
        return "cartoon"
    elif snum == 6:
        return "romantic"
    elif snum == 7:
        return "portrait"
    
    return "portrait"

def get_presets_style(style_id: str):

    snum = int(style_id)
    if snum == 1:
        return "情境深度融合"
    elif snum == 2:
        return "无痕融合"
    elif snum == 3:
        return "场景传送"
    elif snum == 4:
        return "移动镜头"
    elif snum == 5:
        return "重新布光"
    elif snum == 6:
        return "专业产品图"
    elif snum == 7:
        return "画面缩放"
    elif snum == 8:
        return "图像上色"
    elif snum == 9:
        return "电影海报"
    elif snum == 10:
        return "卡通漫画化"
    elif snum == 11:
        return "移除文字"
    elif snum == 12:
        return "更换发型"
    elif snum == 13:
        return "肌肉猛男化"
    elif snum == 14:
        return "清空家具"
    elif snum == 15:
        return "室内设计"
    elif snum == 16:
        return "季节变换"
    elif snum == 17:
        return "时光旅人"
    elif snum == 18:
        return "材质置换"
    elif snum == 19:
        return "微缩世界"
    elif snum == 20:
        return "幻想领域"
    elif snum == 21:
        return "衣帽改造"
    elif snum == 22:
        return "艺术风格模仿"
    elif snum == 23:
        return "蓝图视角"
    elif snum == 24:
        return "添加倒影"
    elif snum == 25:
        return "像素艺术"
    elif snum == 26:
        return "铅笔手绘"
    elif snum == 27:
        return "油画风格"
    else:
        return "情境深度融合"

def process_variant_request(template_code:str, template_id:str,  local_img_path:str, app_ver:str = "1.0.0", page:str="ai"):
    
    try: 
        data = {"style":template_code, "templateId":template_id, "appVer":app_ver, "page":page}
        ai_img_path = process_ai_request(ai_api + "/variant", local_img_path, data)
        return ai_img_path
    except Exception as e:
        expr = traceback.format_exc()
        print(expr)

def process_edit_request(template_code:str, template_id:str,  local_img_path:str, style_id:str, app_ver:str = "1.0.0", page:str="ai"):
    
    try: 
    
        factor = get_image_factor("3:4")
        detail = get_image_detail(5)
        style = get_image_style(style_id)
        seed = -1 
        repaint = 0.5
        
        data = {"model": template_code, "templateId": template_id, 
                "appVer": app_ver, "page": page, "prompt":"", 
                "factor": factor, "detail": detail, "style": style,
                "seed": seed, "repaint": repaint}
        
        ai_img_path = process_ai_request(ai_api + "/edit", local_img_path, data)
        
        return ai_img_path 
    except Exception as e:
        expr = traceback.format_exc()
        print(get_now())
        print(expr)   

def process_kontext_request(template_code:str, template_id:str,  local_img_path:str, style_id:str, app_ver:str = "1.0.0", page:str="ai"):

    try:

        factor = get_image_factor("3:4")
        detail = get_image_detail(5)
        style = get_presets_style(style_id)
        seed = -1 
        repaint = 0.5
        
        data = {"model": template_code, "templateId": template_id, 
                "appVer": app_ver, "page": page, "prompt":"", 
                "factor": factor, "detail": detail, "style": style,
                "seed": seed, "repaint": repaint}
        
        ai_img_path = process_ai_request(ai_api + "/kontext", local_img_path, data)
        
        return ai_img_path 
    except Exception as e:
        expr = traceback.format_exc()
        print(get_now())
        print(expr)   

def process_refer_request(template_code:str, template_id:str,  local_img_path:str, style_id:str, mat_id:str,  app_ver:str = "1.0.0", page:str="ai"):
    
    try: 
    
        factor = get_image_factor("3:4")
        detail = get_image_detail(5)
        seed = -1 
        repaint = 1.0
        
        if style_id in ["14", "15"]:
            style_id = "13"

        data = {"model": template_code, "templateId": template_id, 
                "appVer": app_ver, "page": page, "prompt":"", 
                "factor": factor, "detail": detail, "style": mat_id,
                "seed": seed, "repaint": repaint, "template": style_id}
        
        ai_img_path = process_ai_request(ai_api + "/refimg", local_img_path, data)
        
        return ai_img_path 
    except Exception as e:
        expr = traceback.format_exc()
        print(get_now())
        print(expr)   

def process_draw_request(template_code:str, template_id:str,  local_img_path:str, style_id:str, mat_id:str,  app_ver:str = "1.0.0", page:str="ai"):
    
    try: 
    
        factor = get_image_factor("3:4")
        detail = get_image_detail(5)
        seed = -1 
        repaint = 1.0

        data = {"model": template_code, "templateId": template_id, 
                "appVer": app_ver, "page": page, "prompt":"", 
                "factor": factor, "detail": detail, "style": mat_id,
                "seed": seed, "repaint": repaint, "template": style_id}
        
        ai_img_path = process_ai_request(ai_api + "/refdraw", local_img_path, data)
        
        return ai_img_path 
    except Exception as e:
        expr = traceback.format_exc()
        print(get_now())
        print(expr) 
          
def process_wan_request(template_code:str, template_id:str,  local_img_path:str, style_id:str, mat_id:str,  app_ver:str = "1.0.0", page:str="ai"):
    
    try: 
    
        factor = get_image_factor("3:4")
        detail = get_image_detail(5)
        seed = -1 
        repaint = 1.0

        data = {"model": template_code, "templateId": template_id, 
                "appVer": app_ver, "page": page, "prompt":"", 
                "factor": factor, "detail": detail, "style": mat_id,
                "seed": seed, "repaint": repaint, "template": style_id}
        
        ai_img_path = process_ai_request(ai_api + "/refwan", local_img_path, data)
        
        return ai_img_path 
    except Exception as e:
        expr = traceback.format_exc()
        print(get_now())
        print(expr)  
        
def process_ai_record(style_id:str, template_id:str, img_path:str, mat_id:str):
    
    if style_id == 8:
        process_variant_request("sticker", template_id, img_path)
    elif style_id >= 10 and style_id <= 18:
        process_refer_request("refer", template_id, img_path, style_id, mat_id)
    elif style_id == 60:
        process_draw_request("refdraw", template_id, img_path, style_id, mat_id)
    elif (style_id >= 19 and style_id <= 32) or (style_id >= 53 and style_id <= 55) \
        or (style_id >= 57 and style_id <= 58) or (style_id >= 62 and style_id <= 91):
        process_kontext_request("kontext", template_id, img_path, style_id, mat_id)
    elif (style_id >= 33 and style_id <= 52) or style_id == 56 or style_id == 59 or style_id == 61:
        process_wan_request("refwan", template_id, img_path, style_id, mat_id)
    else:
        process_edit_request("edit", template_id, img_path, style_id)
            
def process_default_image(data_date:str):
    
    img_path = f"./dataset/dft_img"
    Path(img_path).mkdir(parents=True, exist_ok=True)
    
    conn = get_conn()
    if conn is None:
        send_mail(f"{get_now()} 获取数据库连接失败.请及时关注.")
        return
    
    cur = conn.cursor()
    sql = "select id, cover_img_url as img_url, '121' as template_id, style_id from isplimg_material_delivery WHERE update_time >= %s;"
    cur.execute(sql, (data_date))
    result = cur.fetchall()
    cur.close()
    conn.close()
    
    for entity in result:
        mat_id = entity["id"]
        img_url = entity["img_url"]
        template_id = entity["template_id"]
        style_id = entity["style_id"]
        
        if not img_url:
            continue
        print(f"{get_now()} process {mat_id} {img_url}")
        
        img_name = img_url.split("/")[-1]
        img_src = f"{img_path}/{img_name}"
        res = requests.get(img_url.replace("cdn", "source"))
        if res.status_code != 200:
            send_mail(f"{get_now()} 获取默认图片 {img_url} 失败. 请及时关注.")
            continue
        with open(img_src, "wb") as f:
            f.write(res.content)
        img_out = f"{img_path}/{mat_id}.png"
        
        im = Image.open(img_src)
        if im.format == 'GIF':
            num_frames = im.n_frames - 1
            im.seek(num_frames)
            middle_frame = im.copy()
            middle_frame.save(img_out)
            middle_frame.close()
            im.close()
        else:
            im.save(img_out)
            im.close()

        os.remove(img_src)
        process_ai_record(style_id, template_id, img_out, mat_id)
        save_img_to_db(mat_id, f"{img_path}/{mat_id}.png", f"{img_path}/{mat_id}_result.png")
        save_img_to_comfyui(f"{img_path}/{mat_id}.png",)

def main():
    args = parse_args()
    data_date = args.data_date
    if data_date == "":
        data_date = (datetime.now() - timedelta(hours=1)).strftime("%Y-%m-%d %H:00:00")
    print(data_date)

    process_default_image(data_date)

if __name__ == "__main__":
    main()

