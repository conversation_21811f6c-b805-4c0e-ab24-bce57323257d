import shutil
import os
import csv
import argparse
from PIL import Image 
import base64
import io 
import re 

def parse_args():
    desc = "batch memo"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--indir', type=str, default='input file or path', help='[file or path]')
    parser.add_argument("--outdir", type=str, default='output file path', help='output directory')
    return parser.parse_args()

def process_file(src_file:str, outdir:str):

    os.makedirs(outdir, exist_ok=True)
    filepath, filename = os.path.split(src_file)
    i = 0
    if "img" in filename:
        with open(src_file, "r") as fp:
            for line in fp.readlines():
                row = line.strip().split(",")
                if len(row) < 8:
                    continue
                prompt = row[-3]
                if len(prompt) > 100:
                    continue
                in_img = Image.open(io.BytesIO(base64.b64decode(row[-1])))
                out_img = Image.open(io.BytesIO(base64.b64decode(row[-2])))
                prompt = re.sub("\s+", "_", prompt)
                outfile = filename.replace(".log", "") + "_" + prompt + "_" + str(i)
                in_img.save(os.path.join(outdir, outfile + "_in.png"))
                out_img.save(os.path.join(outdir, outfile + "_out.png"))
                i += 1
                    
def process_directory(indir:str, outdir:str):
       
    for dirpath, dirname, filenames in os.walk(indir):
        for filename in filenames:
            infile = os.path.join(dirpath, filename)
            process_file(infile, outdir)

def main():
    
    args = parse_args()
    if os.path.isfile(args.indir):
        process_file(args.indir, args.outdir)
    else:
        process_directory(args.indir, args.outdir)
        
if __name__ == "__main__":
    main()



