#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import redis 
from ..config import get_settings

class RedisStock(object):

    blackWordPrefix = "word:black:"

    def __init__(self):
        
        settings = get_settings()
        self.client = redis.Redis(settings.redis_host, int(settings.redis_port), password=settings.redis_pwd, db=int(settings.redis_db))
        self.word_count = self.client.scard(self.blackWordPrefix)
        self.word_set = self.client.smembers(self.blackWordPrefix)
    
    def update_wordset(self):

        word_count = self.client.scard(self.blackWordPrefix)
        if word_count != self.word_count:
            self.word_count = word_count
            self.word_set = self.client.smembers(self.blackWordPrefix)

    def stripSensitive(self, sentence:str):

        self.update_wordset()
        words = sentence.split()
        out = []
        for word in words:
            if not bytes(word, encoding="utf-8") in self.word_set:
                out.append(word)
        return " ".join(out)

redisStock = RedisStock()
