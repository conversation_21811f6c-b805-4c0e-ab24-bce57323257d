{"3": {"inputs": {"seed": 893897537346733, "steps": 21, "cfg": 6, "sampler_name": "uni_pc", "scheduler": "simple", "denoise": 1, "model": ["58", 0], "positive": ["50", 0], "negative": ["50", 1], "latent_image": ["50", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "6": {"inputs": {"text": ["60", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["58", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Positive Prompt)"}}, "7": {"inputs": {"text": "Overexposure, static, blurred details, subtitles, paintings, pictures, still, overall gray, worst quality, low quality, JPEG compression residue, ugly, mutilated, redundant fingers, poorly painted hands, poorly painted faces, deformed, disfigured, deformed limbs, fused fingers, cluttered background, three legs, a lot of people in the background, upside down", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["58", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Negative Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["39", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "28": {"inputs": {"filename_prefix": "wan/anime", "fps": 8, "lossless": false, "quality": 90, "method": "default", "images": ["8", 0]}, "class_type": "SaveAnimatedWEBP", "_meta": {"title": "SaveAnimatedWEBP"}}, "38": {"inputs": {"clip_name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "type": "wan", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "39": {"inputs": {"vae_name": "wan_2.1_vae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "49": {"inputs": {"clip_name": "clip_vision_h.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "50": {"inputs": {"width": ["63", 0], "height": ["63", 1], "length": 49, "batch_size": 1, "positive": ["6", 0], "negative": ["7", 0], "vae": ["39", 0], "clip_vision_output": ["51", 0], "start_image": ["52", 0]}, "class_type": "WanImageToVideo", "_meta": {"title": "WanImageToVideo"}}, "51": {"inputs": {"crop": "center", "clip_vision": ["49", 0], "image": ["52", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "52": {"inputs": {"image": "微信图片_20250316120351.jpg"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "54": {"inputs": {"unet_name": "wan2.1/wan2.1-i2v-14b-480p-Q4_K_S.gguf"}, "class_type": "UnetLoaderGGUF", "_meta": {"title": "Unet Loader (GGUF)"}}, "55": {"inputs": {"model_type": "wan2.1_i2v_480p_14B", "rel_l1_thresh": 0.4, "start_percent": 0.3, "end_percent": 1, "model": ["54", 0]}, "class_type": "TeaCache", "_meta": {"title": "TeaCache"}}, "56": {"inputs": {"anything": ["8", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "58": {"inputs": {"lora_name": "wan2.1/squish_18.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["55", 0], "clip": ["38", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "59": {"inputs": {"model": "promptgen_base_v2.0", "folder_path": "Path to your image folder", "caption_method": "simple", "max_new_tokens": 1024, "num_beams": 4, "random_prompt": "never", "prefix_caption": "", "suffix_caption": "", "replace_tags": "", "speak_and_recognation": {"__value__": [false, true]}, "images": ["52", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Tagger", "_meta": {"title": "🐾MiaoshouAI Tagger"}}, "60": {"inputs": {"action": "replace", "tidy_tags": "yes", "text_a": "In the video, [target]. It is held in a person’s hands. The person then presses on it, causing a sq41sh squish effect. The person keeps pressing down on it, further showing the sq41sh squish effect.\n", "text_b": "[target]", "text_c": ["59", 2], "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "62": {"inputs": {"size": 512, "interpolation_mode": "bicubic", "image": ["52", 0]}, "class_type": "JWImageResizeByLongerSide", "_meta": {"title": "Image Resize by <PERSON><PERSON>"}}, "63": {"inputs": {"image": ["62", 0]}, "class_type": "GetImageSize", "_meta": {"title": "GetImageSize"}}}