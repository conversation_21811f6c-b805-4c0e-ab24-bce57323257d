#!/usr/bin/env python 
# -*- coding:utf-8 -*-
# 调用接口 
# sd 文生图
# lora  
# crontol net tile | tile difusion |ultimate SD upscale 
# target resolution 1080,1920 
#

import requests
import base64
import os
import re 
import json
import random
import argparse

base_url="http://127.0.0.1:7860"
common_negative="EasyNegative, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry"

def url_txt2img():
    return f"{base_url}/sdapi/v1/txt2img"

def url_img2img():
    return f"{base_url}/sdapi/v1/img2img"

def save_img(images:list[str], args: dict, outdir: str):
    
    for i, img in enumerate(images):
        
        prompt = args["prompt"] 
        seed = args["seed"]
        prompt = re.sub(r'[^\w]+', "_", prompt)
        prompt = re.sub(r'\s+', "_", prompt)
        prompt = "_".join(prompt.split("_")[:10])
        prefix = "%06d" % (i)
        outfile = os.path.join(outdir, f"{prefix}_{prompt}.png")
        while os.path.exists(outfile):
            i +=  1
            prefix = "%06d" % (i)
            outfile = os.path.join(outdir, f"{prefix}_{prompt}.png")

        with open(outfile, "wb") as f:
            f.write(base64.b64decode(img))
            
def simple_txt2img_request():
    return {
        "batch_size": 4,
        "cfg_scale": 7,
        "denoising_strength": 0,
        "enable_hr": False,
        "eta": 0,
        "firstphase_height": 0,
        "firstphase_width": 0,
        "height": 512,
        "width": 512,
        "n_iter": 1,
        "negative_prompt": "",
        "prompt": "example prompt",
        "restore_faces": True,
        "s_churn": 0,
        "s_noise": 1,
        "s_tmax": 0,
        "s_tmin": 0,
        "sampler_index": "DPM++ 2M SDE Karras",
        "seed": -1,
        "seed_resize_from_h": -1,
        "seed_resize_from_w": -1,
        "steps": 25,
        "styles": [],
        "subseed": -1,
        "subseed_strength": 0,
        "tiling": False,
    }

def txt2img_with_complex_prompt(prompt:str, negative_prompt:str, outdir:str):
    params = simple_txt2img_request()
    params["prompt"] = prompt
    params["negative_prompt"] = negative_prompt

    resp = requests.post(url_txt2img(), json=params)
    resp_json = resp.json()
    save_img(resp_json["images"], resp_json["parameters"], outdir)

def sample_prompt(prompt:str):
    
    prompt_list = prompt.split()
    sample_size = len(prompt_list) // 5
    sampled_prompt = random.sample(prompt_list, len(prompt_list) - sample_size)
    return ' '.join(sampled_prompt)
    
def gen_image(prompt_prefix:str, prompt:str, outdir:str, lora_model:list[str]):

    for lora in lora_model:
        prompt = sample_prompt(prompt) 
        prompt = f"{prompt_prefix}, {prompt}, (ultra high res),(highly detailed), {lora}"
        lora_name = lora.split(":")[1]
        tempdir = os.path.join(outdir, lora_name)
        os.makedirs(tempdir, exist_ok=True)
        txt2img_with_complex_prompt(prompt, common_negative, tempdir)

def main(args):

    lora_list = args.lora_list
    if lora_list == None:
        lora_model = [""]
    else:
        lora_model = lora_list.split(";")
    prompt_prefix = args.prompt_prefix 

    dirs = args.indir.split(",")
    for path_dir in dirs:
        if os.path.isdir(path_dir):
            for p, dirs, files in os.walk(path_dir):
                for name in files:
                    if name.endswith(".txt"):
                        print(os.path.join(p, name))
                        prompt = open(os.path.join(p, name), encoding="utf-8").read().strip()
                        outtail = p.split("\\")
                        outdir = os.path.join(args.outdir, outtail[-1])
                        gen_image(prompt_prefix, prompt, outdir, lora_model)
        else:
            with open(path_dir, "r", encoding="utf-8") as f:
                for l in f.readlines():
                    p = l.split("~~~")
                    prompt = p[0]
                    gen_image(prompt_prefix, prompt, args.outdir, lora_model)

def setup_parser() -> argparse.ArgumentParser:

    parser = argparse.ArgumentParser()
    parser.add_argument("--indir", type=str, default=None, help="if specified, load prompts from this file" )
    parser.add_argument("--outdir", type=str, default="outputs", help="dir to write results to")
    parser.add_argument("--prompt_prefix", type=str, default=None, help="prepend to prompt")
    parser.add_argument("--lora_list", type=str, default=None, help="lora group list, seperate by semicolon(;)")
    return parser

if __name__ == "__main__":
    parser = setup_parser()

    args = parser.parse_args()
    main(args)


