#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from py4j.java_gateway import JavaGateway

class CipherAgent(object):
    def __init__(self):
        self.gateway = JavaGateway()
        self.cipher = self.gateway.entry_point.getCipher()

    def get_cipher(self):
        return self.cipher

if __name__ == "__main__":
    text = "今天天气不错，挺风和日丽的。"
    cipher = CipherAgent().get_cipher()
    encrypted_text = cipher.encrypt(text)
    print(encrypted_text)
    decrypted_text = cipher.decrypt(encrypted_text)
    print(decrypted_text)

    text = '{"uid": "123","cid": "1","app": "apct","ver": "ver"}'
    encrypted_text = cipher.encrypt(text)
    print(encrypted_text)
    decrypted_text = cipher.decrypt(encrypted_text)
    #decrypted_text = cipher.decrypt(bytes(encrypted_text, encoding="utf-8"))
    print(decrypted_text)
