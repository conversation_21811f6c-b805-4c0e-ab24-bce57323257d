{"4": {"inputs": {"ckpt_name": "majicmixRealistic_v7.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "10": {"inputs": {"vae_name": "vae-ft-mse-840000-ema-pruned.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "12": {"inputs": {"stop_at_clip_layer": -2, "clip": ["58", 1]}, "class_type": "CLIPSetLastLayer", "_meta": {"title": "CLIP Set Last Layer"}}, "13": {"inputs": {"context_length": ["76", 0], "context_overlap": 4, "fuse_method": "pyramid", "use_on_equal_length": false, "start_percent": 0, "guarantee_steps": 1}, "class_type": "ADE_StandardStaticContextOptions", "_meta": {"title": "Context Options◆Standard Static 🎭🅐🅓"}}, "18": {"inputs": {"text": "(nsfw:1.2),(worst quality, low quality:1.4),(depth of field, blurry:1.2),(greyscale, monochrome:1.1),3D face,cropped,lowres,text,(nsfw:1.3),(worst quality:2),(low quality:2),(normal quality:2),normal quality,((grayscale)),skin spots,acnes,skin blemishes,age spot,(ugly:1.331),(duplicate:1.331),(morbid:1.21),(mutilated:1.21),(tranny:1.331),mutated hands,(poorly drawn hands:1.5),blurry,(bad anatomy:1.21),(bad proportions:1.331),extra limbs,(disfigured:1.331),(missing arms:1.331),(extra legs:1.331),(fused fingers:1.61051),(too many fingers:1.61051),(unclear eyes:1.331),lowers,bad hands,missing fingers,extra digit,bad hands,missing fingers,(((extra arms and legs))),", "clip": ["12", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "19": {"inputs": {"seed": 581716918942906, "steps": 8, "cfg": 1.5, "sampler_name": "lcm", "scheduler": "karras", "denoise": 1, "model": ["142", 0], "positive": ["66", 0], "negative": ["18", 0], "latent_image": ["69", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "23": {"inputs": {"samples": ["19", 0], "vae": ["10", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "25": {"inputs": {"frame_rate": 16, "loop_count": 0, "filename_prefix": "20240528/115639_581716918942906", "format": "image/gif", "pingpong": true, "save_output": true, "images": ["28", 0]}, "class_type": "VHS_VideoCombine", "_meta": {"title": "Video Combine 🎥🅥🅗🅢"}}, "28": {"inputs": {"ckpt_name": "film_net_fp32.pt", "clear_cache_after_n_frames": 6, "multiplier": 4, "frames": ["94", 0]}, "class_type": "FILM VFI", "_meta": {"title": "FILM VFI"}}, "33": {"inputs": {"image": "e5118439-2969-4006-9061-c2f4e0fc24e9.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "36": {"inputs": {"model": "wd-vit-tagger-v3", "threshold": 0.55, "character_threshold": 0.85, "replace_underscore": true, "trailing_comma": false, "exclude_tags": "", "image": ["95", 0]}, "class_type": "WD14Tagger|pysssss", "_meta": {"title": "WD14 Tagger 🐍"}}, "37": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "ultra realistic 8k cg,masterpiece,professional artwork,famous artwork,cinematic lighting,cinematic bloom,", "text_b": ["36", 0], "text_c": ","}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "51": {"inputs": {"beta_schedule": "lcm", "model": ["58", 0], "m_models": ["61", 0], "context_options": ["13", 0]}, "class_type": "ADE_UseEvolvedSampling", "_meta": {"title": "Use Evolved Sampling 🎭🅐🅓②"}}, "58": {"inputs": {"lora_name": "animatediff/AnimateLCM_sd15_t2v_lora.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "61": {"inputs": {"motion_model": ["62", 0], "motion_lora": ["113", 0]}, "class_type": "ADE_ApplyAnimateDiffModelSimple", "_meta": {"title": "Apply AnimateDiff Model 🎭🅐🅓②"}}, "62": {"inputs": {"model_name": "AnimateLCM_sd15_t2v.ckpt"}, "class_type": "ADE_LoadAnimateDiffModel", "_meta": {"title": "Load AnimateDiff Model 🎭🅐🅓②"}}, "66": {"inputs": {"text": "\"0\" : \"(simle:1.2), \",\n\"4\" : \"(laughing:1.2),\",\n\"8\" : \"(smile:1.2),\",\n\"12\" : \"(laughing:1.2),\",\n\"15\" : \"(smile:1.2),\",\n", "max_frames": ["76", 0], "print_output": true, "pre_text": ["37", 0], "app_text": ["37", 0], "start_frame": 0, "clip": ["12", 0]}, "class_type": "BatchPromptSchedule", "_meta": {"title": "Batch Prompt Schedule 📅🅕🅝"}}, "69": {"inputs": {"width": 512, "height": 768, "batch_size": ["76", 0]}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "76": {"inputs": {"int": 16}, "class_type": "Primitive integer [Crysto<PERSON>]", "_meta": {"title": "🪛 Primitive integer"}}, "87": {"inputs": {"weight": 1, "weight_type": "linear", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["88", 0], "ipadapter": ["88", 1], "image": ["95", 0], "clip_vision": ["89", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "88": {"inputs": {"preset": "PLUS (high strength)", "model": ["51", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "89": {"inputs": {"clip_name": "CLIP_ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "94": {"inputs": {"enabled": true, "swap_model": "inswapper_128.onnx", "facedetection": "retinaface_resnet50", "face_restore_model": "codeformer-v0.1.0.pth", "face_restore_visibility": 1, "codeformer_weight": 0.5, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "input_image": ["23", 0], "source_image": ["95", 0]}, "class_type": "ReActorFaceSwap", "_meta": {"title": "ReActor 🌌 Fast Face Swap"}}, "95": {"inputs": {"image_gen_width": 512, "image_gen_height": 768, "resize_mode": "Crop and Resize", "hint_image": ["33", 0]}, "class_type": "HintImageEnchance", "_meta": {"title": "Enchance And Resize Hint Images"}}, "113": {"inputs": {"lora_name": "v2_lora_PanLeft.ckpt", "strength": 1}, "class_type": "ADE_AnimateDiffLoRALoader", "_meta": {"title": "Load AnimateDiff LoRA 🎭🅐🅓"}}, "142": {"inputs": {"b1": 1.3, "b2": 1.4, "s1": 0.9, "s2": 0.2, "model": ["87", 0]}, "class_type": "FreeU_V2", "_meta": {"title": "FreeU_V2"}}}