#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from typing import Annotated, BinaryIO
from PIL import Image, ImageOps, PngImagePlugin
import requests
import torch

import matplotlib.pyplot as plt
import numpy as np 
import io
import base64
import piexif
import os
from ..config import settings
from starlette.exceptions import HTTPException

from diffusers import StableDiffusionXLInstructPix2PixPipeline
from diffusers.utils import load_image

from .safety_checker_lib import safety_checker

model_id = "diffusers/sdxl-instructpix2pix-768"
cache_dir = "./models/sdxl-instructpix2pix-768"
import os

# os.environ["HTTP_PROXY"]= "http://127.0.0.1:1080"
# os.environ["HTTPS_PROXY"]= "http://127.0.0.1:1080"

pipe = StableDiffusionXLInstructPix2PixPipeline.from_pretrained(model_id, resume_download=True, cache_dir=cache_dir, \
    torch_dtype=torch.float16,  proxies=settings.google_translate_proxy, local_files_only=True).to("cuda")

def encode_pil_to_base64(image, ext:str):

    with io.BytesIO() as output_bytes:

        if ext.lower() == 'png':
            use_metadata = False
            metadata = PngImagePlugin.PngInfo()
            for key, value in image.info.items():
                if isinstance(key, str) and isinstance(value, str):
                    metadata.add_text(key, value)
                    use_metadata = True
            image.save(output_bytes, format="PNG", pnginfo=(metadata if use_metadata else None), quality=80)

        elif ext.lower() in ("jpg", "jpeg", "webp"):
            parameters = image.info.get('parameters', None)
            exif_bytes = piexif.dump({
                "Exif": { piexif.ExifIFD.UserComment: piexif.helper.UserComment.dump(parameters or "", encoding="unicode") }
            })
            image.save(output_bytes, format="JPEG", exif = exif_bytes, quality=80)

        else:
            raise HTTPException(status_code=500, detail="Invalid image format")

        bytes_data = output_bytes.getvalue()

    return base64.b64encode(bytes_data)

def instruct_img_from_bytes(image : BinaryIO, prompt:str):
    img = Image.open(io.BytesIO(image.read()))
    img = ImageOps.exif_transpose(img)
    (width, height) = img.size
    rwidth = 768
    rheight = int(height/(width / rwidth))

    img = img.resize((rwidth, rheight))
    img = img.convert("RGB")

    imgb64 = encode_pil_to_base64(img, "png")
    imgb64 = imgb64.decode("utf-8")

    images = pipe(prompt=prompt,image=img, height=rheight, width=rwidth, guidance_scale=3.0, image_guidance_scale=1.5, num_inference_steps=30).images

    return encode_pil_to_base64(images[0], "png"), imgb64

def instruct_img_from_base64(image : str, prompt:str):

    img = Image.open(io.BytesIO(base64.b64decode(image)))
    img = ImageOps.exif_transpose(img)
    (width, height) = img.size
    rwidth = 768
    rheight = int(height/(width / rwidth))

    img = img.resize((rwidth, rheight))
    img = img.convert("RGB")

    imgb64 = encode_pil_to_base64(img, "png")
    imgb64 = imgb64.decode("utf-8")

    images = pipe(prompt=prompt,image=img, height=rheight, width=rwidth, guidance_scale=3.0, image_guidance_scale=1.5, num_inference_steps=30).images

    return encode_pil_to_base64(images[0], "png"), imgb64
