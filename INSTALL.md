./configure   --pkg-config-flags="--static" --extra-libs="-lpthread"   --ld="g++"   --enable-shared --enable-gpl   --enable-libfreetype   --enable-libmp3lame   --enable-libopus   --enable-libvorbis   --enable-libvpx   --enable-libx264   --enable-libx265   --enable-nonfree

apt update
apt -y install autoconf automake build-essential cmake git libass-dev libfreetype6-dev libgnutls28-dev libsdl2-dev libtool libva-dev libvdpau-dev libvorbis-dev libxcb1-dev libxcb-shm0-dev libxcb-xfixes0-dev pkg-config texinfo wget yasm zlib1g-dev nasm libx264-dev libx265-dev libnuma-dev libvpx-dev libfdk-aac-dev libmp3lame-dev libopus-dev libmediainfo-dev libunistring-dev build-essential python3-dev python3-setuptools make cmake libavcodec-dev libavfilter-dev libavformat-dev libavutil-dev libavdevice-dev
mkdir -p /workspace/ffmpeg_build /root/bin
cd /workspace
git -C aom pull 2> /dev/null || git clone --depth 1 https://aomedia.googlesource.com/aom && 
mkdir -p aom_build && 
cd aom_build && 
PATH="$HOME/bin:$PATH" cmake -G "Unix Makefiles" -DCMAKE_INSTALL_PREFIX="/workspace/ffmpeg_build" -DENABLE_SHARED=off -DENABLE_NASM=on ../aom && 
PATH="$HOME/bin:$PATH" make && 
make install
cd /root/ffmpeg_sources && 
git clone https://git.videolan.org/git/ffmpeg/nv-codec-headers.git && 
cd nv-codec-headers && 
make && 
make install
cd /root/ffmpeg_sources && 
wget -O ffmpeg-snapshot.tar.bz2 https://ffmpeg.org/releases/ffmpeg-snapshot.tar.bz2 && 
tar xjvf ffmpeg-snapshot.tar.bz2 && 
cd ffmpeg && 
sed -i -- 's/compute_30/compute_61/g' configure && 
sed -i -- 's/sm_30/sm_61/g' configure && 

PKG_CONFIG_PATH="/workspace/ffmpeg_build/lib/pkgconfig" ./configure \
--prefix="/workspace/ffmpeg_build" \
--pkg-config-flags="--static" \
--extra-cflags="-I/workspace/ffmpeg_build/include" \
--extra-ldflags="-L/workspace/ffmpeg_build/lib" \
--extra-libs="-lpthread -lm" \
--bindir="/usrlocal/bin" \
--enable-shared \
--enable-cuda-nvcc \
--enable-cuvid \
--enable-nvenc \
--enable-nonfree \
--enable-libnpp \
--extra-cflags=-I/usr/local/cuda/include \
--extra-ldflags=-L/usr/local/cuda/lib64 \
--enable-gpl \
--enable-gnutls \
--enable-libaom \
--enable-libass \
--enable-libfdk-aac \
--enable-libfreetype \
--enable-libmp3lame \
--enable-libopus \
--enable-libvorbis \
--enable-libvpx \
--enable-libx264 \
--enable-libx265 \
--enable-nonfree && PATH="$HOME/bin:$PATH" make && make install


hash -r
cp ./libnvcuvid.so /usr/local/cuda/lib64
cd $HOME && 
git clone --recursive https://github.com/dmlc/decord && 
cd decord && 
mkdir build && cd build && 
cmake .. -DUSE_CUDA=ON -DCMAKE_BUILD_TYPE=Release -DFFMPEG_DIR=/workspace/ffmpeg_build && 
make && 
cd ../python && 
python setup.py install --user

#docker 提交命令
docker commit -m "add ffmpeg and decord support" bec1526b087d gothz/sd-webui:1.2

#opencv安装
pip install opencv-python==******** opencv-python-headless==******** opencv-contrib-python==******** opencv-fixer==0.2.5

#onnxruntime-gpu 安装
rm -r -f  /usr/lib/cmake/utf8-range

./build.sh --config Release --update \
--nvcc_threads 2 --build --build_wheel --use_tensorrt --skip_tests --use_cuda --enable_pybind \
--cuda_home /usr/local/cuda-12 --cudnn_home /usr/lib/aarch64-linux-gnu \
--tensorrt_home /usr/lib/aarch64-linux-gnu --allow_running_as_root

cd /workspace/onnxruntime/build/Linux/Release
/usr/bin/python3 /workspace/onnxruntime/setup.py install --wheel_name_suffix=gpu
/usr/bin/python3 /workspace/onnxruntime/setup.py install 

# linux 跑xformers + triton 
# https://github.com/facebookresearch/xformers/issues/960
# 编辑 /usr/local/lib/python3.10/dist-packages/torch/_inductor/triton_heuristics.py
# 替换 from triton.runtime.jit import get_cuda_stream, KernelInterface
# 为 
# from triton.runtime.jit import KernelInterface
# from torch._C import _cuda_getCurrentRawStream as get_cuda_stream
# xformers 安装
# MAX_JOBS=2 pip install -v -e .
# triton 安装
# 执行 pip install -e python
# HTTP_PROXY=************:1080 HTTPS_PROXY=************:1080 pip install -e python
# python -m torch.utils.collect_env
# python -m xformers.info 


# linux 安装opencv
# 编译安装opencv-4.9.0的代码
cd opencv
mkdir build
cd build
cmake -D CMAKE_BUILD_TYPE=RELEASE \
-D CMAKE_INSTALL_PREFIX=/usr/local \
-D OPENCV_GENERATE_PKGCONFIG=ON \
-D BUILD_EXAMPLES=OFF \
-D INSTALL_PYTHON_EXAMPLES=OFF \
-D INSTALL_C_EXAMPLES=OFF \
-D PYTHON_EXECUTABLE=$(which python2) \
-D BUILD_opencv_python2=OFF \
-D PYTHON3_EXECUTABLE=$(which python3) \
-D PYTHON3_INCLUDE_DIR=$(python3 -c "from distutils.sysconfig import get_python_inc; print(get_python_inc())") \
-D PYTHON3_PACKAGES_PATH=$(python3 -c "from distutils.sysconfig import get_python_lib; print(get_python_lib())") \
-D OPENCV_EXTRA_MODULES_PATH=../../opencv_contrib/modules/ \
-D WITH_GSTREAMER=ON \
-D WITH_CUDA=ON \

make -j8
make install 
ldconfig
pip install opencv-python==******** 

docker维护
docker login
docker commit 7f0e7cce09f6 gothz/sd-webui:1.0
docker push gothz/sd-webui:1.0 

#编译pytorch 2.4 编译走不通 还得找docker
 export USE_PRIORITIZED_TEXT_FOR_LD=1   
 export PYTORCH_BUILD_VERSION=2.4.0a0+38b96d3 
 export PYTORCH_HOME=/opt/pytorch/pytorch 
 export PYTORCH_VERSION=2.4.0a0+38b96d3 
 export CMAKE_PREFIX_PATH=/usr/local
 export MAX_JOBS=4 
 nohup python setup.py install &
 MAX_JOBS=4  python setup.py install

#切换镜像
 docker pull dockerproxy.cn/pytorch/pytorch:2.4.1-cuda12.1-cudnn9-devel
 
cd pytorch && \
    USE_PRIORITIZED_TEXT_FOR_LD=1 \
    USE_CUPTI_SO=1 \
    USE_KINETO=1 \
    CMAKE_PREFIX_PATH="/usr/local" \
    NCCL_ROOT="/usr" \
    USE_SYSTEM_NCCL=1 \
    USE_UCC=1 \
    USE_SYSTEM_UCC=1 \
    UCC_HOME="/opt/hpcx/ucc" \
    UCC_DIR="/opt/hpcx/ucc/lib/cmake/ucc" \
    UCX_HOME="/opt/hpcx/ucx" \
    UCX_DIR="/opt/hpcx/ucx/lib/cmake/ucx" \
    CFLAGS='-fno-gnu-unique' \
    DEFAULT_INTEL_MKL_DIR="/usr/local" \
    INTEL_MKL_DIR="/usr/local" \
    python setup.py install 
    
USE_PRIORITIZED_TEXT_FOR_LD=1 \
    USE_CUPTI_SO=1 \
    USE_KINETO=1 \
    CMAKE_PREFIX_PATH="/usr/local" \
    NCCL_ROOT="/usr" \
    USE_SYSTEM_NCCL=1 \
    USE_UCC=1 \
    USE_SYSTEM_UCC=1 \
    UCC_HOME="/opt/hpcx/ucc" \
    UCC_DIR="/opt/hpcx/ucc/lib/cmake/ucc" \
    UCX_HOME="/opt/hpcx/ucx" \
    UCX_DIR="/opt/hpcx/ucx/lib/cmake/ucx" \
    CFLAGS='-fno-gnu-unique' \
    DEFAULT_INTEL_MKL_DIR="/usr/local" \
    INTEL_MKL_DIR="/usr/local"  MAX_JOBS=4 \
    python setup.py install                                                                                                                                                                                                                                        
