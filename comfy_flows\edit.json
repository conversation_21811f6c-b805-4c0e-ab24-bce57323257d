{"1": {"inputs": {"vae_name": "flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "12": {"inputs": {"image": "v0d00fg10000cm5pr2rc77u7tcjikdk0[(000243)2024-01-13-20-48-06.jpg"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "16": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5-v1_1-xxl-encoder-Q5_K_S.gguf", "type": "flux"}, "class_type": "DualCLIPLoaderGGUF", "_meta": {"title": "DualCLIPLoader (GGUF)"}}, "17": {"inputs": {"unet_name": "flux1-dev-Q5_K_S.gguf", "dequant_dtype": "bfloat16", "patch_dtype": "bfloat16", "patch_on_device": false}, "class_type": "UnetLoaderGGUFAdvanced", "_meta": {"title": "Unet Loader (GGUF/Advanced)"}}, "18": {"inputs": {"width": ["62", 0], "height": ["62", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": 0, "image": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "42": {"inputs": {"clip_l": ["74", 0], "t5xxl": ["152", 0], "guidance": 1, "speak_and_recognation": {"__value__": [false, true]}, "clip": ["202", 1]}, "class_type": "CLIPTextEncodeFlux", "_meta": {"title": "CLIPTextEncodeFlux"}}, "53": {"inputs": {"conditioning": ["42", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "62": {"inputs": {"width": 1024, "height": 1024, "aspect_ratio": "3:4 portrait 896x1152", "swap_dimensions": "Off", "upscale_factor": 1, "batch_size": 1}, "class_type": "CR SDXL Aspect Ratio", "_meta": {"title": "AspectRatio"}}, "68": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["18", 1], "height": ["18", 2], "model": ["221", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "69": {"inputs": {"model": "promptgen_base_v2.0", "folder_path": "Path to your image folder", "caption_method": "simple", "max_new_tokens": 1024, "num_beams": 4, "random_prompt": "never", "prefix_caption": "", "suffix_caption": "", "replace_tags": "", "speak_and_recognation": {"__value__": [false, true]}, "images": ["18", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Tagger", "_meta": {"title": "🐾MiaoshouAI Tagger"}}, "74": {"inputs": {"separator": ",", "text1": ["276", 0], "text2": ["197", 0]}, "class_type": "CR Text Concatenate", "_meta": {"title": "🔤 CR Text Concatenate"}}, "86": {"inputs": {"lora_name": "flux/Hyper-FLUX.1-dev-8steps-lora.safetensors", "strength_model": 0.13, "strength_clip": 0.13, "model": ["292", 0], "clip": ["16", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "122": {"inputs": {"mode": true, "a": ["18", 1], "b": ["18", 2]}, "class_type": "ImpactMinMax", "_meta": {"title": "ImpactMinMax"}}, "152": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": ["276", 0], "text_b": ["197", 0], "text_c": ["69", 2], "speak_and_recognation": {"__value__": [false, true]}, "result": "Comics style, lineart, A young woman sitting at a table in a casual cafe, holding a smartphone"}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "197": {"inputs": {"from_translate": "chinese simplified", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "MyMemoryTranslator [free]", "text": "", "Show proxy": "proxy_hide", "Show authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "UserPrompt"}}, "200": {"inputs": {"preprocessor": "AnyLineArtPreprocessor_aux", "resolution": ["122", 0], "image": ["18", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "202": {"inputs": {"lora_name": "flux/aidmaFLUXPro1.1-FLUX-v0.3.safetensors", "strength_model": 0.9, "strength_clip": 0.9, "model": ["86", 0], "clip": ["86", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "205": {"inputs": {"samples": ["206", 0], "vae": ["1", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "206": {"inputs": {"noise": ["210", 0], "guider": ["209", 0], "sampler": ["213", 0], "sigmas": ["208", 0], "latent_image": ["249", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "207": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "208": {"inputs": {"scheduler": "simple", "steps": 8, "denoise": 0.75, "model": ["319", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "209": {"inputs": {"model": ["319", 0], "conditioning": ["219", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "210": {"inputs": {"noise_seed": 122258882211916}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "213": {"inputs": {"detail_amount": 0.5, "start": 0.2, "end": 1, "bias": 0.5, "exponent": 0.5, "start_offset": 0, "end_offset": 0, "fade": 0, "smooth": true, "cfg_scale_override": 1, "sampler": ["207", 0]}, "class_type": "DetailDaemonSamplerNode", "_meta": {"title": "Detail <PERSON>"}}, "214": {"inputs": {"filename_prefix": "edit/flux", "images": ["205", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "215": {"inputs": {"anything": ["205", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "219": {"inputs": {"positive": ["291", 0], "negative": ["53", 0], "vae": ["1", 0], "pixels": ["200", 0]}, "class_type": "InstructPixToPixConditioning", "_meta": {"title": "InstructPixToPixConditioning"}}, "221": {"inputs": {"lora_name": "flux/flux1-depth-dev-lora.safetensors", "strength_model": 1, "model": ["202", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "232": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "234": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "249": {"inputs": {"pixels": ["18", 0], "vae": ["1", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "256": {"inputs": {"guidance": 5, "conditioning": ["42", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "PictureDetail"}}, "259": {"inputs": {"downsampling_factor": 1, "downsampling_function": "nearest", "mode": "keep aspect ratio", "weight": 1, "autocrop_margin": 0.2, "conditioning": ["256", 0], "style_model": ["234", 0], "clip_vision": ["232", 0], "image": ["263", 0]}, "class_type": "ReduxAdvanced", "_meta": {"title": "ReduxAdvanced"}}, "263": {"inputs": {"samples": ["264", 0], "vae": ["1", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "264": {"inputs": {"noise": ["268", 0], "guider": ["267", 0], "sampler": ["269", 0], "sigmas": ["266", 0], "latent_image": ["273", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "265": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "266": {"inputs": {"scheduler": "simple", "steps": 8, "denoise": 1, "model": ["312", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "267": {"inputs": {"model": ["312", 0], "conditioning": ["280", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "268": {"inputs": {"noise_seed": 299858852788394}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "269": {"inputs": {"detail_amount": 0.5, "start": 0.2, "end": 1, "bias": 0.5, "exponent": 0.5, "start_offset": 0, "end_offset": 0, "fade": 0, "smooth": true, "cfg_scale_override": 0, "sampler": ["265", 0]}, "class_type": "DetailDaemonSamplerNode", "_meta": {"title": "Detail <PERSON>"}}, "273": {"inputs": {"width": ["18", 1], "height": ["18", 2], "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "276": {"inputs": {"string": "Comics style, lineart"}, "class_type": "StringConstant", "_meta": {"title": "StyleLora"}}, "277": {"inputs": {"clip_l": ["74", 0], "t5xxl": ["74", 0], "guidance": 3.5, "speak_and_recognation": {"__value__": [false, true]}, "clip": ["202", 1]}, "class_type": "CLIPTextEncodeFlux", "_meta": {"title": "CLIPTextEncodeFlux"}}, "278": {"inputs": {"conditioning": ["277", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "279": {"inputs": {"preprocessor": "DepthAnythingV2Preprocessor", "resolution": ["122", 0], "image": ["18", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "280": {"inputs": {"positive": ["277", 0], "negative": ["278", 0], "vae": ["1", 0], "pixels": ["279", 0]}, "class_type": "InstructPixToPixConditioning", "_meta": {"title": "InstructPixToPixConditioning"}}, "289": {"inputs": {"downsampling_factor": 3, "downsampling_function": "nearest", "mode": "keep aspect ratio", "weight": 1, "autocrop_margin": 0.2, "conditioning": ["42", 0], "style_model": ["234", 0], "clip_vision": ["232", 0], "image": ["18", 0]}, "class_type": "ReduxAdvanced", "_meta": {"title": "ReduxAdvanced"}}, "291": {"inputs": {"conditioning_to": ["259", 0], "conditioning_from": ["289", 0]}, "class_type": "ConditioningConcat", "_meta": {"title": "Conditioning (Concat)"}}, "292": {"inputs": {"model": ["17", 0]}, "class_type": "DifferentialDiffusion", "_meta": {"title": "Differential Diffusion"}}, "300": {"inputs": {"lora_name": "flux/flux1-canny-dev-lora.safetensors", "strength_model": 1, "model": ["202", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "301": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["18", 1], "height": ["18", 2], "model": ["300", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "302": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "model": ["68", 0], "pulid_flux": ["303", 0], "eva_clip": ["304", 0], "face_analysis": ["305", 0], "image": ["306", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "303": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "304": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "305": {"inputs": {"provider": "CPU"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "306": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["18", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "308": {"inputs": {"anything": ["263", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "311": {"inputs": {"model": ["302", 0]}, "class_type": "FluxForwardOverrider", "_meta": {"title": "FluxForwardOverrider"}}, "312": {"inputs": {"rel_l1_thresh": 0.25, "cache_device": "offload_device", "wan_coefficients": "disabled", "model": ["311", 0]}, "class_type": "ApplyTeaCachePatch", "_meta": {"title": "ApplyTeaCachePatch"}}, "313": {"inputs": {"model": ["314", 0]}, "class_type": "FluxForwardOverrider", "_meta": {"title": "FluxForwardOverrider"}}, "314": {"inputs": {"weight": 1, "start_at": 0, "end_at": 1, "model": ["301", 0], "pulid_flux": ["318", 0], "eva_clip": ["315", 0], "face_analysis": ["317", 0], "image": ["316", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "315": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "316": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["263", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "317": {"inputs": {"provider": "CPU"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "318": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "319": {"inputs": {"rel_l1_thresh": 0.25, "cache_device": "offload_device", "wan_coefficients": "disabled", "model": ["313", 0]}, "class_type": "ApplyTeaCachePatch", "_meta": {"title": "ApplyTeaCachePatch"}}}