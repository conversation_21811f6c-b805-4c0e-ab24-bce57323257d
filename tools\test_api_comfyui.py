#!/usr/bin/env python 
# -*- coding:utf-8 -*-
# 测试comfyui工作流
#

import websocket #NOTE: websocket-client (https://github.com/websocket-client/websocket-client)
import uuid
import json
import urllib.request
import urllib.parse
import argparse
import os
import base64
import io
from PIL import Image, PngImagePlugin
from requests_toolbelt import MultipartEncoder
import random

def open_websocket_connection():
  #server_address='127.0.0.1:8188'
  server_address='************:8188'
  client_id=str(uuid.uuid4())
  ws = websocket.WebSocket()
  ws.connect("ws://{}/ws?clientId={}".format(server_address, client_id))
  return ws, server_address, client_id

def queue_prompt(prompt, client_id, server_address):
  p = {"prompt": prompt, "client_id": client_id}
  headers = {'Content-Type': 'application/json'}
  data = json.dumps(p).encode('utf-8')
  req =  urllib.request.Request("http://{}/prompt".format(server_address), data=data, headers=headers)
  return json.loads(urllib.request.urlopen(req).read())

def get_history(prompt_id, server_address):
  with urllib.request.urlopen("http://{}/history/{}".format(server_address, prompt_id)) as response:
      return json.loads(response.read())
  
def get_image(filename, subfolder, folder_type, server_address):
  data = {"filename": filename, "subfolder": subfolder, "type": folder_type}
  url_values = urllib.parse.urlencode(data)
  with urllib.request.urlopen("http://{}/view?{}".format(server_address, url_values)) as response:
      return response.read()

def upload_image(input_path, name, server_address, image_type="input", overwrite=False):
  with open(input_path, 'rb') as file:
    multipart_data = MultipartEncoder(
      fields= {
        'image': (name, file, 'image/png'),
        'type': image_type,
        'overwrite': str(overwrite).lower()
      }
    )

    data = multipart_data
    headers = { 'Content-Type': multipart_data.content_type }
    request = urllib.request.Request("http://{}/upload/image".format(server_address), data=data, headers=headers)
    with urllib.request.urlopen(request) as response:
      return response.read()

def load_workflow(workflow_path):
  try:
      with open(workflow_path, 'r', encoding='utf-8') as file:
          workflow = json.load(file)
          return json.dumps(workflow)
  except FileNotFoundError:
      print(f"The file {workflow_path} was not found.")
      return None
  except json.JSONDecodeError:
      print(f"The file {workflow_path} contains invalid JSON.")
      return None

def track_progress(prompt, ws, prompt_id):
  node_ids = list(prompt.keys())
  finished_nodes = []

  while True:
      out = ws.recv()
      if isinstance(out, str):
          message = json.loads(out)
          if message['type'] == 'progress':
              data = message['data']
              current_step = data['value']
              print('In K-Sampler -> Step: ', current_step, ' of: ', data['max'])
          if message['type'] == 'execution_cached':
              data = message['data']
              for itm in data['nodes']:
                  if itm not in finished_nodes:
                      finished_nodes.append(itm)
                      print('Progess: ', len(finished_nodes), '/', len(node_ids), ' Tasks done')
          if message['type'] == 'executing':
              data = message['data']
              if data['node'] not in finished_nodes:
                  finished_nodes.append(data['node'])
                  print('Progess: ', len(finished_nodes), '/', len(node_ids), ' Tasks done')

              if data['node'] is None and data['prompt_id'] == prompt_id:
                  break #Execution is done
      else:
          continue
  return

def get_images(prompt_id, server_address, allow_preview = False):
    output_images = []

    history = get_history(prompt_id, server_address)[prompt_id]
    for node_id in history['outputs']:
        node_output = history['outputs'][node_id]
        output_data = {}
        if 'images' in node_output:
            for image in node_output['images']:
                if allow_preview and image['type'] == 'temp':
                    preview_data = get_image(image['filename'], image['subfolder'], image['type'], server_address)
                    output_data['image_data'] = preview_data
                if image['type'] == 'output':
                    image_data = get_image(image['filename'], image['subfolder'], image['type'], server_address)
                    output_data['image_data'] = image_data
        output_data['file_name'] = image['filename']
        output_data['type'] = image['type']
        output_images.append(output_data)

    return output_images

def generate_image_by_prompt_and_image(prompt, output_path, input_path, filename, save_previews=False):
    try:
        ws, server_address, client_id = open_websocket_connection()
        upload_image(input_path, filename, server_address)
        prompt_id = queue_prompt(prompt, client_id, server_address)['prompt_id']
        track_progress(prompt, ws, prompt_id)
        images = get_images(prompt_id, server_address, save_previews)
        save_image(images, output_path, save_previews)
    finally:
        ws.close()
    
def prompt_image_to_image(workflow, input_path, positve_prompt, negative_prompt='', save_previews=False):
    prompt = json.loads(workflow)
    id_to_class_type = {id: details['class_type'] for id, details in prompt.items()}
    k_sampler = [key for key, value in id_to_class_type.items() if value == 'KSampler'][0]
    prompt.get(k_sampler)['inputs']['seed'] = random.randint(10**14, 10**15 - 1)
    postive_input_id = prompt.get(k_sampler)['inputs']['positive'][0]
    prompt.get(postive_input_id)['inputs']['text'] = positve_prompt

    if negative_prompt != '':
        negative_input_id = prompt.get(k_sampler)['inputs']['negative'][0]
        prompt.get(negative_input_id)['inputs']['text'] = negative_prompt

    image_loader = [key for key, value in id_to_class_type.items() if value == 'LoadImage'][0]
    filename = input_path.split('/')[-1]
    prompt.get(image_loader)['inputs']['image'] = filename

    generate_image_by_prompt_and_image(prompt, './output/', input_path, filename, save_previews)
  
def save_image(images, output_path, save_previews):
  for itm in images:
      directory = os.path.join(output_path, 'temp/') if itm['type'] == 'temp' and save_previews else output_path
      os.makedirs(directory, exist_ok=True)
      try:
          image = Image.open(io.BytesIO(itm['image_data']))
          image.save(os.path.join(directory, itm['file_name']))
      except Exception as e:
          print(f"Failed to save image {itm['file_name']}: {e}")  
          
def generate_image_by_prompt(prompt, output_path, save_previews=False):
    try:
        ws, server_address, client_id = open_websocket_connection()
        prompt_id = queue_prompt(prompt, client_id, server_address)['prompt_id']
        track_progress(prompt, ws, prompt_id)
        images = get_images(prompt_id, server_address, save_previews)
        save_image(images, output_path, save_previews)
    finally:
        ws.close()
    
def prompt_to_image(workflow, positve_prompt, negative_prompt='', save_previews=False):
    prompt = json.loads(workflow)
    id_to_class_type = {id: details['class_type'] for id, details in prompt.items()}
    k_sampler = [key for key, value in id_to_class_type.items() if value == 'KSampler'][0]
    prompt.get(k_sampler)['inputs']['seed'] = random.randint(10**14, 10**15 - 1)
    postive_input_id = prompt.get(k_sampler)['inputs']['positive'][0]
    prompt.get(postive_input_id)['inputs']['text'] = positve_prompt

    if negative_prompt != '':
        negative_input_id = prompt.get(k_sampler)['inputs']['negative'][0]
        prompt.get(negative_input_id)['inputs']['text'] = negative_prompt

    generate_image_by_prompt(prompt, './output/', save_previews)
  
def gen_image(outdir, img, name_prefix):

    workflow = load_workflow("./comfy_flows/TestFirst.json")    
    prompt = json.loads(workflow)
    id_to_class_type = {id: details['class_type'] for id, details in prompt.items()}
    k_sampler = [key for key, value in id_to_class_type.items() if value == 'KSampler'][0]
    prompt.get(k_sampler)['inputs']['seed'] = random.randint(10**14, 10**15 - 1)
 
    image_loader = [key for key, value in id_to_class_type.items() if value == 'LoadImage'][0]
    filename = img.split('/')[-1]
    prompt.get(image_loader)['inputs']['image'] = filename

    postive_input_id = prompt.get(k_sampler)['inputs']['positive'][0]
    prompt.get(postive_input_id)['inputs']['text'] = "masterpiece, best quality, 1girl"


    negative_input_id = prompt.get(k_sampler)['inputs']['negative'][0]
    prompt.get(negative_input_id)['inputs']['text'] = "lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry"
    
    
    try:
        ws, server_address, client_id = open_websocket_connection()
        upload_image(img, filename, server_address)
        prompt_id = queue_prompt(prompt, client_id, server_address)['prompt_id']
        track_progress(prompt, ws, prompt_id)
        images = get_images(prompt_id, server_address, False)
        save_image(images, outdir, False)
        
    finally:
        ws.close()


def encode_image(image):

    with io.BytesIO() as output_bytes:
        metadata = None
        for key, value in image.info.items():
            if isinstance(key, str) and isinstance(value, str):
                if metadata is None:
                    metadata = PngImagePlugin.PngInfo()
                metadata.add_text(key, value)
        image.save(output_bytes, format="PNG", pnginfo=metadata)
        bytes_data = output_bytes.getvalue()
        
        ret = str(base64.b64encode(bytes_data), "utf-8")
        return ret 

def main(args):

    outdir = args.outdir  
    if os.path.isfile(args.indir):
        name = args.indir
        if name.endswith(".png") or name.endswith(".jpg") or name.endswith(".jpeg"):
            name_prefix = name.split(".")[0]
            gen_image(outdir, name, name_prefix)
    else:
        for p, dirs, files in os.walk(args.indir):
            for name in files:
                if name.endswith(".png") or name.endswith(".jpg") or name.endswith(".jpeg"):
                    img_file = os.path.join(p, name)
                    print(img_file)
                    name_prefix = name.split(".")[0]
                    gen_image(outdir, img_file, name_prefix)

def setup_parser() -> argparse.ArgumentParser:

    parser = argparse.ArgumentParser()
    parser.add_argument("--indir", type=str, required=True, default=None, help="sence to generate")
    parser.add_argument("--outdir", type=str, required=True, default="outputs", help="dir to write results to")
    return parser

if __name__ == "__main__":
    
    parser = setup_parser()
    args = parser.parse_args()
    main(args)



