#!/bin/bash

#docker run --gpus all -it --ipc=host --ulimit memlock=-1 --ulimit stack=67108864 -v /root/stable-diffusion-webui:/workspace/stable-diffusion-webui -p 7860:7860 --name sd-webui nvcr.io/nvidia/pytorch:23.09-py3
docker run --gpus all -it --ipc=host --ulimit memlock=-1 --ulimit stack=67108864 -v /root/stable-diffusion-webui:/workspace/stable-diffusion-webui -v /root/ComfyUI:/workspace/ComfyUI -p 7860:7860 -p 7863:8188 --name sd-webui-comfyui gothz/sd-webui:1.0
docker run --gpus all -it --ipc=host --ulimit memlock=-1 --ulimit stack=67108864 -v /root/ComfyUI:/workspace/ComfyUI -p 7861:8188 --name byjh-comfyui gothz/sd-webui:1.2
docker run --gpus all -it --ipc=host --ulimit memlock=-1 --ulimit stack=67108864 -v /root/ComfyUI:/workspace/ComfyUI -p 7864:8188 --name banyunjuhe-comfyui nvcr.io/nvidia/pytorch:24.05-py3

