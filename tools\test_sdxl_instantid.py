#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import os
os.environ["HTTP_PROXY"]= "http://127.0.0.1:1080"
os.environ["HTTPS_PROXY"]= "http://127.0.0.1:1080"

import torch
from controlnet_aux import OpenposeDetector

from huggingface_hub import hf_hub_download
# hf_hub_download(repo_id="InstantX/InstantID", filename="ControlNetModel/config.json", local_dir="./models/instantid")
# hf_hub_download(repo_id="InstantX/InstantID", filename="ControlNetModel/diffusion_pytorch_model.safetensors", local_dir="./models/instantid")
# hf_hub_download(repo_id="InstantX/InstantID", filename="ip-adapter.bin", local_dir="./models/instantid")
# hf_hub_download(repo_id="latent-consistency/lcm-lora-sdxl", filename="pytorch_lora_weights.safetensors", local_dir="./models/instantid")

# controlnet_pose_model = "thibaud/controlnet-openpose-sdxl-1.0"
# hf_hub_download(repo_id=controlnet_pose_model, filename="config.json", local_dir="./models/instantid/controlnet-openpose-sdxl-1.0")
# hf_hub_download(repo_id=controlnet_pose_model, filename="diffusion_pytorch_model.bin", local_dir="./models/instantid/controlnet-openpose-sdxl-1.0")

# controlnet_canny_model = "diffusers/controlnet-canny-sdxl-1.0-small"
# hf_hub_download(repo_id=controlnet_canny_model, filename="config.json", local_dir="./models/instantid/controlnet-canny-sdxl-1.0-small")
# hf_hub_download(repo_id=controlnet_canny_model, filename="diffusion_pytorch_model.bin", local_dir="./models/instantid/controlnet-canny-sdxl-1.0-small")

# controlnet_depth_model = "diffusers/controlnet-depth-sdxl-1.0-small"
# hf_hub_download(repo_id=controlnet_depth_model, filename="config.json", local_dir="./models/instantid/controlnet-depth-sdxl-1.0-small")
# hf_hub_download(repo_id=controlnet_depth_model, filename="diffusion_pytorch_model.bin", local_dir="./models/instantid/controlnet-depth-sdxl-1.0-small")


controlnet_openpose_detect = "lllyasviel/ControlNet"
# hf_hub_download(repo_id=controlnet_openpose_detect, filename="annotator/ckpts/body_pose_model.pth", local_dir="./models/instantid/controlnet-openpose-sdxl-1.0")
#hf_hub_download(repo_id=controlnet_openpose_detect, filename="annotator/ckpts/hand_pose_model.pth", local_dir="./models/instantid/controlnet-openpose-sdxl-1.0")

openpose = OpenposeDetector.from_pretrained("lllyasviel/ControlNet", cache_dir="./models/instantid/controlnet-openpose-sdxl-1.0")


# depth_anything_model = 'LiheYoung/depth_anything_vitl14'
# hf_hub_download(repo_id=depth_anything_model, filename="config.json", local_dir="./models/instantid/depth_anything_vitl14")
# hf_hub_download(repo_id=depth_anything_model, filename="pytorch_model.bin", local_dir="./models/instantid/depth_anything_vitl14")


# !pip install opencv-python transformers accelerate insightface
# import diffusers
# from diffusers.utils import load_image
# from diffusers.models import ControlNetModel

# import cv2
# import torch
# import numpy as np
# from PIL import Image

# from insightface.app import FaceAnalysis
# from instantid.pipeline_stable_diffusion_xl_instantid import StableDiffusionXLInstantIDPipeline, draw_kps
# from diffusers import LCMScheduler

# def resize_img(input_image, max_side=1280, min_side=1024, size=None, 
#                pad_to_max_side=False, mode=Image.BILINEAR, base_pixel_number=64):

#     w, h = input_image.size
#     if size is not None:
#         w_resize_new, h_resize_new = size
#     else:
#         ratio = min_side / min(h, w)
#         w, h = round(ratio*w), round(ratio*h)
#         ratio = max_side / max(h, w)
#         input_image = input_image.resize([round(ratio*w), round(ratio*h)], mode)
#         w_resize_new = (round(ratio * w) // base_pixel_number) * base_pixel_number
#         h_resize_new = (round(ratio * h) // base_pixel_number) * base_pixel_number
#     input_image = input_image.resize([w_resize_new, h_resize_new], mode)

#     if pad_to_max_side:
#         res = np.ones([max_side, max_side, 3], dtype=np.uint8) * 255
#         offset_x = (max_side - w_resize_new) // 2
#         offset_y = (max_side - h_resize_new) // 2
#         res[offset_y:offset_y+h_resize_new, offset_x:offset_x+w_resize_new] = np.array(input_image)
#         input_image = Image.fromarray(res)
#     return input_image

# # prepare 'antelopev2' under ./models
# app = FaceAnalysis(name='antelopev2', root='./models/instantid', providers=['CUDAExecutionProvider', 'CPUExecutionProvider'])
# app.prepare(ctx_id=0, det_size=(640, 640))

# # prepare models under ./checkpoints
# face_adapter = f'./models/instantid/ip-adapter.bin'
# controlnet_path = f'./models/instantid/ControlNetModel'

# # load IdentityNet
# controlnet = ControlNetModel.from_pretrained(controlnet_path, torch_dtype=torch.float16)

# # base_model = 'wangqixun/YamerMIX_v8'  # from https://civitai.com/models/84040?modelVersionId=196039
# base_model = './models/instantid/SDXLRonghua_v40'

# pipe = StableDiffusionXLInstantIDPipeline.from_pretrained(
#     base_model,
#     controlnet=controlnet,
#     torch_dtype=torch.float16
# )
# pipe.cuda()

# # load adapter
# pipe.load_ip_adapter_instantid(face_adapter)

# # load an image
# face_image = load_image("./media/examples/yann-lecun_resize.jpg")

# # prepare face emb
# face_info = app.get(cv2.cvtColor(np.array(face_image), cv2.COLOR_RGB2BGR))
# face_info = sorted(face_info, key=lambda x:(x['bbox'][2]-x['bbox'][0])*x['bbox'][3]-x['bbox'][1])[-1]  # only use the maximum face
# face_emb = face_info['embedding']
# face_kps = draw_kps(face_image, face_info['kps'])

# # prompt
# prompt = "film noir style, ink sketch|vector, male man, highly detailed, sharp focus, ultra sharpness, monochrome, high contrast, dramatic shadows, 1940s style, mysterious, cinematic"
# negative_prompt = "ugly, deformed, noisy, blurry, low contrast, realism, photorealistic, vibrant, colorful"

# lcm_lora_path = "./models/instantid/pytorch_lora_weights.safetensors"

# pipe.load_lora_weights(lcm_lora_path)
# pipe.fuse_lora()
# pipe.scheduler = LCMScheduler.from_config(pipe.scheduler.config)

# num_inference_steps = 10
# guidance_scale = 7

# # generate image
# images = pipe(
#     prompt=prompt,
#     negative_prompt=negative_prompt,
#     image_embeds=face_emb,
#     image=face_kps,
#     controlnet_conditioning_scale=0.8,
#     ip_adapter_scale=0.8,
#     num_inference_steps=num_inference_steps,
#     guidance_scale=guidance_scale,
#     rcond=None
# ).images

# images[0].save("./media/examples/test.png")






