{"10": {"inputs": {"seed": 550351398165365, "steps": 8, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1, "preview_method": "auto", "vae_decode": "true", "model": ["88", 0], "positive": ["11", 1], "negative": ["11", 2], "latent_image": ["11", 3], "optional_vae": ["11", 4]}, "class_type": "KSampler (Efficient)", "_meta": {"title": "KSampler (Efficient)"}}, "11": {"inputs": {"ckpt_name": "DreamShaperXL_Lightning-SFW.safetensors", "vae_name": "Baked VAE", "clip_skip": -1, "lora_name": "None", "lora_model_strength": 1, "lora_clip_strength": 1, "positive": ["93", 0], "negative": "(nsfw:1.3), lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry,hand ", "token_normalization": "none", "weight_interpretation": "comfy", "empty_latent_width": 768, "empty_latent_height": 1024, "batch_size": 1, "speak_and_recognation": true}, "class_type": "Efficient Loader", "_meta": {"title": "Efficient Loader"}}, "12": {"inputs": {"filename_prefix": "toy/toy", "images": ["50", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "19": {"inputs": {"preset": "PLUS FACE (portraits)", "model": ["11", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "20": {"inputs": {"image": "18e25b92aff6f8082c13de035ba08eb3.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "22": {"inputs": {"clip_name": "CLIP_ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "50": {"inputs": {"facedetection": "retinaface_resnet50", "model": "codeformer-v0.1.0.pth", "visibility": 1, "codeformer_weight": 0.7000000000000001, "image": ["89", 0]}, "class_type": "ReActorRestoreFace", "_meta": {"title": "Restore Face 🌌 ReActor"}}, "77": {"inputs": {"enabled": true, "swap_model": "inswapper_128.onnx", "facedetection": "retinaface_resnet50", "face_restore_model": "codeformer-v0.1.0.pth", "face_restore_visibility": 1, "codeformer_weight": 0.5, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "input_image": ["10", 5], "source_image": ["95", 0]}, "class_type": "ReActorFaceSwap", "_meta": {"title": "ReActor 🌌 Fast Face Swap"}}, "88": {"inputs": {"weight": 1, "weight_type": "linear", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["19", 0], "ipadapter": ["19", 1], "image": ["95", 0], "clip_vision": ["22", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "89": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": 647256582813033, "steps": 8, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 0.5, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 10, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 10, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "speak_and_recognation": true, "image": ["77", 0], "model": ["10", 0], "clip": ["11", 5], "vae": ["10", 4], "positive": ["10", 1], "negative": ["10", 2], "bbox_detector": ["90", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "90": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "91": {"inputs": {"task": "region to description", "text_input": "", "max_new_tokens": 1024, "num_beams": 3, "do_sample": false, "fill_mask": false, "FLORENCE2": ["92", 0], "image": ["97", 0]}, "class_type": "Florence2", "_meta": {"title": "Florence2"}}, "92": {"inputs": {"version": "large-ft"}, "class_type": "LoadFlorence2Model", "_meta": {"title": "Load Florence2 Model"}}, "93": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "Comic caricature illustration, (solo:1.3),(full body:1.3), Exaggerated features with oversized head and eyes,  Hyper-realistic vinyl toy appearance, Caricature art, oversized features, clear background ", "text_b": ["91", 1], "text_c": "", "speak_and_recognation": true}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "95": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["97", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "96": {"inputs": {"anything": ["50", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean GPU Used"}}, "97": {"inputs": {"width": 768, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["20", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}}