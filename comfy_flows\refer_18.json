{"1": {"inputs": {"image": "refer_18.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "5": {"inputs": {"pixels": ["8", 0], "vae": ["211", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAEEncode"}}, "6": {"inputs": {"expand": -5, "tapered_corners": true, "mask": ["137", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "7": {"inputs": {"expand": -5, "tapered_corners": true, "mask": ["134", 0]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "8": {"inputs": {"max_width": ["242", 1], "max_height": ["242", 2], "min_width": 0, "min_height": 0, "crop_if_required": "no", "images": ["242", 0]}, "class_type": "ConstrainImage|pysssss", "_meta": {"title": "Constrain Image 🐍"}}, "9": {"inputs": {"upscale_model": ["10", 0], "image": ["24", 5]}, "class_type": "ImageUpscaleWithModel", "_meta": {"title": "ImageUpscaleWithModel"}}, "10": {"inputs": {"model_name": "放大模型4xNomos8kSCHAT-L_放大模型4xNomos8kSCHAT-L_4xNomos8kSCHAT-.pth"}, "class_type": "UpscaleModelLoader", "_meta": {"title": "UpscaleModelLoader"}}, "11": {"inputs": {"upscale_method": "nearest-exact", "width": ["45", 0], "height": ["45", 1], "crop": "disabled", "image": ["9", 0]}, "class_type": "ImageScale", "_meta": {"title": "ImageScale"}}, "12": {"inputs": {"pixels": ["11", 0], "vae": ["211", 2]}, "class_type": "VAEEncode", "_meta": {"title": "ImageScale"}}, "13": {"inputs": {"mask": ["7", 0]}, "class_type": "MaskToImage", "_meta": {"title": "MaskToImage"}}, "14": {"inputs": {"upscale_method": "nearest-exact", "width": ["45", 0], "height": ["45", 1], "crop": "disabled", "image": ["13", 0]}, "class_type": "ImageScale", "_meta": {"title": "ImageScale"}}, "15": {"inputs": {"pixels": ["41", 0], "vae": ["211", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAEEncode"}}, "16": {"inputs": {"channel": "red", "image": ["14", 0]}, "class_type": "ImageToMask", "_meta": {"title": "ImageToMask"}}, "17": {"inputs": {"x": 0, "y": 0, "operation": "add", "destination": ["7", 0], "source": ["6", 0]}, "class_type": "MaskComposite", "_meta": {"title": "MaskComposite"}}, "18": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["12", 0], "source": ["15", 0], "mask": ["16", 0]}, "class_type": "LatentCompositeMasked", "_meta": {"title": "LatentCompositeMasked"}}, "21": {"inputs": {"positive": ["251", 0], "negative": ["252", 0], "cnet_stack": ["27", 0]}, "class_type": "Apply ControlNet Stack", "_meta": {"title": "Apply ControlNet Stack"}}, "23": {"inputs": {"prompt": "(deformed iris, deformed pupils, semi-realistic, cgi, 3d, render, sketch, cartoon, drawing, anime:1.4),text,close up,cropped,out of frame,worst quality,low quality,jpeg artifacts,ugly,duplicate,morbid,mutilated,extra fingers,mutated hands,poorly drawn hands,poorly drawn face,mutation,deformed,blurry,dehydrated,bad anatomy,bad proportions,extra limbs,cloned face,disfigured,gross proportions,malformed limbs,missing arms,missing legs,extra arms,extra legs,fused fingers,too many fingers,(long neck),"}, "class_type": "CR Prompt Text", "_meta": {"title": "⚙️ CR Prompt Text"}}, "24": {"inputs": {"seed": 184018421807570, "steps": 35, "cfg": 6, "sampler_name": "dpmpp_2m_sde", "scheduler": "karras", "denoise": 0.6, "preview_method": "auto", "vae_decode": "true", "model": ["85", 0], "positive": ["21", 0], "negative": ["21", 1], "latent_image": ["29", 0], "optional_vae": ["211", 2]}, "class_type": "KSampler (Efficient)", "_meta": {"title": "BasicScheduler"}}, "25": {"inputs": {"seed": 279673387026107, "steps": 10, "cfg": 7, "sampler_name": "ddim", "scheduler": "ddim_uniform", "denoise": 0.35000000000000003, "preview_method": "auto", "vae_decode": "true", "model": ["24", 0], "positive": ["24", 1], "negative": ["24", 2], "latent_image": ["31", 0], "optional_vae": ["211", 2]}, "class_type": "KSampler (Efficient)", "_meta": {"title": "KSampler (Efficient)"}}, "27": {"inputs": {"switch_1": "On", "controlnet_1": "control_v11p_sd15_lineart_fp16.safetensors", "controlnet_strength_1": 0.9, "start_percent_1": 0, "end_percent_1": 1, "switch_2": "On", "controlnet_2": "control_v11f1p_sd15_depth_fp16.safetensors", "controlnet_strength_2": 0.51, "start_percent_2": 0, "end_percent_2": 0.45, "switch_3": "Off", "controlnet_3": "None", "controlnet_strength_3": 0, "start_percent_3": 0, "end_percent_3": 0, "image_1": ["244", 0], "image_2": ["243", 0]}, "class_type": "CR Multi-ControlNet Stack", "_meta": {"title": "🕹️ CR Multi-ControlNet Stack"}}, "28": {"inputs": {"image_gen_width": ["45", 0], "image_gen_height": ["45", 1], "resize_mode": "Resize and Fill", "original_image": ["8", 0]}, "class_type": "PixelPerfectResolution", "_meta": {"title": "Pixel Perfect Resolution"}}, "29": {"inputs": {"samples": ["5", 0], "mask": ["30", 0]}, "class_type": "SetLatentNoiseMask", "_meta": {"title": "SetLatentNoiseMask"}}, "30": {"inputs": {"mask": ["17", 0]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "31": {"inputs": {"samples": ["18", 0], "mask": ["32", 0]}, "class_type": "SetLatentNoiseMask", "_meta": {"title": "SetLatentNoiseMask"}}, "32": {"inputs": {"mask": ["16", 0]}, "class_type": "InvertMask", "_meta": {"title": "InvertMask"}}, "37": {"inputs": {"delimiter": ",", "clean_whitespace": "true", "text_a": ["237", 0], "text_b": ["124", 2]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "39": {"inputs": {"text_positive": ["37", 0], "text_negative": ["23", 0], "style": "base", "log_prompt": false, "style_positive": true, "style_negative": true}, "class_type": "SDXLPromptStyler", "_meta": {"title": "SDXL Prompt Styler"}}, "41": {"inputs": {"max_width": ["245", 0], "max_height": ["246", 0], "min_width": 0, "min_height": 0, "crop_if_required": "no", "images": ["242", 0]}, "class_type": "ConstrainImage|pysssss", "_meta": {"title": "Constrain Image 🐍"}}, "45": {"inputs": {"image": ["41", 0]}, "class_type": "GetImageSize+", "_meta": {"title": "🔧 Get Image Size"}}, "48": {"inputs": {"preset": "PLUS (high strength)", "model": ["211", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "85": {"inputs": {"weight": 0.8, "weight_type": "ease in", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["48", 0], "ipadapter": ["48", 1], "image": ["90", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "90": {"inputs": {"interpolation": "LANCZOS", "crop_position": "pad", "sharpening": 0.1, "image": ["238", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "124": {"inputs": {"model": "promptgen_base_v2.0", "folder_path": "Path to your image folder", "caption_method": "extra_mixed", "max_new_tokens": 1024, "num_beams": 4, "random_prompt": "never", "prefix_caption": "", "suffix_caption": "", "replace_tags": "replace_tags eg:search1:replace1;search2:replace2", "images": ["238", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Tagger", "_meta": {"title": "🐾MiaoshouAI Tagger"}}, "129": {"inputs": {"sam_model": "sam_vit_h (2.56GB)", "grounding_dino_model": "GroundingDINO_SwinT_OGC (694MB)", "threshold": 0.3, "detail_method": "VITMatte(local)", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.15, "white_point": 0.99, "process_detail": true, "prompt": "face", "device": "cuda", "max_megapixels": 2, "cache_model": false, "image": ["1", 0]}, "class_type": "LayerMask: SegmentAnythingUltra V2", "_meta": {"title": "LayerMask: SegmentAnythingUltra V2(Advance)"}}, "130": {"inputs": {"method": "VITMatte(local)", "mask_grow": 0, "fix_gap": 0, "fix_threshold": 0.75, "edge_erode": 6, "edte_dilate": 6, "black_point": 0.01, "white_point": 0.99, "device": "cuda", "max_megapixels": 2, "image": ["129", 0], "mask": ["129", 1]}, "class_type": "LayerMask: MaskEdgeUltraDetail V2", "_meta": {"title": "LayerMask: MaskEdgeUltraDetail V2"}}, "134": {"inputs": {"erode_dilate": 4, "fill_holes": 4, "remove_isolated_pixels": 4, "smooth": 8, "blur": 8, "mask": ["130", 1]}, "class_type": "MaskFix+", "_meta": {"title": "🔧 Mask Fix"}}, "137": {"inputs": {"erode_dilate": 4, "fill_holes": 4, "remove_isolated_pixels": 6, "smooth": 3, "blur": 3, "mask": ["139", 1]}, "class_type": "MaskFix+", "_meta": {"title": "🔧 Mask Fix"}}, "138": {"inputs": {"sam_model": "sam_vit_h (2.56GB)", "grounding_dino_model": "GroundingDINO_SwinT_OGC (694MB)", "threshold": 0.3, "detail_method": "VITMatte(local)", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.15, "white_point": 0.99, "process_detail": true, "prompt": "hair", "device": "cuda", "max_megapixels": 2, "cache_model": false, "image": ["1", 0]}, "class_type": "LayerMask: SegmentAnythingUltra V2", "_meta": {"title": "LayerMask: SegmentAnythingUltra V2(Advance)"}}, "139": {"inputs": {"method": "VITMatte(local)", "mask_grow": 0, "fix_gap": 0, "fix_threshold": 0.75, "edge_erode": 6, "edte_dilate": 6, "black_point": 0.01, "white_point": 0.99, "device": "cuda", "max_megapixels": 2, "image": ["138", 0], "mask": ["138", 1]}, "class_type": "LayerMask: MaskEdgeUltraDetail V2", "_meta": {"title": "LayerMask: MaskEdgeUltraDetail V2"}}, "211": {"inputs": {"ckpt_name": "majicMIX realistic 麦橘写实_v7.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Checkpoint加载器（简易）"}}, "212": {"inputs": {"model_path": "iclight_sd15_fc.safetensors", "model": ["211", 0]}, "class_type": "LoadAndApplyICLightUnet", "_meta": {"title": "Load And Apply IC-Light"}}, "213": {"inputs": {"text": "Soft lighting effect", "clip": ["211", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIPTextEncode"}}, "214": {"inputs": {"text": "lowres, bad anatomy, bad hands, cropped, worst quality", "clip": ["211", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIPTextEncode"}}, "217": {"inputs": {"rem_mode": "RMBG-1.4", "image_output": "Preview", "save_prefix": "ComfyUI", "torchscript_jit": false, "add_background": "none", "images": ["25", 5]}, "class_type": "easy imageRemBg", "_meta": {"title": "easy imageRemBg"}}, "218": {"inputs": {"samples": ["25", 3], "vae": ["211", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAEDecode"}}, "219": {"inputs": {"image": ["217", 0]}, "class_type": "SplitImageWithAlpha", "_meta": {"title": "SplitImageWithAlpha"}}, "220": {"inputs": {"x": 0, "y": 0, "resize_source": false, "destination": ["218", 0], "source": ["219", 0], "mask": ["217", 1]}, "class_type": "ImageCompositeMasked", "_meta": {"title": "ImageCompositeMasked"}}, "221": {"inputs": {"image": ["217", 0], "alpha": ["217", 1]}, "class_type": "ICLightApplyMaskGrey", "_meta": {"title": "ICLightApplyMaskGrey"}}, "222": {"inputs": {"pixels": ["221", 0], "vae": ["211", 2]}, "class_type": "VAEEncodeArgMax", "_meta": {"title": "VAEEncodeArgMax"}}, "223": {"inputs": {"multiplier": 0.18215, "positive": ["213", 0], "negative": ["214", 0], "vae": ["211", 2], "foreground": ["222", 0]}, "class_type": "ICLightConditioning", "_meta": {"title": "IC-Light Conditioning"}}, "224": {"inputs": {"samples": ["225", 0], "vae": ["211", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAEDecode"}}, "225": {"inputs": {"seed": 648332228250673, "steps": 25, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 0.45, "model": ["226", 0], "positive": ["223", 0], "negative": ["223", 1], "latent_image": ["227", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "226": {"inputs": {"preset": "PLUS (high strength)", "lora_strength": 0.6, "provider": "CPU", "weight": 1, "weight_faceidv2": 1, "start_at": 0, "end_at": 1, "cache_mode": "all", "use_tiled": false, "model": ["212", 0], "image": ["220", 0], "attn_mask": ["217", 1]}, "class_type": "easy ipadapterApply", "_meta": {"title": "easy ipadapterApply"}}, "227": {"inputs": {"pixels": ["220", 0], "vae": ["211", 2]}, "class_type": "VAEEncodeArgMax", "_meta": {"title": "VAEEncodeArgMax"}}, "230": {"inputs": {"mode": "add", "blur_sigma": 1, "blend_factor": 1, "target": ["224", 0], "source": ["219", 0]}, "class_type": "DetailTransfer", "_meta": {"title": "Detail Transfer"}}, "232": {"inputs": {"anything": ["230", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "easy cleanGpuUsed"}}, "235": {"inputs": {"filename_prefix": "refer/swap", "images": ["230", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "237": {"inputs": {"from_translate": "chinese simplified", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "MyMemoryTranslator [free]", "text": "realistic hyperdetalization,real real photography,\nmake up,lips,", "Show proxy": "proxy_hide", "Show authorization": "authorization_hide"}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "UserPrompt"}}, "238": {"inputs": {"width": ["239", 0], "height": ["239", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "width_input": ["239", 0], "height_input": ["239", 1], "crop": "center", "image": ["240", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "239": {"inputs": {"width": 1024, "height": 1024, "aspect_ratio": "1:1 square 1024x1024", "swap_dimensions": "Off", "upscale_factor": 1, "batch_size": 1}, "class_type": "CR SDXL Aspect Ratio", "_meta": {"title": "AspectRatio"}}, "240": {"inputs": {"image": "input/template/487.png", "force_size": "Disabled", "custom_width": 512, "custom_height": 512}, "class_type": "VHS_LoadImagePath", "_meta": {"title": "LoadImageFromPath"}}, "242": {"inputs": {"width": ["239", 0], "height": ["239", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "width_input": ["239", 0], "height_input": ["239", 1], "crop": "center", "image": ["1", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "243": {"inputs": {"preprocessor": "DepthAnythingV2Preprocessor", "resolution": ["28", 0], "image": ["8", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "244": {"inputs": {"preprocessor": "AnyLineArtPreprocessor_aux", "resolution": ["28", 0], "image": ["8", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "245": {"inputs": {"a": ["242", 1], "b": 2, "operation": "multiply"}, "class_type": "easy mathInt", "_meta": {"title": "Math Int"}}, "246": {"inputs": {"a": ["242", 2], "b": 2, "operation": "multiply"}, "class_type": "easy mathInt", "_meta": {"title": "Math Int"}}, "251": {"inputs": {"text": ["39", 0], "clip": ["211", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "252": {"inputs": {"text": ["39", 1], "clip": ["211", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}}