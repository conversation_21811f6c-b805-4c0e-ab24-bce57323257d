from pydantic import BaseModel, <PERSON>, validator
from ..utils import translate_post, contains_zhCN 
from ..utils import OssAddressEmptyException, PromptTooLongException, PromptIsEmptyException, ImageEntityEmptyException
from ..utils.redis_stock import redisStock

class ImageScore(BaseModel):
    loc : str = Field(..., example="https://byjh-ai-data.oss-rg-china-mainland.aliyuncs.com/image/hornet.png")
  
    @validator("loc")
    def loc_validator(cls, value, values):
        if value == "":
            raise OssAddressEmptyException()
        return value
    
    
class ImageInfo(BaseModel):
    loc : str = Field(..., example="https://byjh-ai-data.oss-rg-china-mainland.aliyuncs.com/image/hornet.png")
    prompt: str = Field(..., example="一个漂亮的女孩")

    @property
    def prompt_en(self):
        if contains_zhCN(self.prompt):
            self.prompt = redisStock.stripSensitive(self.prompt)
            value = translate_post("en", self.prompt)
        else:
            value = self.prompt
        return value
    
    @validator("loc")
    def loc_validator(cls, value, values):
        if value == "":
            raise OssAddressEmptyException()
        return value
    
    @validator("prompt")
    def prompt_validator(cls, value, values):
        if value == "":
            raise PromptIsEmptyException()
        if len(value) > 100:
            raise PromptTooLongException()
        return value

class InstructInfo(BaseModel):
    loc : str = Field(..., example="https://byjh-ai-data.oss-rg-china-mainland.aliyuncs.com/image/hornet.png")
    prompt : str = Field(..., example="将天空改为红色")
    infer_steps : int = Field(..., example=10)
    guidance_scale : int = Field(..., example=1)

    @property
    def prompt_en(self):
        if contains_zhCN(self.prompt):
            value = translate_post("en", self.prompt)
        else:
            value = self.prompt
        return value
    
    @validator("loc")
    def loc_validator(cls, value, values):
        if value == "":
            raise OssAddressEmptyException()
        return value

    @validator("prompt")
    def prompt_validator(cls, value, values):
        if value == "":
            raise PromptIsEmptyException()
        if len(value) > 100:
            raise PromptTooLongException()
        
        return value

class ImageCompress(BaseModel):
    img : str = Field(..., example="a beautiful girl in the kitchen...")
    prompt: str = Field(..., example="一个漂亮的女孩")

    @property
    def prompt_en(self):
        if contains_zhCN(self.prompt):
            value = translate_post("en", self.prompt)
        else:
            value = self.prompt
        return value
    
    @validator("img")
    def loc_validator(cls, value, values):
        if value == "":
            raise ImageEntityEmptyException()
        return value
    
    @validator("prompt")
    def prompt_validator(cls, value, values):
        if value == "":
            raise PromptIsEmptyException()
        if len(value) > 100:
            raise PromptTooLongException()
        
        return value
        