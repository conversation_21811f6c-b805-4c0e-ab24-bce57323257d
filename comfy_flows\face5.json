{"4": {"inputs": {"ckpt_name": "DreamShaperXL_Lightning-SFW.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "11": {"inputs": {"image": "0_XIUREN-No.5608-<PERSON>-<PERSON>-<PERSON>-MrCong.com-014.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "19": {"inputs": {"lora_name": "ipadapter/ip-adapter-faceid_sdxl_lora.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "Load <PERSON>", "_meta": {"title": "Load <PERSON>"}}, "21": {"inputs": {"image": "6519e1d9b9d3ce5b42c3e6febcc6c1ae.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "22": {"inputs": {"enabled": true, "swap_model": "inswapper_128.onnx", "facedetection": "retinaface_resnet50", "face_restore_model": "codeformer-v0.1.0.pth", "face_restore_visibility": 1, "codeformer_weight": 0.5, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "input_image": ["65", 0], "source_image": ["25", 0]}, "class_type": "ReActorFaceSwap", "_meta": {"title": "ReActor 🌌 Fast Face Swap"}}, "25": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["11", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "28": {"inputs": {"guide_size": 384, "guide_size_for": true, "max_size": 1024, "seed": 330554663208098, "steps": 8, "cfg": 2, "sampler_name": "ddim", "scheduler": "sgm_uniform", "denoise": 0.35000000000000003, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 10, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 10, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "image": ["22", 0], "model": ["30", 0], "clip": ["19", 1], "vae": ["4", 2], "positive": ["33", 0], "negative": ["34", 0], "bbox_detector": ["35", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "29": {"inputs": {"preset": "FACEID PORTRAIT UNNORM - SDXL only (strong)", "lora_strength": 0.9, "provider": "CPU", "model": ["19", 0]}, "class_type": "IPAdapterUnifiedLoaderFaceID", "_meta": {"title": "IPAdapter Unified Loader FaceID"}}, "30": {"inputs": {"weight": 0.8, "weight_faceidv2": 0.8, "weight_type": "ease in-out", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "K+V", "model": ["29", 0], "ipadapter": ["29", 1], "image": ["25", 0], "clip_vision": ["32", 0]}, "class_type": "IPAdapterFaceID", "_meta": {"title": "IPAdapter FaceID"}}, "32": {"inputs": {"clip_name": "CLIP_ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "33": {"inputs": {"text": "perfect face", "clip": ["19", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "34": {"inputs": {"text": "embedding:ng_deepnegative_v1_75t, ugly ", "clip": ["19", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "35": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "58": {"inputs": {"facedetection": "retinaface_resnet50", "model": "codeformer-v0.1.0.pth", "visibility": 1, "codeformer_weight": 0.9, "image": ["28", 0]}, "class_type": "ReActorRestoreFace", "_meta": {"title": "Restore Face 🌌 ReActor"}}, "63": {"inputs": {"filename_prefix": "face5/face5", "images": ["58", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "65": {"inputs": {"width": 768, "height": 1024, "position": "center", "x_offset": 0, "y_offset": 0, "image": ["21", 0]}, "class_type": "ImageCrop+", "_meta": {"title": "🔧 Image Crop"}}}