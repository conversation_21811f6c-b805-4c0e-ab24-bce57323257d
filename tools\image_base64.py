#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import sys, getopt
from PIL import Image, PngImagePlugin
import base64
from pathlib import Path
import os
import io 
import argparse
import piexif
import piexif.helper

def parse_args():
    desc = "encode image to base64 string format"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--img', type=str, default='image file', help='[image file to be encoded]')
    parser.add_argument('--out', type=str, default='base64 result to a file', help='[base64 str to be saved]')
    return parser.parse_args()

def encode_pil_to_base64(image, ext:str):

    with io.BytesIO() as output_bytes:

        if ext.lower() == 'png':
            use_metadata = False
            metadata = PngImagePlugin.PngInfo()
            for key, value in image.info.items():
                if isinstance(key, str) and isinstance(value, str):
                    metadata.add_text(key, value)
                    use_metadata = True
            image.save(output_bytes, format="PNG", pnginfo=(metadata if use_metadata else None), quality=80)

        elif ext.lower() in ("jpg", "jpeg", "webp"):
            parameters = image.info.get('parameters', None)
            exif_bytes = piexif.dump({
                "Exif": { piexif.ExifIFD.UserComment: piexif.helper.UserComment.dump(parameters or "", encoding="unicode") }
            })
            
            image.save(output_bytes, format="JPEG", exif = exif_bytes, quality=80)
        else:
            raise Exception("can not determinate the iamge file format")

        bytes_data = output_bytes.getvalue()

    return base64.b64encode(bytes_data)

def process_file(image:str, out:str):

    ext = os.path.splitext(image)[-1][1:]
    img = Image.open(image)
    b64str = encode_pil_to_base64(img, ext)
    with open(out, "w") as f:
        f.write(b64str.decode("utf-8"))

def main():
    args = parse_args()
    process_file(args.img, args.out)

if __name__ == "__main__":
    #python .\tools\image_base64.py --img D:\train-material\a.png --out D:\train-material\base64img.txt
    main()
