
#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from transformers import AutoFeatureExtractor

from diffusers.pipelines.stable_diffusion.safety_checker import StableDiffusionSafetyChecker
from PIL import Image
import numpy as np
import torch
import argparse
import numpy as np


proxies_dict = {"http":"http://127.0.0.1:1080", "https":"http://127.0.0.1:1080"}
safety_model_id = "CompVis/stable-diffusion-safety-checker"
safety_cache_dir="./models/safety-checker"

safety_feature_extractor = AutoFeatureExtractor.from_pretrained(safety_model_id, resume_download=True, cache_dir=safety_cache_dir, proxies=proxies_dict, torch_dtype=torch.float16)

safety_checker = StableDiffusionSafetyChecker.from_pretrained(safety_model_id,  resume_download=True, cache_dir=safety_cache_dir, proxies=proxies_dict, torch_dtype=torch.float16).to("cuda")

def check_safety(x_image):

    safety_checker_input = safety_feature_extractor(numpy_to_pil(x_image), return_tensors="pt").to("cuda")
    x_checked_image, has_nsfw_concept = safety_checker(images=x_image, clip_input=safety_checker_input.pixel_values)
    assert x_checked_image.shape[0] == len(has_nsfw_concept)
    for i in range(len(has_nsfw_concept)):
        if has_nsfw_concept[i]:
            x_checked_image[i] = load_replacement(x_checked_image[i])
    return x_checked_image, has_nsfw_concept

def numpy_to_pil(images):
    """
    Convert a numpy image or a batch of images to a PIL image.
    """
    if images.ndim == 3:
        images = images[None, ...]
    images = (images * 255).round().astype("uint8")
    pil_images = [Image.fromarray(image) for image in images]

    return pil_images

def pil_to_numpy(images):
    """
    Convert a pil image or a batch of images to a numpy image.
    """

    if type(images) == list:
        images = [np.array(image)/255.0 for image in images] 
    else:
        images = [np.array(images)/255.0]
    return np.array(images)

def load_replacement(x):
    try:
        hwc = x.shape
        y = Image.open("./static/nsfw/nsfw.png").convert("RGB").resize((hwc[1], hwc[0]))
        y = (np.array(y)/255.0).astype(x.dtype)
        assert y.shape == x.shape
        return y
    except Exception as e:
        return x

def parse_args():
    desc = "encode image to base64 string format"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--img', type=str, default='image file', help='[image file to be encoded]')
    parser.add_argument('--out', type=str, default='base64 result to a file', help='[base64 str to be saved]')
    return parser.parse_args()

def process_file(image:str, out:str):

    img = Image.open(image).convert("RGB")
    checked_img, has_nsfw_concept = check_safety(pil_to_numpy(img))

    img = numpy_to_pil(checked_img)[0]
    img.save(out)

def main():
    args = parse_args()
    process_file(args.img, args.out)

if __name__ == "__main__":
    #python .\tools\test_safety_checker.py --img D:\train-material\dangerous.png --out D:\train-material\nsfw.png
    main()

