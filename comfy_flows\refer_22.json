{"1": {"inputs": {"vae_name": "flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "12": {"inputs": {"image": "14186_17404509563766942.jpg"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "16": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5-v1_1-xxl-encoder-Q5_K_S.gguf", "type": "flux"}, "class_type": "DualCLIPLoaderGGUF", "_meta": {"title": "DualCLIPLoader (GGUF)"}}, "17": {"inputs": {"unet_name": "flux1-dev-Q4_K_S.gguf", "dequant_dtype": "bfloat16", "patch_dtype": "bfloat16", "patch_on_device": false}, "class_type": "UnetLoaderGGUFAdvanced", "_meta": {"title": "Unet Loader (GGUF/Advanced)"}}, "18": {"inputs": {"width": ["62", 0], "height": ["62", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": 0, "image": ["12", 0], "height_input": ["62", 1]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "42": {"inputs": {"clip_l": ["271", 0], "t5xxl": ["271", 0], "guidance": 3.5, "speak_and_recognation": {"__value__": [false, true]}, "clip": ["272", 1]}, "class_type": "CLIPTextEncodeFlux", "_meta": {"title": "CLIPTextEncodeFlux"}}, "62": {"inputs": {"width": 1024, "height": 1024, "aspect_ratio": "3:4 portrait 896x1152", "swap_dimensions": "Off", "upscale_factor": 1, "batch_size": 1}, "class_type": "CR SDXL Aspect Ratio", "_meta": {"title": "AspectRatio"}}, "68": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["18", 1], "height": ["18", 2], "model": ["272", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "86": {"inputs": {"lora_name": "flux/Hyper-FLUX.1-dev-8steps-lora.safetensors", "strength_model": 0.13, "strength_clip": 0.13, "model": ["17", 0], "clip": ["16", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "145": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "146": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "147": {"inputs": {"provider": "CPU"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "148": {"inputs": {"weight": 0.8, "start_at": 0, "end_at": 1, "model": ["68", 0], "pulid_flux": ["145", 0], "eva_clip": ["146", 0], "face_analysis": ["147", 0], "image": ["149", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "149": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["238", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "197": {"inputs": {"from_translate": "chinese simplified", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "MyMemoryTranslator [free]", "text": "夫人小我十岁我知她勇敢无畏", "hide_proxy": "proxy_hide", "hide_authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "UserPrompt"}}, "202": {"inputs": {"lora_name": "flux/aidmaFLUXPro1.1-FLUX-v0.3.safetensors", "strength_model": 0.9, "strength_clip": 0.9, "model": ["86", 0], "clip": ["86", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "205": {"inputs": {"samples": ["206", 0], "vae": ["1", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "206": {"inputs": {"noise": ["210", 0], "guider": ["209", 0], "sampler": ["207", 0], "sigmas": ["208", 0], "latent_image": ["262", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "207": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "208": {"inputs": {"scheduler": "simple", "steps": 8, "denoise": 1, "model": ["267", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "209": {"inputs": {"model": ["267", 0], "conditioning": ["258", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "210": {"inputs": {"noise_seed": 1036007160279938}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "214": {"inputs": {"filename_prefix": "refer/flux", "images": ["205", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "215": {"inputs": {"anything": ["205", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "238": {"inputs": {"detail_method": "PyMatting", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.01, "white_point": 0.99, "process_detail": true, "device": "cuda", "max_megapixels": 2, "image": ["18", 0]}, "class_type": "LayerMask: RmBgUltra V2", "_meta": {"title": "LayerMask: RmBgUltra V2"}}, "256": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "257": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "258": {"inputs": {"strength": 0.05, "strength_type": "attn_bias", "conditioning": ["42", 0], "style_model": ["256", 0], "clip_vision_output": ["265", 0]}, "class_type": "StyleModelApply", "_meta": {"title": "Apply Style Model"}}, "262": {"inputs": {"width": ["18", 1], "height": ["18", 2], "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "265": {"inputs": {"crop": "center", "clip_vision": ["257", 0], "image": ["18", 0]}, "class_type": "CLIPVisionEncode", "_meta": {"title": "CLIP Vision Encode"}}, "266": {"inputs": {"model": ["148", 0]}, "class_type": "FluxForwardOverrider", "_meta": {"title": "FluxForwardOverrider"}}, "267": {"inputs": {"rel_l1_thresh": 0.25, "cache_device": "offload_device", "wan_coefficients": "disabled", "model": ["266", 0]}, "class_type": "ApplyTeaCachePatch", "_meta": {"title": "ApplyTeaCachePatch"}}, "271": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "anime", "text_b": ["197", 0], "text_c": "", "speak_and_recognation": {"__value__": [false, true]}, "result": "anime, <PERSON><PERSON>, when I was ten years old, I knew she was brave and fearless."}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "272": {"inputs": {"lora_name": "flux/you_write_i_draw/手办Flux.safetensors", "strength_model": 0.9, "strength_clip": 0.9, "model": ["202", 0], "clip": ["202", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}}