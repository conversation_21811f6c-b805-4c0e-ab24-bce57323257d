#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from pathlib import Path
from functools import lru_cache
try:
    from pydantic import BaseSettings
except Exception as e:
    from pydantic_settings import BaseSettings 

# 文档：https://pydantic-docs.helpmanual.io/usage/settings/

class Settings(BaseSettings):
    class Config:
        # 环境变量文件
        env_file = ".env"

    # debug模式
    debug: bool = True

    # 项目标题
    project_title:str = 'FastAPI AI SERVER BACKEND'
    # 项目描述
    project_description:str = '人工&智能'
    # 项目版本
    project_version:str = '0.0.1'

    # url的前缀
    url_prefix: str = "/api/v1"
    # host
    server_host: str = '0.0.0.0'
    server_port: int = 8765

    #  swagger docs 的登陆重定向地址

    # 项目根目录
    base_dir: str = Path(__file__).absolute().parent.parent.parent.absolute().as_posix()
    # 日志目录
    log_dir: str = base_dir + '/logs'
    # 静态资源
    static_dir: str = base_dir + '/static'
    static_url_prefix: str = '/static'
    # 用户上传目录
    media_dir: str = base_dir + '/media'
    media_url_prefix: str = '/media'
    # jinja2 模板目录
    jinja2_templates_dir: str = base_dir +'/notebook/templates'

    OSS_ENDPOINT: str = "oss-rg-china-mainland.aliyuncs.com"
    OSS_SECRETKEY: str = "LTAI5tBWs1EknMeactnaxhHK"
    OSS_SECRETPASS: str = "******************************"
    OSS_BUCKET: str = "byjh-ai-data"
    OSS_VOICE_PREFIX: str = "voice"
    OSS_IMAGE_PREFIX: str = "voice"
    VOICE_REGION: str = "cn-shanghai"
    VOICE_APPKEY: str = "ZxzHwCBuNE0dMxYI"
    VOICE_DOMAIN: str = "nls-meta.cn-shanghai.aliyuncs.com"
    VOICE_URL: str = "wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1"
    
    jwt_secret: str = "banyunjuhe_ai_data_&adf*^"
    jwt_algorithm: str ="HS256"
    google_translate_credentials: str = "AIzaSyCo2Jly7begyK0X4UDBmIOsxW5mpEGAejI"
    google_translate_proxy: dict = {"http":"http://127.0.0.1:1080", "https":"http://127.0.0.1:1080"}

    sd_base_url: str = "http://127.0.0.1:7860"
    comfy_base_url: str = "127.0.0.1:8188"
    
    redis_host: str = "r-2zev703ntwvbh6bndb.redis.rds.aliyuncs.com"
    redis_port: str = "6379"
    redis_pwd: str = "^PBC@byjh$"
    redis_db: str = "0"

    upload_dir: str = base_dir + "/dataset"
    comfy_dir: str = base_dir + "/comfy_flows"
    comfy_replace_img: list = ["safety_crop/32.png","safety_crop/42.jpg","safety_crop/52.jpg","safety_crop/61.png","safety_crop/72.png","safety_crop/81.png","safety_crop/91.png"]
    
def get_settings():
    settings = Settings()
    return settings
    
