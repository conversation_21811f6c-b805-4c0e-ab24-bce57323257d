#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import typing
from .resp import RespCode

class AIException(Exception):
    def __init__(
        self,
        code: int,
        detail: typing.Optional[str] = None
    ) -> None:
       
        self.code = code
        self.detail = detail
       

    def __repr__(self) -> str:
        class_name = self.__class__.__name__
        return f"{class_name}(status_code={self.status_code!r}, detail={self.detail!r})"
    

class PromptTooLongException(AIException):
    def __init__(self) -> None:
        super().__init__(RespCode.PROMPT_TOO_LONG, "提示词不能超过100个字符")

class PromptIsEmptyException(AIException):
    def __init__(self) -> None:
        super().__init__(RespCode.PROMPT_IS_EMTPY, "提示词不能为空")

class OssAddressEmptyException(AIException):
    def __init__(self) -> None:
        super().__init__(RespCode.OSS_ADDRESS_EMPTY, "oss地址不能为空")

class ImageEntityEmptyException(AIException):
    def __init__(self) -> None:
        super().__init__(RespCode.IMAGE_ENTITY_EMPTY, "图片不能为空")

class PasswordMustSameException(AIException):
    def __init__(self) -> None:
        super().__init__(RespCode.PASSWORD_MUST_SAME, "两个密码必须一致")

class UsernameIsEmptyException(AIException):
    def __init__(self) -> None:
        super().__init__(RespCode.PASSWORD_MUST_SAME, "用户名不能为空")

class ImageFaceEmptyException(AIException):
    def __init__(self) -> None:
        super().__init__(RespCode.IMAGE_FACE_EMPTY, "未能检测到人脸")

