import requests
import base64
import os
import re 
import json
import random
from PIL import Image, ImageOps, PngImagePlugin, ImageDraw, ImageFont
import io
import piexif
import piexif.helper
from starlette.exceptions import HTTPException
from .safety_checker_lib import check_safety, pil_to_numpy, numpy_to_pil, add_visible_tags
from ..config import settings

from .exception import ImageFaceEmptyException

from ultralytics import YOL<PERSON>
from supervision import Detections
yolo_path = "./models/models--arnabdhar--YOLOv8-Face-Detection/snapshots/4094ffaba7a6e243801ddb7f5f9a1fae0cf4d78b/model.pt"
yolo_model = YOLO(yolo_path)

common_negative = "(nsfw:1.3), EasyNegative, lowres, worst quality, low quality, watermark, username, blurry"

def url_img2img():
    return f"{settings.sd_base_url}/sdapi/v1/img2img"

def url_txt2img():
    return f"{settings.sd_base_url}/sdapi/v1/txt2img"

def url_interrogate():
    return f"{settings.sd_base_url}/sdapi/v1/interrogate"

def url_reactor():
    return f"{settings.sd_base_url}/reactor/image"

def simple_txt2img_request():
    return {
        "batch_size": 1,
        "cfg_scale": 7,
        "denoising_strength": 0.65,
        "enable_hr": False,
        "eta": 0,
        "firstphase_height": 0,
        "firstphase_width": 0,
        "height": 768,
        "width": 512,
        "n_iter": 1,
        "negative_prompt": "",
        "prompt": "example prompt",
        "restore_faces": True,
        "s_churn": 0,
        "s_noise": 1,
        "s_tmax": 0,
        "s_tmin": 0,
        "sampler_index": "DPM++ 3M SDE Exponential",
        "seed": -1,
        "seed_resize_from_h": -1,
        "seed_resize_from_w": -1,
        "steps": 20,
        "styles": [],
        "subseed": -1,
        "subseed_strength": 0,
        "tiling": False,
        "alwayson_scripts": {},
    }

def simple_img2img_request():
    return {
        "batch_size": 1,
        "cfg_scale": 7,
        "denoising_strength": 0.65,
        "eta": 0,
        "height": 768,
        "include_init_images": True,
        "init_images": [],
        "inpaint_full_res": False,
        "inpaint_full_res_padding": 0,
        "inpainting_fill": 0,
        "inpainting_mask_invert": False,
        "mask": None,
        "mask_blur": 4,
        "n_iter": 1,
        "negative_prompt": "",
        "override_settings": {},
        "prompt": "",
        "resize_mode": 0,
        "restore_faces": True,
        "s_churn": 0,
        "s_noise": 1,
        "s_tmax": 0,
        "s_tmin": 0,
        "sampler_index": "DPM++ 3M SDE Exponential",
        "seed": -1,
        "seed_resize_from_h": -1,
        "seed_resize_from_w": -1,
        "steps": 20,
        "styles": [],
        "subseed": -1,
        "subseed_strength": 0,
        "tiling": False,
        "width": 512,
        "alwayson_scripts": {},
    }

def sample_controlnet_unit():
    """
    "input_image" : image to use in this unit. defaults to null
    "mask" : mask pixel_perfect to filter the image. defaults to null
    "module" : preprocessor to use on the image passed to this unit before using it for conditioning. accepts values returned by the /controlnet/module_list route. defaults to "none"
    "model" : name of the model to use for conditioning in this unit. accepts values returned by the /controlnet/model_list route. defaults to "None"
    "weight" : weight of this unit. defaults to 1
    "resize_mode" : how to resize the input image so as to fit the output resolution of the generation. defaults to "Scale to Fit (Inner Fit)". Accepted values:
    0 or "Just Resize" : simply resize the image to the target width/height
    1 or "Scale to Fit (Inner Fit)" : scale and crop to fit smallest dimension. preserves proportions.
    2 or "Envelope (Outer Fit)" : scale to fit largest dimension. preserves proportions.
    "lowvram" : whether to compensate low GPU memory with processing time. defaults to false
    "processor_res" : resolution of the preprocessor. defaults to 64
    "threshold_a" : first parameter of the preprocessor. only takes effect when preprocessor accepts arguments. defaults to 64
    "threshold_b" : second parameter of the preprocessor, same as above for usage. defaults to 64
    "guidance_start" : ratio of generation where this unit starts to have an effect. defaults to 0.0
    "guidance_end" : ratio of generation where this unit stops to have an effect. defaults to 1.0
    "control_mode" : see the related issue for usage. defaults to 0. Accepted values:
    0 or "Balanced" : balanced, no preference between prompt and control model
    1 or "My prompt is more important" : the prompt has more impact than the model
    2 or "ControlNet is more important" : the controlnet model has more impact than the prompt
    "pixel_perfect" : enable pixel-perfect preprocessor. defaults to false
    """
    controlnet_unit = {
        "enabled": True,
        "input_image": "none",
        "mask": "none",
        "module": "none",
        "model": "none",
        "weight": 1.0,
        "resize_mode": 0,
        "lowvram": "false",
        "processor_res": 64,
        "threshold_a": 64,
        "threshold_b": 64,
        "guidance_start": 0.0,
        "guidance_end": 1.0,
        "control_mode": 0,
        "pixel_perfect": "true"
    }
    return controlnet_unit

def controlnet_args(img:str):
    
    depth_args = sample_controlnet_unit()
    depth_args["module"] = "depth_midas"
    depth_args["model"] = "control_v11f1p_sd15_depth [cfd03158]"
    depth_args["input_image"] = img
    depth_args["mask"] = None
    depth_args["weight"] = 1.0
    depth_args["resize_mode"] = 0
    depth_args["control_mode"] = 2
    
    ip2p_args = sample_controlnet_unit()
    ip2p_args["module"] = "none"
    ip2p_args["model"] = "control_v11e_sd15_ip2p [c4bb465c]"
    ip2p_args["input_image"] = img
    ip2p_args["mask"] = None
    ip2p_args["weight"] = 1.0
    ip2p_args["resize_mode"] = 0
    ip2p_args["control_mode"] = 2
    params = {}
    params["args"] = [depth_args, ip2p_args]
    
    return params 

def sample_adetailer_unit():
    return  {
          "ad_model": "face_yolov8n.pt",
          "ad_prompt": "",
          "ad_negative_prompt": ""
        }

def adetailer_args():

    face_args = sample_adetailer_unit()
    face_args["ad_model"] = "face_yolov8n.pt"
    face_args["ad_prompt"] = f"(best illustration),(best shadow),perfect face,finely detail,masterpiece,ultra-detailed,highres"

    hand_args = sample_adetailer_unit()
    hand_args["ad_model"] = "hand_yolov8n.pt"
    hand_args["ad_prompt"] = "perfect hands"

    params = {}
    params["args"] = [
        True,
        False,
        face_args,
        hand_args
    ]
    return params


def img2img_with_complex_prompt(image:str, prompt:str, negative_prompt:str, width:int, height:int):

    params = simple_img2img_request()
    params["prompt"] = prompt
    params["negative_prompt"] = negative_prompt
    params["width"] = width
    params["height"] = height
    params["init_images"].append(image)
    cntl_args = controlnet_args(image)
    params["alwayson_scripts"]["ControlNet"] = cntl_args
    adtl_args = adetailer_args()
    params["alwayson_scripts"]["ADetailer"] = adtl_args
    
    resp = requests.post(url_img2img(), json=params)
    
    # params = simple_txt2img_request()
    # params["prompt"] = prompt
    # params["negative_prompt"] = negative_prompt
    # params["width"] = width
    # params["height"] = height
    # params["init_images"].append(image)
    # cntl_args = controlnet_args(image)
    # params["alwayson_scripts"]["ControlNet"] = cntl_args
    # adtl_args = adetailer_args()
    # params["alwayson_scripts"]["ADetailer"] = adtl_args
    # resp = requests.post(url_txt2img(), json=params)
        
    
    resp_json = resp.json()
    
    img = resp_json["images"][0]
    img = Image.open(io.BytesIO(base64.b64decode(img)))
    # x_checked_image, has_nsfw_concept, nsfw = check_safety(pil_to_numpy(img))
    # tagged_image = add_visible_tags(numpy_to_pil(x_checked_image)[0])
    
    # 通过负向提示词控制内容安全
    
    tagged_image = add_visible_tags(img) 
    return encode_pil_to_base64(tagged_image, "png").decode("utf-8"), False

def encode_pil_to_base64(image, ext:str):

    with io.BytesIO() as output_bytes:
        if ext.lower() == 'png':
            image.save(output_bytes, format="PNG")
        elif ext.lower() in ("jpg", "jpeg", "webp"):
            image.save(output_bytes, format="JPEG")
        else:
            raise HTTPException(status_code=500, detail="Invalid image format")

        bytes_data = output_bytes.getvalue()

    return base64.b64encode(bytes_data)

def get_resolution(resolution, width, height):
    resolution = int(resolution)
    width = int(width)
    height = int(height)
    if width > height:
        dw = resolution
        dh = int(height / (width / dw))
    else:
        dh = resolution
        dw = int(width / (height / dh))

    return dw, dh

def img2img_from_bytes(image : bytes, prompt:str):
    img = Image.open(io.BytesIO(image))
    (width, height) = img.size
    rwidth, rheight = get_resolution(512, width, height)
    img = img.resize((rwidth, rheight))

    imgb64 = encode_pil_to_base64(img, "png")
    imgb64 = imgb64.decode("utf-8")
    #使用controlnet instructp2p 不再调用blip
    # params = {"image" : imgb64}
    # resp = requests.post(url_interrogate(), json=params)
    # resp_json = resp.json()

    # prompt_blip = resp_json["caption"]
    prompt = "(" + prompt + ")" 
    rs_img, nsfw = img2img_with_complex_prompt(imgb64, prompt, common_negative, rwidth, rheight)
    return rs_img, nsfw, imgb64

def img2img_from_base64(image : str, prompt:str):

    img = Image.open(io.BytesIO(base64.b64decode(image)))
    (width, height) = img.size
    rwidth, rheight = get_resolution(512, width, height)
    img = img.resize((rwidth, rheight))

    imgb64 = encode_pil_to_base64(img, "png")
    imgb64 = imgb64.decode("utf-8")
    rs_img, nsfw = img2img_with_complex_prompt(imgb64, prompt, common_negative, rwidth, rheight)
    return rs_img, nsfw, imgb64

def simple_img_reactor_request():
    return {
    "source_image": "data:image/png;base64,",
    "target_image": "data:image/png;base64,",
    "source_faces_index": [0],
    "face_index": [0],
    "upscaler": "R-ESRGAN 4x+ Anime6B",
    "scale": 1,
    "upscale_visibility": 1,
    "face_restorer": "CodeFormer",
    "restorer_visibility": 1,
    "restore_first": 1,
    "model": "inswapper_128.onnx",
    "gender_source": 0,
    "gender_target": 0,
    "save_to_file": 0,
    "result_file_path": ""
	}

def face_resolution(width, height, dest_width, dest_height):
    
    width = int(width)
    height = int(height)
    if width < height:
        dw = dest_width
        dh = int(height / (width / dw))
    else:
        dh = dest_height
        dw = int(width / (height / dh))

    return dw, dh

def resize(img:Image, dest_width, dest_height):
    (width, height) = img.size
    rwidth, rheight = face_resolution(width, height, dest_width, dest_height)
    img = img.resize((rwidth, rheight))
    (width, height) = img.size

    outputs = yolo_model(img)
    results = Detections.from_ultralytics(outputs[0])

    if len(results.xyxy) > 0:
        face_corner = results.xyxy[0].tolist()
        if width == dest_width:
            left = 0
            right = dest_width
            header = int(face_corner[1])
            if header > dest_height//2:
                upper = header - dest_height//2
                lower = upper + dest_height
            else:      
                upper = 0
                lower = dest_height 
        else:
            upper = 0
            lower = dest_height
            header = int(face_corner[0])
            if header > dest_width // 2:
                left = header - dest_width // 2 
                right = left + dest_width
            else:
                left = 0
                right = dest_width
    else:
        return None
    
    img = img.crop((left, upper, right, lower))
    return img

def img2img_reactor(src_img:bytes, tgt_img:bytes, src_face:int, tgt_face:int):
    
    params = simple_img_reactor_request()

    srcimg = Image.open(io.BytesIO(src_img))
    srcimg = resize(srcimg, 512, 768)
    if srcimg == None:
        raise ImageFaceEmptyException()
    
    srcimgb64 = encode_pil_to_base64(srcimg, "png")
    srcimgb64 = srcimgb64.decode("utf-8")
        
    tgtimg = Image.open(io.BytesIO(tgt_img))
    tgtimg = resize(tgtimg, 512, 768)
    if tgtimg == None:
        raise ImageFaceEmptyException()
      
    tgtimgb64 = encode_pil_to_base64(tgtimg, "png")
    tgtimgb64 = tgtimgb64.decode("utf-8")
                    
    params["source_image"] = f"data:image/png;base64,{srcimgb64}"
    params["target_image"] = f"data:image/png;base64,{tgtimgb64}"
    params["source_faces_index"] = [src_face]
    params["face_index"] = [tgt_face]
       
    resp = requests.post(url_reactor(), json=params)
    
    resp_json = resp.json()
    img = resp_json["image"]
    img = Image.open(io.BytesIO(base64.b64decode(img)))
    x_checked_image, has_nsfw_concept, nsfw = check_safety(pil_to_numpy(img))

    tagged_image = add_visible_tags(numpy_to_pil(x_checked_image)[0])
    return encode_pil_to_base64(tagged_image, "png").decode("utf-8"), nsfw, srcimgb64, tgtimgb64
