{"3": {"inputs": {"seed": 267748890507032, "steps": 20, "cfg": 7, "sampler_name": "euler_ancestral", "scheduler": "normal", "denoise": 0.5400000000000001, "model": ["98", 0], "positive": ["76", 0], "negative": ["7", 0], "latent_image": ["95", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "aziibpixelmix_v10.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "7": {"inputs": {"text": "low quality, blurry, deformed, watermark, text, signature, depth of field, nsfw", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["49", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "25": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "49": {"inputs": {"stop_at_clip_layer": -2, "clip": ["4", 1]}, "class_type": "CLIPSetLastLayer", "_meta": {"title": "CLIP Set Last Layer"}}, "66": {"inputs": {"image": "微信图片_20250609175607.jpg"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "67": {"inputs": {"delimiter": ", ", "clean_whitespace": "true", "text_a": ["101", 2], "text_b": ["74", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "72": {"inputs": {"width": 1024, "height": 1024, "interpolation": "nearest", "method": "keep proportion", "condition": "always", "multiple_of": 0, "image": ["66", 0]}, "class_type": "ImageResize+", "_meta": {"title": "🔧 Image Resize"}}, "74": {"inputs": {"prompt": "Pixel art, scenery, ", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "CR Prompt Text", "_meta": {"title": "⚙️ CR Prompt Text"}}, "76": {"inputs": {"clip": ["49", 0], "text": ["67", 0]}, "class_type": "Text to Conditioning", "_meta": {"title": "Text to Conditioning"}}, "77": {"inputs": {"pixels": ["72", 0], "vae": ["4", 2]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "87": {"inputs": {"confidence": 0.2, "margin": 128, "model": ["88", 0], "image": ["72", 0]}, "class_type": "Crop Face", "_meta": {"title": "Crop Face"}}, "88": {"inputs": {}, "class_type": "Load RetinaFace", "_meta": {"title": "Load RetinaFace"}}, "89": {"inputs": {"interpolation": "BICUBIC", "crop_position": "center", "sharpening": 1, "image": ["87", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "95": {"inputs": {"amount": 1, "samples": ["77", 0]}, "class_type": "RepeatLatentBatch", "_meta": {"title": "Repeat Latent Batch"}}, "96": {"inputs": {"preset": "PLUS FACE (portraits)", "model": ["4", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "98": {"inputs": {"weight": 1, "weight_faceidv2": 1, "weight_type": "linear", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["4", 0], "ipadapter": ["96", 1], "image": ["89", 0]}, "class_type": "IPAdapterFaceID", "_meta": {"title": "IPAdapter FaceID"}}, "101": {"inputs": {"model": "promptgen_base_v2.0", "folder_path": "Path to your image folder", "caption_method": "simple", "max_new_tokens": 1024, "num_beams": 4, "random_prompt": "never", "prefix_caption": "", "suffix_caption": "", "replace_tags": "replace_tags eg:search1:replace1;search2:replace2", "speak_and_recognation": {"__value__": [false, true]}, "images": ["72", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Tagger", "_meta": {"title": "🐾MiaoshouAI Tagger"}}, "106": {"inputs": {"anything": ["25", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "108": {"inputs": {"filename_prefix": "flux/refer", "images": ["25", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}