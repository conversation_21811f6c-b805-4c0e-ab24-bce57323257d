{"1": {"inputs": {"editText": ["66", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["65", 0]}, "class_type": "InContextEditInstruction", "_meta": {"title": "InContextEditInstruction~"}}, "2": {"inputs": {"image": "微信图片_20250609180550.jpg"}, "class_type": "DiptychCreate", "_meta": {"title": "InputImage"}}, "3": {"inputs": {"In_context": ["44", 0], "negative": ["52", 0], "vae": ["41", 0], "diptych": ["2", 0], "maskDiptych": ["2", 1]}, "class_type": "ICEFConditioning", "_meta": {"title": "ICEFConditioning~"}}, "16": {"inputs": {"samples": ["46", 0], "vae": ["41", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "41": {"inputs": {"vae_name": "flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "44": {"inputs": {"guidance": 91.9, "conditioning": ["61", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "46": {"inputs": {"add_noise": "enable", "noise_seed": 373940359658787, "steps": 28, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "start_at_step": 0, "end_at_step": 1000, "return_with_leftover_noise": "disable", "model": ["55", 0], "positive": ["3", 0], "negative": ["3", 1], "latent_image": ["3", 2]}, "class_type": "KSamplerAdvanced", "_meta": {"title": "<PERSON><PERSON><PERSON><PERSON> (Advanced)"}}, "52": {"inputs": {"conditioning": ["61", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "55": {"inputs": {"lora_name": "icedit/pytorch_lora_weights.safetensors", "strength_model": 1.0000000000000002, "model": ["64", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "56": {"inputs": {"width": 512, "height": 1024, "x": 512, "y": 0, "image": ["60", 0]}, "class_type": "ImageCrop", "_meta": {"title": "Image Crop"}}, "60": {"inputs": {"anything": ["16", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "61": {"inputs": {"anything": ["1", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "64": {"inputs": {"unet_name": "flux1-fill-dev-Q4_K_S.gguf"}, "class_type": "UnetLoaderGGUF", "_meta": {"title": "Unet Loader (GGUF)"}}, "65": {"inputs": {"clip_name1": "t5-v1_1-xxl-encoder-Q5_K_S.gguf", "clip_name2": "clip_l.safetensors", "type": "flux"}, "class_type": "DualCLIPLoaderGGUF", "_meta": {"title": "DualCLIPLoader (GGUF)"}}, "66": {"inputs": {"from_translate": "auto", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "GoogleTranslator", "text": ["68", 0], "hide_proxy": "proxy_hide", "hide_authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "Deep Translator Text Node"}}, "68": {"inputs": {"text": "女人穿着蓝色的上衣", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "UserPrompt"}}, "70": {"inputs": {"filename_prefix": "ComfyUI", "images": ["56", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}}