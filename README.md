# ai-data

#### 介绍
ai model services 

#### 软件架构
FastApi web api server

#### 安装教程
cd ai-data
python -m venv venv 
venv/Scripts/activate

pip install -r requirements.txt

下载 [alibabacloud-nls-python-sdk-1.0.0.zip](https://static-aliyun-doc.oss-cn-hangzhou.aliyuncs.com/file-manage-files/zh-CN/20221222/efsj/alibabacloud-nls-python-sdk-1.0.0.zip?spm=a2c4g.374323.0.0.8a24142agfFVQT&file=alibabacloud-nls-python-sdk-1.0.0.zip)

在alibabacloud-nls-python-sdk-1.0.0下执行
python -m pip install -r requirements.txt
python -m pip install .

#NOTE: websocket-client (https://github.com/websocket-client/websocket-client)

#py4j加解密接口
额外开启进程 python py4j_command.py 

python main.py

# 批量生产图片
python .\tools\test_diffusers.py --outdir="E:/wallpaper/raw" --indir="D:/train-material/lora/test" --batch_size=1 --steps=20

# 调用SD API
```

    python .\tools\test_api.py --outdir="E:/wallpaper/model_2" --indir="E:\beauty_girl\2_bairuxue,E:\beauty_girl\3_doubanjiang"
    python .\tools\test_api.py --outdir="E:/wallpaper/model_2" --indir="E:\beauty_girl\2_bairuxue,E:\beauty_girl\3_doubanjiang,E:\beauty_girl\4_guoer,E:\beauty_girl\5_hejiaying"

    python .\tools\test_api.py --outdir="E:/wallpaper/model_3" --indir="E:\beauty_girl\12_liuyuer,E:\beauty_girl\13_luxuanxuan"

    python .\tools\test_api.py --outdir="E:/wallpaper/model_3" --indir="E:\beauty_girl\14_qilijia,E:\beauty_girl\15_xieanqi"

    python .\tools\test_img2img.py --inimg "D:\train-material\publicity\polotno3.png" --outimg "D:\train-material\publicity\polotno5.png"

    python .\tools\test_api_common.py --outdir="E:\screen_shot" --indir="E:\bizhi\img2img" --prompt_prefix="masterpiece," --lora_list="<lora:CattlePunkAI:0.5><lora:more_details:0.5>;<lora:CrystallineAI:0.5><lora:more_details:0.5>;<lora:CyberPunkAI:0.5><lora:more_details:0.5>;<lora:reelmech1v2:0.5><lora:more_details:0.5>"

    python .\tools\test_api_common.py --outdir="E:\screen_shot\memo" --indir="E:\壁纸\memo.txt" --prompt_prefix="masterpiece," --lora_list="<lora:CyberPunkAI:1>;<lora:CrystallineAI:1>;<lora:blindbox_v1_mix:1>;<lora:CattlePunkAI:1>;<lora:reelmech1v2:1>"

    python .\tools\\image_compression.py -d E:\wallpaper\selected3 -q 90
    
    python tools\image_resize.py --indir D:\train-material\ant --outdir  D:\train-material\ant\out --img_x 512 --img_y 394  

    #显微镜图片生产
    python .\tools\test_api_controlnet.py --indir="E:\image_factor\200\cihuichong" --outdir="E:\image_hifx\cihuichong\200" --prompt="sections of female roundworms under microscopic observation" --lora="<lora:micro_200_cihuichong:1>"
    
    python .\tools\test_api_controlnet.py --indir="E:\image_factor\800\cihuichong" --outdir="E:\image_hifx\cihuichong\800" --prompt="sections of female roundworms under microscopic observation" --lora="<lora:micro_800_cihuichong:1>"

    python .\tools\test_api_controlnet.py --indir="E:\image_factor\2000\cihuichong" --outdir="E:\image_hifx\cihuichong\2000" --prompt="sections of female roundworms under microscopic observation" --lora="<lora:micro_2000_cihuichong:1>"

    python .\tools\test_api_controlnet.py --indir="E:\image_factor\200\duanmujing" --outdir="E:\image_hifx\duanmujing\200" --prompt="transverse section of linden stems under the microscope" --lora="<lora:micro_200_duanmujing:1>"
    python .\tools\test_api_controlnet.py --indir="E:\image_factor\800\duanmujing" --outdir="E:\image_hifx\duanmujing\800" --prompt="transverse section of linden stems under the microscope" --lora="<lora:micro_800_duanmujing:1>"
    python .\tools\test_api_controlnet.py --indir="E:\image_factor\2000\duanmujing" 
    --outdir="E:\image_hifx\duanmujing\2000" --prompt="transverse section of linden stems under the microscope" --lora="<lora:micro_2000_duanmujing:1>"


    #显微镜图片切分 80 => 200
    python.exe tools/prepare.py --indir="E:\电镜标本\雌蛔虫横切\雌蛔虫-80倍-全景.png" --outdir="E:\image_factor\200\cihuichong" --split_x=3 --split_y=2 

    80 => 800
    python.exe tools/prepare.py --indir="E:\电镜标本\雌蛔虫横切\雌蛔虫-80倍-全景.png" --outdir="E:\image_factor\800\cihuichong" --split_x=5 --split_y=4

    80 => 2000
    python.exe tools/prepare.py --indir="E:\电镜标本\雌蛔虫横切\雌蛔虫-80倍-全景.png" --outdir="E:\image_factor\2000\cihuichong" --split_x=7 --split_y=7

    #缩放表格
    原始倍数	目标倍数	放大倍数	缩放到512之前的像素数	原始分辨率	切分份数
    80	       200	       2.5	      204.8	                1024	    5
    80	       800	       10	      51.2	                1024	    20
    80	       2000	       25	      20.48	                1024	    50

    #美女走control的逻辑
    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene\pool" --outdir="E:\wallpaper\controlnet\pool"  --lora="<lora:FilmVelvia3:0.4>,<lora:lora_15_xieanqi:0.5>"

    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene\beach" --outdir="E:\wallpaper\controlnet\beach"  --lora="<lora:FilmVelvia3:0.9><lora:lora_15_xieanqi:0.5>"

    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene" --outdir="E:\wallpaper\controlnet"  --lora="<lora:FilmVelvia3:0.5><lora:chinese_viewer_V1.0:0.4>"

    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene" --outdir="E:\wallpaper\controlnet_daji" --lora="<lora:FilmVelvia3:0.5><lora:lora_1_daji:0.4>"

    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene" --outdir="E:\wallpaper\controlnet_daji" --lora="<lora:lora_1_daji:0.6><lora:FilmVelvia3:0.3>"
    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene" --outdir="E:\wallpaper\controlnet_baiduxue" --lora="<lora:lora_2_bairuxue:0.5><lora:FilmVelvia3:0.3>"
    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene" --outdir="E:\wallpaper\controlnet_doubanjiang" --lora="<lora:lora_3_doubanjiang:0.7><lora:FilmVelvia3:0.3>"

    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene" --outdir="E:\wallpaper\controlnet_ross" --lora="<lora:lora_33_ross:0.7>,<lora:FilmVelvia3:0.3>"
    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene" --outdir="E:\wallpaper\controlnet_guoer" --lora="<lora:lora_4_guoer:0.7>,<lora:FilmVelvia3:0.3>"
    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene" --outdir="E:\wallpaper\controlnet_yanmo" --lora="<lora:lora_19_yanmo:0.7>,<lora:FilmVelvia3:0.3>"
    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene\wall" --outdir="E:\wallpaper\controlnet_yanmo1\wall" --lora="<lora:lora_19_yanmo:0.7>,<lora:FilmVelvia3:0.3>"

    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene" --outdir="E:\wallpaper\controlnet_douyin_janice" --lora="douyin_janice,<lora:lora_douyin_janice:0.7>,<lora:FilmVelvia3:0.3>"

    python tools\image_resize_face.py --indir E:\beauty_girl\19_yanmo --outdir  E:\beauty_girl\19_yanmo_crop --img_x 512 --img_y 768
    python tools\image_resize_face.py --indir E:\beauty_girl\20_yangchenchen --outdir  E:\beauty_girl\20_yangchenchen_crop --img_x 512 --img_y 768


    python tools\extract_frame.py --indir D:\360JiLianFiles\2023-06 --outdir E:\beauty_girl\douyin-2023-06

    python tools\extract_frame.py --indir D:\360JiLianFiles\2023-07 --outdir E:\beauty_girl\douyin-2023-07
    python tools\image_resize_face_remove_tag.py --indir E:\beauty_girl\douyin-2023-07 --outdir E:\beauty_girl\douyin-2023-07_crop --img_x 512 --img_y 768
    python tools\image_resize_face_remove_tag.py --indir E:\beauty_girl\douyin-2023-07_remain --outdir E:\beauty_girl\douyin-2023-07_crop --img_x 512 --img_y 768

    python tools\extract_frame.py --indir E:\美女sample\Janice晨 --outdir  E:\beauty_girl\douyin_Janice晨
    python tools\image_resize_face.py --indir E:\beauty_girl\douyin_Janice晨 --outdir E:\beauty_girl\douyin_Janice晨_crop --img_x 512 --img_y 768

    python tools\batch_memo.py --indir E:\beauty_girl\douyin_Janice晨_crop --memo "douyin_janice"
    python tools\merge_output.py --indir E:\wallpaper_picker --outdir E:\wallpaper_picker

    #抖音数据处理
    python tools\extract_frame.py --indir E:\美女sample\Kiwi在这里 --outdir  E:\beauty_girl\douyin_Kiwi
    python tools\image_resize_face.py --indir E:\beauty_girl\douyin_Kiwi --outdir E:\beauty_girl\douyin_Kiwi_crop --img_x 512 --img_y 768
    python tools\batch_memo.py --indir E:\beauty_girl\douyin_Kiwi_crop --memo "douyin_kiwi"

    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\38_guoer" --outdir="E:\wallpaper\controlnet_douyin_kiwi" --lora="douyin_kiwi,<lora:lora_douyin_kiwi:0.7>,<lora:FilmVelvia3:0.3>"

    python tools\image_resize_face.py --indir E:\风格化\2.png --outdir E:\风格化\crop  --img_x 512 --img_y 768
    
    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene\spring" --outdir="E:\wallpaper\controlnet_douyin_kiwi" --lora="douyin_kiwi,<lora:lora_douyin_kiwi:0.7>,<lora:FilmVelvia3:0.3>"

    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\scene\spring" --outdir="E:\wallpaper\controlnet_douyin_kiwi" --lora="douyin_janice,<lora:lora_douyin_janice:0.7>,<lora:FilmVelvia3:0.3>"

    python .\tools\test_api_tile_ref.py --indir="E:\beauty_girl\scene\spring" --outdir="E:\wallpaper\controlnet_douyin_janice" --lora="douyin_janice,<lora:lora_douyin_janice:0.7>,<lora:FilmVelvia3:0.3>"

    python .\tools\test_api_reactor.py --indir="E:\beauty_girl\scene_crop\spring" --facedir="E:\beauty_girl\face" --outdir="E:\wallpaper\reactor\scene_crop\spring"

    python tools\image_resize_face.py --indir E:\beauty_girl\scene\spring  --outdir E:\beauty_girl\scene_crop\spring  --img_x 512 --img_y 768

    python .\tools\test_api_reactor.py --indir="E:\beauty_girl\douyin_miqilin_crop\0_00000001.png" --facedir="E:\beauty_girl\douyin_laibao_crop\0_00000005.png" --outdir="E:\wallpaper\reactor"

    python .\tools\test_api_reactor.py --indir="E:\beauty_girl\birme-35_ross\IMISS-Vol.643-Lynn-Liu-Yining-and-Rossi-MrCong.com-003.jpg" --facedir="E:\beauty_girl\face" --outdir="E:\wallpaper\reactor\girl"

    python .\tools\test_api_img2img_reactor.py --indir="E:\beauty_girl\1_daji_crop" --facemodel="janice,kiwi,laibao,lixiaoyan,meier,miqilin" --outdir="E:\wallpaper\reactor\girl\1_dajia_crop"

    python .\tools\test_api_img2img_reactor_server.py --indir="E:\beauty_girl\26_zhizhi_crop" --facemodel="liuyifei,yangmi" --outdir="E:\wallpaper\reactor\server\26_zhizhi_crop"

    python .\tools\test_api_img2img_reactor_server.py --indir="E:\beauty_girl\27_zhouyuxi_crop" --facemodel="liuyifei,yangmi,sijiali,libingbing" --outdir="E:\wallpaper\reactor\server\27_zhouyuxi_crop"

    python .\tools\test_api_img2img_reactor_server.py --indir="E:\beauty_girl\28_zhukeer_crop" --facemodel="liuyifei,yangmi,sijiali,libingbing" --outdir="E:\wallpaper\reactor\server\28_zhukeer_crop"

    python .\tools\test_api_img2img_reactor_server.py --indir="E:\beauty_girl\29_riben_crop" --facemodel="liuyifei,yangmi,sijiali,libingbing" --outdir="E:\wallpaper\reactor\server\29_riben_crop"

    python .\tools\test_api_img2img_reactor_server.py --indir="E:\beauty_girl\30_yiyi_crop" --facemodel="liuyifei,yangmi,sijiali,libingbing" --outdir="E:\wallpaper\reactor\server\30_yiyi_crop"

    python .\tools\test_api_img2img_reactor_server.py --indir="E:\beauty_girl\31_zhu_crop" --facemodel="liuyifei,yangmi,sijiali,libingbing" --outdir="E:\wallpaper\reactor\server\31_zhu_crop"

    python .\tools\test_api_img2img_reactor.py --indir="E:\beauty_girl\2_bairuxue_crop" --facemodel="janice,kiwi,miqilin" --outdir="E:\wallpaper\reactor\girl\2_bairuxue_crop"

    python .\tools\test_api_openai.py --indir="E:\wallpaper_picker\wall\wall (1)._000000_e6bd7799-9c2b-4f70-9d98-a2d780908308.png" --outdir="E:\wallpaper\openai\dalle"

    #融入墨西哥风格的图片
    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\3_doubanjiang" --outdir="E:\wallpaper\controlnet_douyin_xuanmei" --lora="douyin_xuanmei,<lora:lora_douyin_xuanmei:0.5>,<lora:America Indigenous Mix:0.5>"
    python .\tools\test_api_beauty.py --indir="E:\beauty_girl\4_guoer_crop" --outdir="E:\wallpaper\controlnet_douyin_xuanmei" --lora="<lora:America Indigenous Mix:1>"


    #编译onnxruntime-gpu
    # ./build.sh --build_shared_lib --config Release --build --build_wheel  --parallel --enable_pybind --allow_running_as_root --use_cuda --cudnn_home "/usr/lib/aarch64-linux-gnu" --cuda_home "/usr/local/cuda" --use_tensorrt --tensorrt_home "/opt/tensorrt"
    #cd /workspace/onnxruntime/build/Linux/Release
    # /usr/bin/python3 /workspace/onnxruntime/setup.py install --wheel_name_suffix=gpu

    # linux 跑xformers + triton 
    # https://github.com/facebookresearch/xformers/issues/960
    # 编辑 /usr/local/lib/python3.10/dist-packages/torch/_inductor/triton_heuristics.py
    # 替换 from triton.runtime.jit import get_cuda_stream, KernelInterface
    # 为 
    # from triton.runtime.jit import KernelInterface
    # from torch._C import _cuda_getCurrentRawStream as get_cuda_stream
    # python -m torch.utils.collect_env


    # linux 安装opencv
    # 编译安装opencv-4.9.0的代码
    # pip install opencv-python==******** 

    # docker维护
    # docker commit 7f0e7cce09f6 gothz/sd-webui:1.0
    # docker login
    # docker push gothz/sd-webui:1.0
```




