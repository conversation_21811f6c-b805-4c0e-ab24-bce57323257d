#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import sys, getopt
from PIL import Image
from pathlib import Path
import os

import argparse

def parse_args():
    desc = "prepare microscope dataset"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--indir', type=str, default='input file or path', help='[file or path]')
    parser.add_argument('--outdir', type=str, default='dataset/out', help='output data dir')
    parser.add_argument('--img_x', type=int, default=512, help='output image size')
    parser.add_argument('--img_y', type=int, default=512, help='output image size')
    return parser.parse_args()

def get_resolution(width, height, dest_width, dest_height):
    
    width = int(width)
    height = int(height)
    if width < height:
        dw = dest_width
        dh = int(height / (width / dw))
    else:
        dh = dest_height
        dw = int(width / (height / dh))
    print("calc size", dw, dh)
    return dw, dh

def process_file(indir:str, outdir:str, img_x:int = 512, img_y:int = 512):
    
    img = Image.open(indir)    
    (width, height) = img.size
    print("raw size", width, height)
    rwidth, rheight = get_resolution(width, height, img_x, img_y)
    img = img.resize((rwidth, rheight))
    (width, height) = img.size
    print("resize size",width, height)
    
    if width == img_x:
        left = 0
        right = img_x
        upper = (height - img_y)/2
       
        lower = upper + img_y 
        img = img.crop((left, upper, right, lower))
        
    elif height == img_y:
        upper = 0
        lower = height
        left = (width - img_x)/2
        right = left + img_x 
        img = img.crop((left, upper, right, lower))
    else:
        pass 
    
    (width, height) = img.size
    print("crop size", width, height)
    file_name = os.path.basename(indir)
    file_name  = file_name.replace(".jpeg", ".png")
    outfile = f"{outdir}/{file_name}"
    img.save(outfile)
    
def process_directory(indir:str, outdir:str, img_x:int = 512, img_y:int = 512):
       
    for dirpath, dirname, filenames in os.walk(indir):
        for filename in filenames:
            infile = os.path.join(dirpath, filename)
            process_file(infile, outdir, img_x, img_y)

def main():
    args = parse_args()
    os.makedirs(args.outdir, exist_ok=True)
    if os.path.isfile(args.indir):
        process_file(args.indir, args.outdir, args.img_x, args.img_y)
    else:
        process_directory(args.indir, args.outdir, args.img_x, args.img_y)
        
if __name__ == "__main__":
    main()
