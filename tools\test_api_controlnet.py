#!/usr/bin/env python 
# -*- coding:utf-8 -*-
# 调用接口 
# sd 文生图
# lora  
# crontol net tile | tile difusion |ultimate SD upscale 
# target resolution 1080,1920 
#

import requests
import base64
import os
import re 
import json
import random
import argparse
import io

from PIL import Image, PngImagePlugin

base_url="http://127.0.0.1:7860"
common_negative="EasyNegative, lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry"

def url_txt2img():
    return f"{base_url}/sdapi/v1/txt2img"

def url_img2img():
    return f"{base_url}/sdapi/v1/img2img"

def save_img(images:list[str], args: dict, outdir: str, name_prefix: str):
    
    for i, img in enumerate(images):

        prefix = "%06d" % (i)
        outfile = os.path.join(outdir, f"{name_prefix}_{prefix}.png")
        while os.path.exists(outfile):
            i +=  1
            prefix = "%06d" % (i)
            outfile = os.path.join(outdir, f"{name_prefix}_{prefix}.png")

        with open(outfile, "wb") as f:
            f.write(base64.b64decode(img))

def simple_txt2img_request():
    return {
        "batch_size": 4,
        "cfg_scale": 7,
        "denoising_strength": 0,
        "enable_hr": False,
        "eta": 0,
        "firstphase_height": 0,
        "firstphase_width": 0,
        "height": 512,
        "width": 512,
        "n_iter": 1,
        "negative_prompt": "",
        "prompt": "example prompt",
        "restore_faces": True,
        "s_churn": 0,
        "s_noise": 1,
        "s_tmax": 0,
        "s_tmin": 0,
        "sampler_index": "DPM++ 2M SDE Karras",
        "seed": -1,
        "seed_resize_from_h": -1,
        "seed_resize_from_w": -1,
        "steps": 20,
        "styles": [],
        "subseed": -1,
        "subseed_strength": 0,
        "tiling": False,
        "alwayson_scripts": {},

    }
def simple_img2img_request():
    return {
        "batch_size": 4,
        "cfg_scale": 7,
        "denoising_strength": 0.55,
        "eta": 0,
        "height": 512,
        "include_init_images": True,
        "init_images": [],
        "inpaint_full_res": False,
        "inpaint_full_res_padding": 0,
        "inpainting_fill": 0,
        "inpainting_mask_invert": False,
        "mask": None,
        "mask_blur": 4,
        "n_iter": 1,
        "negative_prompt": "",
        "override_settings": {},
        "prompt": "",
        "resize_mode": 0,
        "restore_faces": True,
        "s_churn": 0,
        "s_noise": 1,
        "s_tmax": 0,
        "s_tmin": 0,
        "sampler_index": "DPM++ 2M SDE Karras",
        "seed": -1,
        "seed_resize_from_h": -1,
        "seed_resize_from_w": -1,
        "steps": 20,
        "styles": [],
        "subseed": -1,
        "subseed_strength": 0,
        "tiling": False,
        "width": 512,
        "alwayson_scripts": {},
    }

def sample_controlnet_unit():
    """
    "input_image" : image to use in this unit. defaults to null
    "mask" : mask pixel_perfect to filter the image. defaults to null
    "module" : preprocessor to use on the image passed to this unit before using it for conditioning. accepts values returned by the /controlnet/module_list route. defaults to "none"
    "model" : name of the model to use for conditioning in this unit. accepts values returned by the /controlnet/model_list route. defaults to "None"
    "weight" : weight of this unit. defaults to 1
    "resize_mode" : how to resize the input image so as to fit the output resolution of the generation. defaults to "Scale to Fit (Inner Fit)". Accepted values:
    0 or "Just Resize" : simply resize the image to the target width/height
    1 or "Scale to Fit (Inner Fit)" : scale and crop to fit smallest dimension. preserves proportions.
    2 or "Envelope (Outer Fit)" : scale to fit largest dimension. preserves proportions.
    "lowvram" : whether to compensate low GPU memory with processing time. defaults to false
    "processor_res" : resolution of the preprocessor. defaults to 64
    "threshold_a" : first parameter of the preprocessor. only takes effect when preprocessor accepts arguments. defaults to 64
    "threshold_b" : second parameter of the preprocessor, same as above for usage. defaults to 64
    "guidance_start" : ratio of generation where this unit starts to have an effect. defaults to 0.0
    "guidance_end" : ratio of generation where this unit stops to have an effect. defaults to 1.0
    "control_mode" : see the related issue for usage. defaults to 0. Accepted values:
    0 or "Balanced" : balanced, no preference between prompt and control model
    1 or "My prompt is more important" : the prompt has more impact than the model
    2 or "ControlNet is more important" : the controlnet model has more impact than the prompt
    "pixel_perfect" : enable pixel-perfect preprocessor. defaults to false
    """
    controlnet_unit = {
        "enabled": True,
        "input_image": "none",
        "mask": "none",
        "module": "none",
        "model": "none",
        "weight": 1.0,
        "resize_mode": 0,
        "lowvram": "false",
        "processor_res": 64,
        "threshold_a": 64,
        "threshold_b": 64,
        "guidance_start": 0.0,
        "guidance_end": 1.0,
        "control_mode": 2,
        "pixel_perfect": "true"
    }
    return controlnet_unit

def controlnet_args(img:str):
    lineart_args = sample_controlnet_unit()
    lineart_args["module"] = "lineart_realistic"
    lineart_args["model"] = "control_v11p_sd15_lineart [43d4be0d]"
    lineart_args["input_image"] = img
    lineart_args["mask"] = None
    lineart_args["weight"] = 2.0
    lineart_args["resize_mode"] = 0
    lineart_args["control_mode"] = 0

    reference_args = sample_controlnet_unit()
    reference_args["module"] = None
    reference_args["model"] = "control_v11e_sd15_ip2p [c4bb465c]"
    reference_args["input_image"] = img
    reference_args["mask"] = None
    reference_args["weight"] = 2.0
    reference_args["resize_mode"] = 0
    reference_args["control_mode"] = 0

    params = {}
    params["args"] = [lineart_args, reference_args]

    return params 

def txt2img_with_contronnet(prompt:str, negative_prompt:str, outdir:str, img:str, name_prefix:str, seed:int = -1):
    #params = simple_txt2img_request()
    params = simple_img2img_request()
    params["prompt"] = prompt
    params["negative_prompt"] = negative_prompt
    params["seed"] = seed
    params["init_images"].append(img)
    cntl_args = controlnet_args(img)
    params["alwayson_scripts"]["ControlNet"] = cntl_args

    #resp = requests.post(url_txt2img(), json=params)
    resp = requests.post(url_img2img(), json=params)
    resp_json = resp.json()
    save_img(resp_json["images"], resp_json["parameters"], outdir, name_prefix)

def gen_image(prompt:str, outdir:str, lora_model:str, img:str, name_prefix:str, seed:int = -1):

    prompt = f"{prompt}, (ultra high res),(highly detailed), {lora_model}"
    os.makedirs(outdir, exist_ok=True)
    txt2img_with_contronnet(prompt, common_negative, outdir, img, name_prefix, seed)

def encode_image(image):

    with io.BytesIO() as output_bytes:
        metadata = None
        for key, value in image.info.items():
            if isinstance(key, str) and isinstance(value, str):
                if metadata is None:
                    metadata = PngImagePlugin.PngInfo()
                metadata.add_text(key, value)
        image.save(output_bytes, format="PNG", pnginfo=metadata)
        bytes_data = output_bytes.getvalue()
        
        ret = str(base64.b64encode(bytes_data), "utf-8")
        return ret 

def main(args):

    lora = args.lora
    prompt = args.prompt

    for p, dirs, files in os.walk(args.indir):
        for name in files:
            if name.endswith(".png"):
                print(os.path.join(p, name))
                img = Image.open(os.path.join(p, name))
                img = encode_image(img)
                name_prefix = name.strip(".png")
                outdir = args.outdir
                gen_image(prompt, outdir, lora, img, name_prefix, args.seed)

def setup_parser() -> argparse.ArgumentParser:

    parser = argparse.ArgumentParser()
    parser.add_argument("--indir", type=str, required=True, default=None, help="set controlnet within dir images")
    parser.add_argument("--outdir", type=str, required=True, default="outputs", help="dir to write results to")
    parser.add_argument("--prompt", type=str, required=True, default=None, help="prompt content")
    parser.add_argument("--lora", type=str, required=True, default=None, help="lora")
    parser.add_argument("--seed", type=int, required=False, default=-1, help="random seed for data generation")
    return parser

if __name__ == "__main__":
    
    parser = setup_parser()
    args = parser.parse_args()
    main(args)


