{"4": {"inputs": {"ckpt_name": "DreamShaperXL_Lightning-SFW.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "10": {"inputs": {"text": ["38", 0], "speak_and_recognation": true, "clip": ["18", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "11": {"inputs": {"text": " (nsfw:1.5),embedding:ng_deepnegative_v1_75t", "speak_and_recognation": true, "clip": ["12", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "12": {"inputs": {"lora_name": "claymate/CLAYMATE_V2.03_.safetensors", "strength_model": 0.5, "strength_clip": 0.5, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "18": {"inputs": {"lora_name": "claymate/DD-made-of-clay-XL-v2.safetensors", "strength_model": 0.45, "strength_clip": 0.45, "model": ["12", 0], "clip": ["12", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "21": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["57", 0], "negative": ["57", 1], "control_net": ["58", 0], "image": ["23", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet (Advanced)"}}, "23": {"inputs": {"preprocessor": "LineartStandardPreprocessor", "resolution": 1024, "image": ["87", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "24": {"inputs": {"image": "4a964e50964015ab3c17471318e85e89.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "38": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": ["77", 1], "text_b": "claymotion, made-of-clay, stopmotion, polymer clay, ultra light clay, High quality, details, cartoonish, 8k", "text_c": "", "speak_and_recognation": true}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "57": {"inputs": {"strength": 1, "start_percent": 0, "end_percent": 1, "positive": ["10", 0], "negative": ["11", 0], "control_net": ["58", 0], "image": ["59", 0]}, "class_type": "ControlNetApplyAdvanced", "_meta": {"title": "Apply ControlNet (Advanced)"}}, "58": {"inputs": {"control_net_name": "controlnet-union-sdxl/diffusion_pytorch_model.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "59": {"inputs": {"preprocessor": "DepthAnythingPreprocessor", "resolution": 1024, "image": ["87", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "66": {"inputs": {"clip_name": "CLIP_ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "72": {"inputs": {"samples": ["74", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "74": {"inputs": {"seed": 566448146348083, "steps": 8, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1, "model": ["81", 0], "positive": ["21", 0], "negative": ["21", 1], "latent_image": ["83", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "75": {"inputs": {"filename_prefix": "claymate/claymate", "images": ["72", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "77": {"inputs": {"task": "more detailed caption", "text_input": "", "max_new_tokens": 1024, "num_beams": 3, "do_sample": false, "fill_mask": false, "FLORENCE2": ["78", 0], "image": ["87", 0]}, "class_type": "Florence2", "_meta": {"title": "Florence2"}}, "78": {"inputs": {"version": "large-ft"}, "class_type": "LoadFlorence2Model", "_meta": {"title": "Load Florence2 Model"}}, "81": {"inputs": {"weight": 1, "weight_type": "linear", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["82", 0], "ipadapter": ["82", 1], "image": ["85", 0], "clip_vision": ["66", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "82": {"inputs": {"preset": "PLUS FACE (portraits)", "model": ["18", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "83": {"inputs": {"width": 768, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "85": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["87", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "86": {"inputs": {"anything": ["72", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean GPU Used"}}, "87": {"inputs": {"width": 768, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["24", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}}