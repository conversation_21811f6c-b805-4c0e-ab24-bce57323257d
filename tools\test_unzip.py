# -*- coding:utf-8 -*-

import os
import zipfile
import argparse
import shutil

def unzip_file(zip_file_path, dest_dir):
    with zipfile.ZipFile(zip_file_path) as zip_file:
        for member in zip_file.namelist():
            filename = os.path.basename(member)
            # skip directories
            if not filename:
                continue
            if filename.startswith("."):
                continue
            # copy file (taken from zipfile's extract)
            source = zip_file.open(member)
            target = open(os.path.join(dest_dir, filename), "wb")
            with source, target:
                shutil.copyfileobj(source, target)

def main(args):

    for p, dirs, files in os.walk(args.indir):
        for name in files:
            if name.endswith(".zip"):
                zipfile = os.path.join(p, name)
                outpath = name.split(" ")[-1].strip(".zip")
                dest_dir= os.path.join(args.outdir, outpath)
                os.makedirs(dest_dir, exist_ok=True)
                unzip_file(zipfile, dest_dir)
                
def setup_parser() -> argparse.ArgumentParser:

    parser = argparse.ArgumentParser()
    parser.add_argument("--indir", type=str, default=None, help="all zip file " )
    parser.add_argument("--outdir", type=str, default="outputs", help="dir to write results to")

    return parser

if __name__ == "__main__":
    parser = setup_parser()
    args = parser.parse_args()
    main(args)

