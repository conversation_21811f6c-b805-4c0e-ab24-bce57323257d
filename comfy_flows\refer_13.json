{"1": {"inputs": {"vae_name": "flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "12": {"inputs": {"image": "v0d00fg10000cm5pr2rc77u7tcjikdk0[(000243)2024-01-13-20-48-06.jpg"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "16": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5-v1_1-xxl-encoder-Q5_K_S.gguf", "type": "flux"}, "class_type": "DualCLIPLoaderGGUF", "_meta": {"title": "DualCLIPLoader (GGUF)"}}, "17": {"inputs": {"unet_name": "flux1-dev-Q4_K_S.gguf", "dequant_dtype": "bfloat16", "patch_dtype": "bfloat16", "patch_on_device": false}, "class_type": "UnetLoaderGGUFAdvanced", "_meta": {"title": "Unet Loader (GGUF/Advanced)"}}, "18": {"inputs": {"width": ["62", 0], "height": ["62", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 2, "crop": 0, "image": ["224", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "42": {"inputs": {"clip_l": ["197", 0], "t5xxl": ["69", 2], "guidance": 3.5, "speak_and_recognation": {"__value__": [false, true]}, "clip": ["202", 1]}, "class_type": "CLIPTextEncodeFlux", "_meta": {"title": "CLIPTextEncodeFlux"}}, "53": {"inputs": {"conditioning": ["42", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "62": {"inputs": {"width": 1024, "height": 1024, "aspect_ratio": "3:4 portrait 896x1152", "swap_dimensions": "Off", "upscale_factor": 1, "batch_size": 1}, "class_type": "CR SDXL Aspect Ratio", "_meta": {"title": "AspectRatio"}}, "68": {"inputs": {"max_shift": 1.15, "base_shift": 0.5, "width": ["18", 1], "height": ["18", 2], "model": ["202", 0]}, "class_type": "ModelSamplingFlux", "_meta": {"title": "ModelSamplingFlux"}}, "69": {"inputs": {"model": "promptgen_base_v1.5", "folder_path": "Path to your image folder", "caption_method": "detailed", "max_new_tokens": 1024, "num_beams": 4, "random_prompt": "never", "prefix_caption": "", "suffix_caption": "", "replace_tags": "", "speak_and_recognation": {"__value__": [false, true]}, "images": ["239", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Tagger", "_meta": {"title": "🐾MiaoshouAI Tagger"}}, "86": {"inputs": {"lora_name": "flux/Hyper-FLUX.1-dev-8steps-lora.safetensors", "strength_model": 0.13, "strength_clip": 0.13, "model": ["17", 0], "clip": ["16", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "122": {"inputs": {"mode": true, "a": ["18", 1], "b": ["18", 2]}, "class_type": "ImpactMinMax", "_meta": {"title": "ImpactMinMax"}}, "145": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "146": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "147": {"inputs": {"provider": "CPU"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "148": {"inputs": {"weight": 0.8, "start_at": 0, "end_at": 1, "model": ["68", 0], "pulid_flux": ["145", 0], "eva_clip": ["146", 0], "face_analysis": ["147", 0], "image": ["149", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "149": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["238", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "181": {"inputs": {"control_net_name": "flux/flux-depth-controlnet-v3.safetensors", "model": ["276", 0]}, "class_type": "DiffControlNetLoader", "_meta": {"title": "Load ControlNet Model (diff)"}}, "182": {"inputs": {"strength": 0.8, "start_percent": 0, "end_percent": 1, "positive": ["42", 0], "negative": ["53", 0], "control_net": ["183", 0], "vae": ["1", 0], "image": ["200", 0]}, "class_type": "ControlNetApplySD3", "_meta": {"title": "Apply Controlnet with VAE"}}, "183": {"inputs": {"type": "depth", "control_net": ["181", 0]}, "class_type": "SetUnionControlNetType", "_meta": {"title": "SetUnionControlNetType"}}, "197": {"inputs": {"from_translate": "auto", "to_translate": "english", "add_proxies": false, "proxies": "", "auth_data": "", "service": "MyMemoryTranslator [free]", "text": "柔和的自然光透过窗户，洒在她精致的脸上，记录下她沉思的瞬间。", "hide_proxy": "proxy_hide", "hide_authorization": "authorization_hide", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "DeepTranslatorTextNode", "_meta": {"title": "UserPrompt"}}, "200": {"inputs": {"preprocessor": "DepthAnythingV2Preprocessor", "resolution": ["122", 0], "image": ["230", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "202": {"inputs": {"lora_name": "flux/FLUXpro1.1-FLUX-V0.2_aidmafluxpro1.1.safetensors", "strength_model": 0.9, "strength_clip": 0.9, "model": ["272", 0], "clip": ["272", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "205": {"inputs": {"samples": ["206", 0], "vae": ["1", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "206": {"inputs": {"noise": ["210", 0], "guider": ["209", 0], "sampler": ["213", 0], "sigmas": ["208", 0], "latent_image": ["232", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "207": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "208": {"inputs": {"scheduler": "simple", "steps": 8, "denoise": 1, "model": ["276", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "209": {"inputs": {"model": ["276", 0], "conditioning": ["182", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "210": {"inputs": {"noise_seed": 908253568692949}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "213": {"inputs": {"detail_amount": 1, "start": 0.2, "end": 1, "bias": 0.5, "exponent": 0.5, "start_offset": 0, "end_offset": 0, "fade": 0, "smooth": true, "cfg_scale_override": 1, "sampler": ["207", 0]}, "class_type": "DetailDaemonSamplerNode", "_meta": {"title": "Detail <PERSON>"}}, "214": {"inputs": {"filename_prefix": "refer/flux", "images": ["243", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "215": {"inputs": {"anything": ["243", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "217": {"inputs": {"pixels": ["18", 0], "vae": ["1", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "224": {"inputs": {"image": "input/template/487.png", "custom_width": 0, "custom_height": 0}, "class_type": "VHS_LoadImagePath", "_meta": {"title": "LoadImageFromPath"}}, "230": {"inputs": {"sam_model": "sam_vit_h (2.56GB)", "grounding_dino_model": "GroundingDINO_SwinT_OGC (694MB)", "threshold": 0.3, "detail_method": "PyMatting", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.15, "white_point": 0.99, "process_detail": true, "prompt": "face", "device": "cuda", "max_megapixels": 2, "cache_model": false, "image": ["18", 0]}, "class_type": "LayerMask: SegmentAnythingUltra V2", "_meta": {"title": "LayerMask: SegmentAnythingUltra V2(Advance)"}}, "232": {"inputs": {"samples": ["217", 0], "mask": ["237", 0]}, "class_type": "SetLatentNoiseMask", "_meta": {"title": "Set Latent Noise Mask"}}, "237": {"inputs": {"expand": 2, "tapered_corners": false, "mask": ["230", 1]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "238": {"inputs": {"detail_method": "VITMatte(local)", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.01, "white_point": 0.99, "process_detail": true, "device": "cuda", "max_megapixels": 2, "image": ["12", 0]}, "class_type": "LayerMask: RmBgUltra V2", "_meta": {"title": "LayerMask: RmBgUltra V2"}}, "239": {"inputs": {"fill_background": false, "background_color": "#000000", "RGBA_image": ["149", 0]}, "class_type": "LayerUtility: ImageRemoveAlpha", "_meta": {"title": "LayerUtility: ImageRemoveAlpha"}}, "243": {"inputs": {"samples": ["244", 0], "vae": ["1", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "244": {"inputs": {"noise": ["248", 0], "guider": ["247", 0], "sampler": ["249", 0], "sigmas": ["246", 0], "latent_image": ["252", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "245": {"inputs": {"sampler_name": "euler"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "246": {"inputs": {"scheduler": "simple", "steps": 8, "denoise": 0.3, "model": ["276", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "RepaintScheduler"}}, "247": {"inputs": {"model": ["276", 0], "conditioning": ["267", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "248": {"inputs": {"noise_seed": 299345987849819}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "249": {"inputs": {"detail_amount": 1, "start": 0.2, "end": 1, "bias": 0.5, "exponent": 0.5, "start_offset": 0, "end_offset": 0, "fade": 0, "smooth": true, "cfg_scale_override": 1, "sampler": ["245", 0]}, "class_type": "DetailDaemonSamplerNode", "_meta": {"title": "Detail <PERSON>"}}, "251": {"inputs": {"pixels": ["278", 0], "vae": ["1", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "252": {"inputs": {"samples": ["251", 0], "mask": ["253", 0]}, "class_type": "SetLatentNoiseMask", "_meta": {"title": "Set Latent Noise Mask"}}, "253": {"inputs": {"expand": 2, "tapered_corners": false, "mask": ["255", 1]}, "class_type": "GrowMask", "_meta": {"title": "GrowMask"}}, "254": {"inputs": {"model": "promptgen_base_v1.5", "folder_path": "Path to your image folder", "caption_method": "detailed", "max_new_tokens": 1024, "num_beams": 4, "random_prompt": "never", "prefix_caption": "", "suffix_caption": "", "replace_tags": "", "speak_and_recognation": {"__value__": [false, true]}, "images": ["278", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Tagger", "_meta": {"title": "🐾MiaoshouAI Tagger"}}, "255": {"inputs": {"sam_model": "sam_vit_h (2.56GB)", "grounding_dino_model": "GroundingDINO_SwinT_OGC (694MB)", "threshold": 0.3, "detail_method": "VITMatte", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.15, "white_point": 0.99, "process_detail": true, "prompt": "person", "device": "cuda", "max_megapixels": 2, "cache_model": false, "image": ["278", 0]}, "class_type": "LayerMask: SegmentAnythingUltra V2", "_meta": {"title": "LayerMask: SegmentAnythingUltra V2(Advance)"}}, "267": {"inputs": {"clip_l": ["254", 2], "t5xxl": ["254", 2], "guidance": 3.5, "speak_and_recognation": {"__value__": [false, true]}, "clip": ["202", 1]}, "class_type": "CLIPTextEncodeFlux", "_meta": {"title": "CLIPTextEncodeFlux"}}, "272": {"inputs": {"lora_name": "flux/nwsj_flux0924.safetensors", "strength_model": 0.8, "strength_clip": 0.8, "model": ["86", 0], "clip": ["86", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "275": {"inputs": {"model": ["148", 0]}, "class_type": "FluxForwardOverrider", "_meta": {"title": "FluxForwardOverrider"}}, "276": {"inputs": {"rel_l1_thresh": 0.25, "cache_device": "offload_device", "wan_coefficients": "disabled", "model": ["275", 0]}, "class_type": "ApplyTeaCachePatch", "_meta": {"title": "ApplyTeaCachePatch"}}, "278": {"inputs": {"anything": ["205", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}}