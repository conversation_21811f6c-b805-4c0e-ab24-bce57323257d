{"72": {"inputs": {"image": "微信图片_20250617102112.png"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "118": {"inputs": {"vae_name": "Wan2_1_VAE_bf16.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "119": {"inputs": {"clip_name": "umt5_xxl_fp8_e4m3fn_scaled.safetensors", "type": "wan", "device": "default"}, "class_type": "CLIPLoader", "_meta": {"title": "Load CLIP"}}, "122": {"inputs": {"text": ["193", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["119", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "123": {"inputs": {"text": "色调艳丽，过曝，静态，细节模糊不清，字幕，风格，作品，画作，画面，静止，整体发灰，最差质量，低质量，JPEG压缩残留，丑陋的，残缺的，多余的手指，画得不好的手部，画得不好的脸部，畸形的，毁容的，形态畸形的肢体，手指融合，静止不动的画面，杂乱的背景，三条腿，三条胳膊，三只手，背景人很多，倒着走，看不到脸，三个人，多个人，背景模糊", "speak_and_recognation": {"__value__": [false, true]}, "clip": ["119", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "124": {"inputs": {"width": 480, "height": 832, "length": 81, "batch_size": 1, "strength": 1.0000000000000002, "positive": ["122", 0], "negative": ["123", 0], "vae": ["118", 0], "reference_image": ["187", 0]}, "class_type": "WanVaceToVideo", "_meta": {"title": "WanVaceToVideo"}}, "125": {"inputs": {"shift": 8, "model": ["196", 0]}, "class_type": "ModelSamplingSD3", "_meta": {"title": "ModelSamplingSD3"}}, "126": {"inputs": {"seed": 148749103898876, "steps": 4, "cfg": 1, "sampler_name": "lcm", "scheduler": "simple", "denoise": 1, "model": ["125", 0], "positive": ["124", 0], "negative": ["124", 1], "latent_image": ["124", 2]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "127": {"inputs": {"trim_amount": ["124", 3], "samples": ["126", 0]}, "class_type": "TrimVideoLatent", "_meta": {"title": "TrimVideoLatent"}}, "128": {"inputs": {"samples": ["127", 0], "vae": ["118", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "129": {"inputs": {"anything": ["128", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "139": {"inputs": {"filename_prefix": "wan/animie", "fps": 16.000000000000004, "lossless": true, "quality": 90, "method": "default", "images": ["128", 0]}, "class_type": "SaveAnimatedWEBP", "_meta": {"title": "SaveAnimatedWEBP"}}, "187": {"inputs": {"width": 480, "height": 832, "upscale_method": "nearest-exact", "keep_proportion": "crop", "pad_color": "0, 0, 0", "crop_position": "center", "divisible_by": 2, "device": "cpu", "image": ["72", 0]}, "class_type": "ImageResizeKJv2", "_meta": {"title": "Resize Image v2"}}, "193": {"inputs": {"action": "replace", "tidy_tags": "yes", "text_a": "[target],a heartwarming, realistic family scene, (a man walking toward the [target] and gently handing over a baby to the [target])+++, the [target] receiving the baby with both arms, the man standing beside the [target] and putting his arm around the [target] shoulders lovingly, the couple standing side by side, sharing a sweet intimate moment, the baby resting peacefully in the [target] arms, warm expressions on their faces, realistic lighting, natural skin texture, cozy and romantic atmosphere, cinematic composition, detailed clothing folds, high-quality photographic style, soft background blur, gentle sunlight or indoor ambient light", "text_b": "[target]", "text_c": "", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "194": {"inputs": {"unet_name": "Wan2.1-VACE-14B-Q4_K_S.gguf"}, "class_type": "UnetLoaderGGUF", "_meta": {"title": "Unet Loader (GGUF)"}}, "196": {"inputs": {"lora_name": "wan2.1/Wan21_I2V_14B_480P_lightx2v_cfg_step_distill_lora_rank64.safetensors", "strength_model": 1, "model": ["194", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}}