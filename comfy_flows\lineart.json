{"3": {"inputs": {"seed": 5165917823933, "steps": 8, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1, "model": ["50", 0], "positive": ["52", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "DreamShaperXL_Lightning-SFW.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 768, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"text": ["51", 0], "speak_and_recognation": true, "clip": ["50", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry,embedding:ng_deepnegative_v1_75t, ", "speak_and_recognation": true, "clip": ["50", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "lineart/lineart", "images": ["68", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "12": {"inputs": {"image": "747c2d06-afc9-4db3-8d5f-afdd86aed299_1.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "50": {"inputs": {"lora_name": "sdxl/Pixel_Lineart.safetensors", "strength_model": 1, "strength_clip": 1, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "51": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "(lineart:1.2), (white background:1.2) masterpiece, pixlineart, monochrome, ", "text_b": ["64", 1], "text_c": "", "speak_and_recognation": true}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "52": {"inputs": {"strength": 0.8, "conditioning": ["6", 0], "control_net": ["53", 0], "image": ["55", 0]}, "class_type": "ControlNetApply", "_meta": {"title": "Apply ControlNet"}}, "53": {"inputs": {"control_net_name": "controlnet-union-sdxl/diffusion_pytorch_model.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "55": {"inputs": {"preprocessor": "AnimeLineArtPreprocessor", "resolution": 1024, "image": ["70", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "64": {"inputs": {"task": "more detailed caption", "text_input": "", "max_new_tokens": 1024, "num_beams": 3, "do_sample": false, "fill_mask": false, "FLORENCE2": ["65", 0], "image": ["70", 0]}, "class_type": "Florence2", "_meta": {"title": "Florence2"}}, "65": {"inputs": {"version": "large-ft"}, "class_type": "LoadFlorence2Model", "_meta": {"title": "Load Florence2 Model"}}, "68": {"inputs": {"facedetection": "retinaface_resnet50", "model": "codeformer-v0.1.0.pth", "visibility": 1, "codeformer_weight": 0.5, "image": ["8", 0]}, "class_type": "ReActorRestoreFace", "_meta": {"title": "Restore Face 🌌 ReActor"}}, "70": {"inputs": {"width": 768, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "71": {"inputs": {"anything": ["68", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean GPU Used"}}}