#!/usr/bin/env python
# -*- coding:utf-8 -*-

import os
import re
import time
import csv
import requests
import asyncio
import aiohttp
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.common.action_chains import ActionChains
from selenium.common.exceptions import StaleElementReferenceException, TimeoutException, NoSuchElementException
import argparse

from selenium.webdriver.common.keys import Keys
from selenium.webdriver.chrome.options import Options
from datetime import datetime

TARGET_URL = "https://app.nieta.art/?channel_from=nieta_art_official"
# 发现角色页面
DISCOVER_URL = "https://app.nieta.art/character/discover"
# 数据集目录
DATASET_DIR = "dataset/nieta"

IMAGE_LOAD_COUNT = 50


def get_now():
    """获取当前时间字符串"""
    return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

def setup_driver():
    """设置Chrome浏览器驱动"""
    chrome_options = Options()
    chrome_options.add_argument("--no-sandbox")
    chrome_options.add_argument("--start-maximized")
    
    try:
        # 注意：请根据您的实际路径修改chromedriver路径
        browser_path = r"D:\hexueying\software\chromedriver-win64\chromedriver.exe"
        driver = webdriver.Chrome(options=chrome_options)
        return driver
    except Exception as e:
        print(f"{get_now()} 创建Chrome驱动失败: {e}")
        print("请确保已安装Chrome浏览器和ChromeDriver")
        return None

def wait_for_user_login():
    """等待用户手动登录"""
    print(f"{get_now()} 请在浏览器中手动登录...")
    print(f"{get_now()} 登录完成后，请在终端中按 Enter 键继续...")
    input("按 Enter 键继续...")


def scroll_by_centimeters_attr(driver, class_attr, max_image_count=50, no_new_image_timeout=5):
    try:
        # 初始化结果列表
        image_urls = []
        # Find all <div> elements
        divs_all = []
        image_count = 0  # 初始化图片计数器
        last_image_count = 0  # 上一次图片计数
        no_new_image_start = None  # 没有新图片开始时间
        
        while True:
            divs = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located((By.CSS_SELECTOR, 
                    class_attr))
            )
            # print(f"找到 {len(divs)} 个符合条件的 <div> 元素")
            # Scroll to each <div> to ensure <img> is loaded
            
            for i, div in enumerate(divs):
                if div in divs_all:
                    continue  # Skip already processed <div> elements
                # Scroll to the <div> element   
                try:
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", div)
                    # print(f"已滚动到第 {i + 1} 个 <div>")
                    WebDriverWait(driver, 2).until(EC.presence_of_element_located((By.TAG_NAME, "img")))
                    # Extract <img> src
                    img = div.find_element(By.TAG_NAME, "img")
                    src = img.get_attribute("src")
                    
                    # Clean the URL (remove query parameters if needed)
                    clean_url = src.split('?')[0] if '?' in src else src

                    # 确保 URL 是唯一的
                    if clean_url not in image_urls:
                        image_urls.append(clean_url)
                        image_count += 1
                        print(f"第 {image_count} 个图片 URL: {clean_url}")
                    divs_all.append(div)  # Add to processed list
                except Exception as e:
                    print(f"处理第 {i + 1} 个 <div> 时的错误: {e}")
            # print(f"当前页面找到 {len(divs)} 个符合条件的 <div> 元素")        

            # 检查是否有新图片加载
            if image_count == last_image_count:
                # 没有新图片加载，检查是否超时
                if no_new_image_start is None:
                    no_new_image_start = time.time()  # 记录没有新图片开始的时间
                elif time.time() - no_new_image_start >= no_new_image_timeout:
                    print(f"超过 {no_new_image_timeout} 秒没有新图片加载，退出循环")
                    break
            else:
                # 有新图片加载，重置计时器
                no_new_image_start = None
                last_image_count = image_count

            # 如果图片数量达到上限，退出循环
            if image_count >= max_image_count:
                print("图片数量达到上限，退出循环")
                break
            
    except Exception as e:
        print(f"操作过程中发生错误: {e}")

    return image_urls

# 保存下载的图片
def save_scraped_images(save_dir, image_urls, name_charactors=None):
    # 创建保存图片的目录
    os.makedirs(save_dir, exist_ok=True)
    
    # 如果没有提供 name_charactors，则使用默认的编号
    if name_charactors is None or len(name_charactors) == 0:
        name_charactors = [f"image_{i}" for i in range(len(image_urls))]
    
    for i, (clean_url, name_charactor) in enumerate(zip(image_urls, name_charactors)):
        # 下载图片
        response = requests.get(clean_url, headers={'User-Agent': 'Mozilla/5.0'})
        if response.status_code == 200:
            # 提取图片的扩展名
            _, image_extension = os.path.splitext(clean_url)
            
            # 生成唯一的文件名
            image_path = os.path.join(save_dir, f"{name_charactor}{image_extension}")
            
            # 保存图片
            with open(image_path, 'wb') as f:
                f.write(response.content)
            print(f"图片已下载并保存为: {image_path}")
        else:
            print(f"下载图片失败，URL: {clean_url}，状态码: {response.status_code}")

# 滚动页面并提取图片 URL 和角色名称
def scroll_by_centimeters(driver,element_text, class_attr, max_scrolls=50):
    try:
        # 初始化结果列表
        image_urls = []
        name_charactors = []
        # 找到所有符合条件的 <div> 元素
        divs_all = []
        scroll_count = 0  # 初始化滚动次数计数器
        image_count = 0  # 初始化图片计数器
        i = 0  # 初始化索引
        while True:
            # 重新查找所有符合条件的 <div> 元素
            divs = WebDriverWait(driver, 10).until(
                EC.presence_of_all_elements_located((By.CSS_SELECTOR, class_attr))
            )
            
            while True:
                try:
                    div = divs[i]
                except Exception as e:
                    # print(f"处理第 {i} 个 <div> 时的错误: {e}")
                    # 如果发生错误，尝试回退索引
                    i -= 5  # 根据你的需求调整回退的步数
                    break

                i += 1
                if div in divs_all:
                    continue  # 跳过已经处理过的 <div> 元

                divs_all.append(div)  # 添加到已处理列表
                # 滚动到 <div> 元素
                try:
                    driver.execute_script("arguments[0].scrollIntoView({block: 'center'});", div)
                    WebDriverWait(driver, 2).until(EC.presence_of_element_located((By.TAG_NAME, "img")))

                    # 提取 <img> 的 src 属性
                    img = div.find_element(By.TAG_NAME, "img")
                    src = img.get_attribute("src")
                    
                    # 清理 URL（移除查询参数）
                    clean_url = src.split('?')[0] if '?' in src else src

                    # 提取角色名称
                    name_element = div.find_element(By.CSS_SELECTOR, "div.relative.px-8px.mb-6px.line-clamp-2")
                    name_charactor = name_element.text

                    # 确保 URL 是唯一的
                    if clean_url not in image_urls:
                        image_urls.append(clean_url)
                        name_charactors.append(name_charactor)
                        image_count += 1
                        
                        print(f"第 {image_count} 个角色名为 {name_charactor} 的图片 URL: {clean_url}")

                        # 点击角色图片以加载更多
                        div.click()
                        time.sleep(2)  # 等待页面加载
                        image_id_urls = scroll_by_centimeters_attr(driver, class_attr="div.relative.rounded-8px.\!shadow-none", max_image_count=20, no_new_image_timeout=5)
                        # 注意：这里需要将 element_text 传递给函数
                        character_dir = os.path.join(DATASET_DIR, element_text, name_element.text)
                        save_scraped_images(character_dir, image_id_urls)
                        # 返回上一页
                        driver.back()
                        time.sleep(3)  # 等待页面加载

                        break

                except Exception as e:
                    print(f"处理第 {i} 个 <div> 时的错误: {e}")
            
            # 滚动次数计数器加1
            scroll_count += 1

            # 如果滚动次数超过指定次数，退出循环
            if scroll_count >= max_scrolls:
                print("滚动次数达到指定次数，退出循环")
                break
    except Exception as e:
        print(f"操作过程中发生错误: {e}")

    return image_urls, name_charactors


def scrape_nieta_art_data(character:str):
    """主要的爬取函数"""
    print(f"{get_now()} 开始Nieta Art数据爬取...")
    
    # if character == "哈利波特":
    #     return

    # 设置浏览器驱动
    driver = setup_driver()
    if not driver:
        return False

    try:
        # 访问页面
        print(f"{get_now()} 正在访问页面...")
        driver.get(TARGET_URL)
        
        # 打印页面标题
        print(f"{get_now()} 页面标题: {driver.title}")
        
        # 等待用户手动登录
        wait_for_user_login()
        
        # 跳转到指定页面
        print(f"{get_now()} 正在跳转到发现角色页面...")
        driver.get(DISCOVER_URL)
        time.sleep(5)  # 等待页面加载
        
        # 查找所有静态加载的元素
        category_elements = driver.find_elements(By.CSS_SELECTOR, '.flex.flex-col .shrink-0.relative')

        # 遍历找到的元素，从第一个开始
        for i, element in enumerate(category_elements):
            # 检查元素的文本是否等于 "哈利波特"
            if element.text == "哈利波特":
                print(f"找到 '哈利波特'，开始从第 {i+1} 个元素点击")
                break
        else:
            print("没有找到 '哈利波特'")
            # driver.quit()
            # exit()

        # 从找到的 "哈利波特" 元素开始点击
        for j in range(i, len(category_elements)):
            element = category_elements[j]
            # 点击当前元素
            element.click()
            print(f"点击了第 {j+1-i} 个元素: {element.text}")
            
            # 等待页面加载
            time.sleep(2)

            # Scroll to each <div> element
            save_dir = os.path.join(DATASET_DIR, element.text)
            image_urls, name_charactors = scroll_by_centimeters(driver, element.text, class_attr="div.relative.rounded-8px.overflow-hidden", max_scrolls=50)
            save_scraped_images(save_dir, image_urls, name_charactors)
            
            time.sleep(2)  # 等待

            # 重新查找所有静态加载的元素
            category_elements = driver.find_elements(By.CSS_SELECTOR, '.flex.flex-col .shrink-0.relative')

            # 等待一段时间（根据需要调整）
            time.sleep(5)
    finally:
        # 关闭浏览器
        driver.quit()

def parse_args():
    desc = "encode image to base64 string format"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--character',  type=str, default='哈利波特', help='roles for start crawler')
    return parser.parse_args()

def main():
    """主函数"""
    print(f"{get_now()} Nieta Art 数据爬虫启动")
    
    args = parse_args()

    success = scrape_nieta_art_data(args.character)
    
    if success:
        print(f"{get_now()} 爬取任务完成！")
    else:
        print(f"{get_now()} 爬取任务失败！")

if __name__ == "__main__":
    main()
