import hashlib
import os
from datetime import datetime
import uuid
import json

def generate_content_producer() -> str:

    fund_time = datetime.strptime("2018-08-15 12:33:33", "%Y-%m-%d %H:%M:%S")
    fund_ts = fund_time.timestamp()
    fund_ts = int(fund_ts)
    fund_tail = "0000055555"
    return f"{fund_ts}91110108MA01G6WA73{fund_tail}"


def generate_uuid_sha256(uuid:str) -> str | None:
    # 创建 SHA-256 哈希对象
    sha256_hash = hashlib.sha256()
    sha256_hash.update(uuid.encode())
    return sha256_hash.hexdigest()

def generate_image_sha256(image_path: str) -> str | None:
    """
    计算指定图片文件的 SHA-256 哈希值。

    参数:
    image_path (str): 图片文件的完整路径。

    返回:
    str | None: 图片的 SHA-256 哈希值（十六进制字符串），如果文件不存在则返回 None。
    """
    if not os.path.exists(image_path):
        print(f"错误：图片文件 '{image_path}' 不存在。")
        return None

    try:
        # 以二进制读取模式打开图片文件
        with open(image_path, 'rb') as f:
            # 创建 SHA-256 哈希对象
            sha256_hash = hashlib.sha256()
            
            # 分块读取文件，以处理大文件，避免一次性加载到内存
            for byte_block in iter(lambda: f.read(4096), b""):
                sha256_hash.update(byte_block)
            
            # 返回十六进制格式的哈希值
            return sha256_hash.hexdigest()

        
    except Exception as e:
        print(f"计算图片哈希值时发生错误: {e}")
        return None


def create_aigc_metadata(content_producer: str, content_id: str, sha256_value: str) -> dict:
    """
    创建符合要求的AIGC元数据结构
    
    参数:
    content_producer (str): 内容生产者信息
    content_id (str): 内容ID的SHA256值
    sha256_value (str): 图片文件的SHA256值
    
    返回:
    dict: 包含AIGC元数据的字典
    """
    metadata = {
        "AIGC": {
            "Label": "AIGC",
            "ContentProducer": content_producer,
            "ProduceID": content_id,
            "ReservedCode1": sha256_value,
            "ContentPropagator": content_producer,  # 与ContentProducer相同
            "PropagateID": content_id,
            "ReservedCode2": sha256_value
        }
    }
    return metadata

# --- 示例用法 ---
if __name__ == "__main__":
    # 1. 准备一个示例图片文件
    # 你需要替换成你自己的图片路径
    # 例如：你可以使用前面提到的D:\dataset\ssh\3105915_8fa602fcada8cec8f26427a84581c318.png
    # 或者创建一个临时的dummy图片文件进行测试
    
    # 创建一个用于测试的虚拟图片文件
    test_image_filename = "./dataset/dft_img/0_result.png"


    image_path_to_hash = test_image_filename # 替换为你的图片文件路径

    sha256_value = generate_image_sha256(image_path_to_hash)
    content_producer = generate_content_producer()
    uid = str(uuid.uuid4())
    content_id = generate_uuid_sha256(uid)

    if sha256_value and content_producer and content_id:
        print(f"\n文件: '{image_path_to_hash}'")
        print(f"content producer: {content_producer}")
        print(f"content id: {content_id}")
        print(f"SHA-256 哈希值: {sha256_value}")
        
        # 创建AIGC元数据
        metadata = create_aigc_metadata(content_producer, content_id, sha256_value)
        print(f"\nAIGC元数据 JSON:")
        print(json.dumps(metadata, indent=2, ensure_ascii=False))
    else:
        print(f"\n无法计算文件 '{image_path_to_hash}' 的信息。")
        if not sha256_value:
            print("SHA-256 哈希值计算失败")
        if not content_producer:
            print("内容生产者生成失败")
        if not content_id:
            print("内容ID生成失败")
