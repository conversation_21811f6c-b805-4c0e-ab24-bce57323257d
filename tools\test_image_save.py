import pyheif
from PIL import Image
import matplotlib.pyplot as plt
import sys

def read_heif(file_path):
    heif_file = pyheif.read(file_path)
    image = Image.frombytes(
        heif_file.mode,
        heif_file.size,
        heif_file.data,
        "raw",
        heif_file.mode,
        heif_file.stride,
    )
    return image

def display_image(image):
    plt.imshow(image)
    plt.axis('off')  # 不显示坐标轴
    plt.show()

# 示例用法
file_path = sys.argv[1]
heif_image = read_heif(file_path)
heif_image.save(sys.argv[2], "PNG")
