#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import sys, getopt
from PIL import Image
from pathlib import Path
import os
import uuid
import io

from ultralytics import YOLO
from supervision import Detections
yolo_path = "./models/models--arnabdhar--YOLOv8-Face-Detection/snapshots/4094ffaba7a6e243801ddb7f5f9a1fae0cf4d78b/model.pt"
yolo_model = YOLO(yolo_path)


from alibabacloud_imageenhan20190930.client import Client
from alibabacloud_imageenhan20190930.models import RemoveImageWatermarkAdvanceRequest, RemoveImageSubtitlesAdvanceRequest
from alibabacloud_tea_openapi.models import Config
from alibabacloud_tea_util.models import RuntimeOptions
from typing import BinaryIO

import requests

config = Config(
  access_key_id='LTAI5t82fT5f57g5KL7nqW5z',
  access_key_secret='******************************',
  endpoint='imageenhan.cn-shanghai.aliyuncs.com',
  region_id='cn-shanghai'
)

import argparse

def parse_args():
    desc = "prepare microscope dataset"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--indir', type=str, default='input file or path', help='[file or path]')
    parser.add_argument('--outdir', type=str, default='dataset/out', help='output data dir')
    parser.add_argument('--img_x', type=int, default=512, help='output image size')
    parser.add_argument('--img_y', type=int, default=512, help='output image size')
    return parser.parse_args()

def get_resolution(width, height, dest_width, dest_height):
    
    width = int(width)
    height = int(height)
    if width < height:
        dw = dest_width
        dh = int(height / (width / dw))
    else:
        dh = dest_height
        dw = int(width / (height / dh))

    return dw, dh

def resize(img:Image, dest_width, dest_height):
    (width, height) = img.size
    rwidth, rheight = get_resolution(width, height, dest_width, dest_height)
    img = img.resize((rwidth, rheight))
    (width, height) = img.size

    outputs = yolo_model(img)
    results = Detections.from_ultralytics(outputs[0])

    if len(results.xyxy) > 0:
        
        face_corner = results.xyxy[0].tolist()
      
        if width == dest_width:
            left = 0
            right = dest_width
            header = int(face_corner[1])
            if header > dest_height//2:
                upper = header - dest_height//2
                lower = upper + dest_height
            else:      
                upper = 0
                lower = dest_height 
        else:
            upper = 0
            lower = dest_height
            header = int(face_corner[0])
            if header > dest_width // 2:
                left = header - dest_width // 2 
                right = left + dest_width
            else:
                left = 0
                right = dest_width
    else:
        return None
        if width > dest_width:
            left = (width - dest_width)//2
            right = left + dest_width 
        else:
            left = 0
            right = dest_width

        if height > dest_height:
            upper = (height - dest_height)//2
            lower = upper + dest_height
        else:    
            upper = 0
            lower = dest_height

    img = img.crop((left, upper, right, lower))
    return img 

def img_remove_tag(img:Image):
    
    img.save("./media/tmp.png", format="PNG")   
    tmp_img = open("./media/tmp.png", "rb")
    
    remove_image_watermark_request = RemoveImageWatermarkAdvanceRequest()
    remove_image_watermark_request.image_urlobject = tmp_img
    runtime = RuntimeOptions()
   
    try:
        # 初始化Client
        client = Client(config)
        response = client.remove_image_watermark_advance(remove_image_watermark_request, runtime)

        resp = response.body
        image_url = resp.data.image_url
        resp = requests.get(image_url)
        
        tmp_img.close()
        
        with open("./media/tmp.png", "wb") as f:
            for chuck in resp.iter_content():
                f.write(chuck)
        
        tmp_img = open("./media/tmp.png", "rb")
                     
        remove_image_subtitle_request = RemoveImageSubtitlesAdvanceRequest()
        remove_image_subtitle_request.image_urlobject = tmp_img
        remove_image_subtitle_request.bx = 0
        remove_image_subtitle_request.by = 0
        remove_image_subtitle_request.bw = 1
        remove_image_subtitle_request.by = 1
        
        response = client.remove_image_subtitles_advance(remove_image_subtitle_request, runtime)
        
        resp = response.body
        image_url = resp.data.image_url
        tmp_img.close()
        
        resp = requests.get(image_url)
        
        out_img = io.BytesIO()
        for chuck in resp.iter_content():
            out_img.write(chuck)
            
        return Image.open(out_img)
    
    except Exception as error:
        print(error)
        return None
            
def process_file(indir:str, outdir:str, img_x:int = 512, img_y:int = 512):

    print(indir)
    os.makedirs(outdir, exist_ok=True)
    img = Image.open(indir)
    img = resize(img, img_x, img_y)

    if img != None:
        img = img_remove_tag(img)
        
    if img != None:    
        file_name = os.path.basename(indir)
        outfile = f"{outdir}/{file_name}"
        idx = 0
        while os.path.exists(outfile):
            outfile = f"{outdir}/{idx}_{file_name}"
            idx += 1
            
        img.save(outfile)
        
def process_directory(indir:str, outdir:str, img_x:int = 512, img_y:int = 512):
       
    for dirpath, dirname, filenames in os.walk(indir):
        for filename in filenames:
            infile = os.path.join(dirpath, filename)
            process_file(infile, outdir, img_x, img_y)

def main():
    args = parse_args()
    os.makedirs(args.outdir, exist_ok=True)
    if os.path.isfile(args.indir):
        process_file(args.indir, args.outdir, args.img_x, args.img_y)
    else:
        process_directory(args.indir, args.outdir, args.img_x, args.img_y)
        
if __name__ == "__main__":
    main()
