#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from fastapi import APIRouter

from .dev import router as dev_router
from .docs import custom_docs
from .user import router as user_router
from .token import router as token_router 
from .voice import router as voice_router
# from .image import router as image_router
from .styles import router as styles_router
# from .instruct import router as instruct_router 

from ..config import settings

api_router = APIRouter(prefix=settings.url_prefix)

if settings.debug:
    api_router.include_router(dev_router)

# api_router.include_router(user_router)
api_router.include_router(token_router)
api_router.include_router(voice_router)
# api_router.include_router(image_router)
# api_router.include_router(instruct_router)
api_router.include_router(styles_router)
