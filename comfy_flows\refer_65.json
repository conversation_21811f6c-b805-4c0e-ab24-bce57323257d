{"6": {"inputs": {"text": ["207", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["38", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["31", 0], "vae": ["39", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "31": {"inputs": {"seed": 37222349368499, "steps": 20, "cfg": 1, "sampler_name": "euler", "scheduler": "simple", "denoise": 1, "model": ["227", 0], "positive": ["35", 0], "negative": ["135", 0], "latent_image": ["124", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "35": {"inputs": {"guidance": 2.5, "conditioning": ["177", 0]}, "class_type": "FluxGuidance", "_meta": {"title": "FluxGuidance"}}, "38": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "39": {"inputs": {"vae_name": "ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "42": {"inputs": {"image": ["146", 0]}, "class_type": "FluxKontextImageScale", "_meta": {"title": "FluxKontextImageScale"}}, "124": {"inputs": {"pixels": ["42", 0], "vae": ["39", 0]}, "class_type": "VAEEncode", "_meta": {"title": "VAE Encode"}}, "135": {"inputs": {"conditioning": ["6", 0]}, "class_type": "ConditioningZeroOut", "_meta": {"title": "ConditioningZeroOut"}}, "146": {"inputs": {"direction": "right", "match_image_size": true, "spacing_width": 0, "spacing_color": "white", "image1": ["202", 0]}, "class_type": "ImageStitch", "_meta": {"title": "Image Stitch"}}, "177": {"inputs": {"conditioning": ["6", 0], "latent": ["124", 0]}, "class_type": "ReferenceLatent", "_meta": {"title": "ReferenceLatent"}}, "193": {"inputs": {"inputcount": 2, "direction": "right", "match_image_size": true, "Update inputs": null, "image_1": ["146", 0], "image_2": ["8", 0]}, "class_type": "ImageConcatMulti", "_meta": {"title": "Image Concatenate Multi"}}, "199": {"inputs": {"width": ["200", 1], "height": ["200", 2], "batch_size": 1}, "class_type": "EmptySD3LatentImage", "_meta": {"title": "EmptySD3LatentImage"}}, "200": {"inputs": {"image": ["232", 0]}, "class_type": "GetImageSizeAndCount", "_meta": {"title": "Get Image Size & Count"}}, "202": {"inputs": {"size": 1024, "mode": true, "images": ["232", 0]}, "class_type": "ImageScaleDownToSize", "_meta": {"title": "Scale Down To Size"}}, "205": {"inputs": {"api_baseurl": "https://dashscope.aliyuncs.com/compatible-mode/v1", "api_key": "sk-9502dc0b92784939b2f1986d897a41ea", "model": "qwen-vl-max", "role": ["223", 0], "prompt": ["237", 0], "temperature": 0.3, "seed": 1674, "speak_and_recognation": {"__value__": [false, true]}, "ref_image": ["232", 0]}, "class_type": "RH_LLMAPI_NODE", "_meta": {"title": "Runninghub LLM API Node"}}, "206": {"inputs": {"image": "2023我去过最美的18座江南古镇古村❗️_1_今天开心了吗_来自小红书网页版.jpg"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "207": {"inputs": {"text": "Transform the image into a pencil sketch style, maintaining the theme and adding <PERSON> on the bridge.", "anything": ["205", 0]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "211": {"inputs": {"text": "在图像中加入白雪公主", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "UserPrompt"}}, "220": {"inputs": {"preset": "Haircut"}, "class_type": "LoadKontextPresets", "_meta": {"title": "Kontext Presets"}}, "222": {"inputs": {"text": "You are a creative prompt engineer. Your mission is to analyze the provided image and generate exactly 1 distinct image transformation *instructions*.\nThe Brief:You are a master of pencil sketching. Write prompt words to transform the picture into a pencil sketch style based on the image content. The description needs to be precise and detailed, and avoid describing content unrelated to the task.\nYour response must consist of concise instruction ready for the image editing AI. Do not add any conversational text, explanations, or deviations; only the instructions.", "anything": ["223", 0]}, "class_type": "easy showAnything", "_meta": {"title": "Show Any"}}, "223": {"inputs": {"preset": "铅笔手绘"}, "class_type": "LoadKontextPresetslarge", "_meta": {"title": "KontextPresets"}}, "225": {"inputs": {"unet_name": "flux1-kontext-dev-Q5_K_M.gguf", "dequant_dtype": "bfloat16", "patch_dtype": "bfloat16", "patch_on_device": false}, "class_type": "UnetLoaderGGUFAdvanced", "_meta": {"title": "Unet Loader (GGUF/Advanced)"}}, "226": {"inputs": {"lora_name": "flux/Flux.1-Labubu泡泡玛特-开放下载_labubu3D-v1.safetensors", "strength_model": 1, "model": ["225", 0]}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "227": {"inputs": {"model_type": "flux", "rel_l1_thresh": 0.4, "start_percent": 0, "end_percent": 1, "cache_device": "cuda", "model": ["226", 0]}, "class_type": "TeaCache", "_meta": {"title": "TeaCache"}}, "229": {"inputs": {"text": "用户输入主题为：", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "Text Multiline"}}, "230": {"inputs": {"delimiter": ".", "clean_whitespace": "true", "text_b": ["229", 0], "text_c": ["223", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "231": {"inputs": {"width": 1024, "height": 1024, "aspect_ratio": "3:4 portrait 896x1152", "swap_dimensions": "Off", "upscale_factor": 1, "batch_size": 1}, "class_type": "CR SDXL Aspect Ratio", "_meta": {"title": "AspectRatio"}}, "232": {"inputs": {"width": ["231", 0], "height": ["231", 1], "upscale_method": "lanc<PERSON>s", "keep_proportion": false, "divisible_by": 56, "crop": 0, "image": ["206", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image (deprecated)"}}, "233": {"inputs": {"anything": ["8", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}, "234": {"inputs": {"filename_prefix": "refer/kontext", "images": ["8", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "235": {"inputs": {"lora_name": "flux/MZ可爱小猫怼脸摄影_v1.safetensors", "strength_model": 1}, "class_type": "LoraLoaderModelOnly", "_meta": {"title": "LoraLoaderModelOnly"}}, "236": {"inputs": {"text": "。用英文回答", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "Text Multiline"}}, "237": {"inputs": {"delimiter": ".", "clean_whitespace": "true", "text_a": ["239", 0], "text_b": ["211", 0], "text_c": ["236", 0]}, "class_type": "Text Concatenate", "_meta": {"title": "Text Concatenate"}}, "239": {"inputs": {"text": "保持图片主题，", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "Text Multiline"}}}