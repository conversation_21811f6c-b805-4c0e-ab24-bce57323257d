[loggers]
keys=root,req,img

[handlers]
keys=logconsole,logfile,reqfile,imgfile

[formatters]
keys=logformatter,imgformatter

[logger_root]
level=INFO
handlers=logconsole,logfile

[logger_img]
level=INFO
handlers=imgfile
qualname=img
propagate=0

[logger_req]
level=INFO
handlers=reqfile
qualname=req
propagate=0

[formatter_logformatter]
format=[%(asctime)s.%(msecs)03d] %(levelname)s [%(thread)d] - %(message)s

[formatter_imgformatter]
format=%(message)s

[handler_logconsole]
class=handlers.logging.StreamHandler
level=INFO
args=()
formatter=logformatter

[handler_logfile]
class=handlers.ConcurrentTimedRotatingFileHandler
kwargs={'filename':'./logs/ai-data.log', 'when':'M', 'interval':20, 'maxBytes':0, 'backupCount':1}
level=INFO
formatter=logformatter

[handler_imgfile]
class=handlers.ConcurrentTimedRotatingFileHandler
kwargs={'filename':'./logs/img-data.log', 'when':'M', 'interval':20, 'maxBytes':0, 'backupCount':1}
level=INFO
formatter=imgformatter

[handler_reqfile]
class=handlers.ConcurrentTimedRotatingFileHandler
kwargs={'filename':'./logs/req-data.log', 'when':'M', 'interval':20, 'maxBytes':0, 'backupCount':1}
level=INFO
formatter=imgformatter







