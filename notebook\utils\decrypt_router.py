import gzip
from typing import Callable, List
from fastapi import <PERSON>AP<PERSON>
from fastapi.routing import APIRoute
from starlette.requests import Request
from starlette.responses import Response

from .cipher_lib import CipherAgent
cipher_tool = CipherAgent().get_cipher()

class DecryptRequest(Request):
    
    async def body(self) -> bytes:
        if not hasattr(self, "_body"):
            body = await super().body()
            if "gzip" in self.headers.getlist("Content-Encoding"):
                body = gzip.decompress(body)
            host = self.client.host
            if host.startswith("192.") or host.startswith("10.") \
                or host.startswith("127.") or host.startswith("172."):
                    encrypt = self.query_params.get("encrypt", None)
                    if not encrypt:
                        self._body = body
                        return self._body

            body = cipher_tool.decrypt(body.decode(encoding="utf-8"))
            self._body = body.encode(encoding="utf-8")
                
        return self._body

class DecryptRoute(APIRoute):

    def get_route_handler(self) -> Callable:
        original_route_handler = super().get_route_handler()

        async def custom_route_handler(request: Request) -> Response:
            request = DecryptRequest(request.scope, request.receive)
            response = await original_route_handler(request)
            return response

        return custom_route_handler

