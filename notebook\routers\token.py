#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from fastapi import APIRouter, Body, Form, Depends, Query
from ..schemas.user import UserJwt, Token
from ..utils import RespCode
from ..auth import signJWT, decodeJWT
from ..auth import JWTBearer
from ..utils import DecryptRoute
import time

router = APIRouter(prefix='/token', tags=['用户接口'])
router.route_class = DecryptRoute

@router.post("/get_token", summary="获取用户token信息")
async def get_token(user: UserJwt = Body(...)):
    token = signJWT(user)
    return RespCode.resp_ok({"token":token})

@router.post("/refresh", summary="校验token")
async def refresh_token(token: Token = Body(...)):
    payload = decodeJWT(token.token)

    if payload:
        try:
            if  payload["expires"] < time.time():
                user = UserJwt.parse_obj(payload)
                token = signJWT(user)
                return RespCode.resp_ok({"token":token})
            else:
                return RespCode.resp_ok(token)
        except Exception as e:
            return RespCode.resp_err(RespCode.INVALID_TOKEN, "无效的token.")
    else:
        return RespCode.resp_err(RespCode.INVALID_TOKEN, "无效的token.")

@router.post("/test_injection", dependencies=[Depends(JWTBearer())], summary="验证依赖注入")
async def test_injection(token: str = Form(...)):
    payload = decodeJWT(token)
    if payload:
        if  payload["expires"] > time.time():
            return token
        else:
            user = UserJwt().parse_obj(payload)
            token = signJWT(user)
            return RespCode.resp_ok({"token":token})
    else:
        return RespCode.resp_err(RespCode.INVALID_TOKEN, "无效的token.")
   



