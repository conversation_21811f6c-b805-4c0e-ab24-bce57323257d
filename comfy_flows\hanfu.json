{"16": {"inputs": {"filename_prefix": "hanfu/hanfu", "images": ["150", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "17": {"inputs": {"image": "test.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "27": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "ha<PERSON><PERSON>, S<lora:HanfuTang_SDXL:0.7> ", "text_b": ["122", 1], "text_c": "", "speak_and_recognation": true}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "32": {"inputs": {"clip_name": "CLIP_ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "57": {"inputs": {"ckpt_name": "DreamShaperXL_Lightning-SFW.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "59": {"inputs": {"text": ["27", 0], "speak_and_recognation": true, "clip": ["126", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "60": {"inputs": {"text": "(nsfw:1.3), lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry,embedding:ng_deepnegative_v1_75t, ", "speak_and_recognation": true, "clip": ["126", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "61": {"inputs": {"lora_name": "sdxl/HanfuTang_SDXL.safetensors", "strength_model": 0.7000000000000001, "strength_clip": 0.7000000000000001, "model": ["57", 0], "clip": ["57", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "67": {"inputs": {"samples": ["140", 0], "vae": ["57", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "122": {"inputs": {"task": "region to description", "text_input": "", "max_new_tokens": 1024, "num_beams": 3, "do_sample": false, "fill_mask": false, "FLORENCE2": ["123", 0], "image": ["158", 0]}, "class_type": "Florence2", "_meta": {"title": "Florence2"}}, "123": {"inputs": {"version": "large-ft"}, "class_type": "LoadFlorence2Model", "_meta": {"title": "Load Florence2 Model"}}, "126": {"inputs": {"lora_name": "ipadapter/ip-adapter-faceid-plusv2_sdxl_lora.safetensors", "strength_model": 0.8, "strength_clip": 0.8, "model": ["61", 0], "clip": ["61", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "140": {"inputs": {"seed": 442825270164387, "steps": 8, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1, "model": ["153", 0], "positive": ["145", 0], "negative": ["60", 0], "latent_image": ["141", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "141": {"inputs": {"width": 768, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "145": {"inputs": {"strength": 0.8, "conditioning": ["59", 0], "control_net": ["146", 0], "image": ["149", 0]}, "class_type": "ControlNetApply", "_meta": {"title": "Apply ControlNet"}}, "146": {"inputs": {"control_net_name": "controlnet-union-sdxl/diffusion_pytorch_model.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "149": {"inputs": {"preprocessor": "DepthAnythingPreprocessor", "resolution": 1024, "image": ["158", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "150": {"inputs": {"enabled": true, "swap_model": "inswapper_128.onnx", "facedetection": "retinaface_resnet50", "face_restore_model": "codeformer-v0.1.0.pth", "face_restore_visibility": 1, "codeformer_weight": 0.5, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "input_image": ["67", 0], "source_image": ["157", 0]}, "class_type": "ReActorFaceSwap", "_meta": {"title": "ReActor 🌌 Fast Face Swap"}}, "153": {"inputs": {"weight": 1, "weight_type": "linear", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["154", 0], "ipadapter": ["154", 1], "image": ["157", 0], "clip_vision": ["32", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "154": {"inputs": {"preset": "PLUS FACE (portraits)", "model": ["126", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "157": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["158", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "158": {"inputs": {"width": 768, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["17", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "159": {"inputs": {"anything": ["150", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean GPU Used"}}}