#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import argparse
import glob
import os
from pyiqa import create_metric
import cv2 
import torch
from pyiqa.utils.img_util import img2tensor
import pyiqa

def musiq_koniq(input_img:str):
    img = cv2.imread(input_img)
    img = cv2.resize(img, (512,384))

    img_tensor = img2tensor(img)
    img_tensor = img_tensor.unsqueeze(0)

    metric_name = "musiq-koniq"
    iqa_model = create_metric(metric_name, metric_mode="NR")
    score = iqa_model(img_tensor, None).cpu().item()
    return score

def brisque(input_img:str):
    
    img = cv2.imread(input_img)
    img = cv2.resize(img, (512,384))

    img_tensor = img2tensor(img)
    img_tensor = img_tensor.unsqueeze(0)

    metric_name = "brisque"
    iqa_model = create_metric(metric_name, metric_mode="NR")
    score = iqa_model(img_tensor, None).cpu().item()
    return score

def hyperiqa(input_img:str):
    
    img = cv2.imread(input_img)
    img = cv2.resize(img, (512,384))

    img_tensor = img2tensor(img)
    img_tensor = img_tensor.unsqueeze(0)

    metric_name = "hyperiqa"
    iqa_model = create_metric(metric_name, metric_mode="NR")
    score = iqa_model(img_tensor, None).cpu().item()
    return score

def paq2piq(input_img:str):
    
    img = cv2.imread(input_img)
    img = cv2.resize(img, (512,384))

    img_tensor = img2tensor(img)
    img_tensor = img_tensor.unsqueeze(0)

    metric_name = "paq2piq"
    iqa_model = create_metric(metric_name, metric_mode="NR")
    score = iqa_model(img_tensor, None).cpu().item()
    return score

if __name__ == "__main__":
    
    print(pyiqa.list_models())
    
    # print("paq2piq", paq2piq(r"D:\train-material\a.png"))
    # print("hyperiqa", hyperiqa(r"D:\train-material\a.png"))
    print("brisque", brisque(r"D:\train-material\a.png"))  
    print("musiq-koniq", musiq_koniq(r"D:\train-material\a.png"))

    
    
