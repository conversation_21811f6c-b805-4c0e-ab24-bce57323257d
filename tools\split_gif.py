import shutil
import os
import csv
import argparse
from PIL import Image 
import base64
import io 
import re 

def parse_args():
    desc = "batch memo"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--indir', type=str, default='input file', help='input file')
    parser.add_argument("--outdir", type=str, default='output file path', help='output directory')
    return parser.parse_args()

def process_file(src_file:str, outdir:str):

    os.makedirs(outdir, exist_ok=True)
    filepath, filename = os.path.split(src_file)
    # 打开GIF文件
    gif = Image.open(src_file)

    # 获取GIF的帧数
    frames = gif.n_frames

    # 逐帧读取并保存每一帧图片
    for i in range(frames):
        gif.seek(i)
        frame = gif.copy()
        frame.save(f'{outdir}/frame_{i}.png')
    print(f'共读取了{frames}帧图片')
    
def main():
    
    args = parse_args()
    if os.path.isfile(args.indir):
        process_file(args.indir, args.outdir)
    
        
if __name__ == "__main__":
    main()



