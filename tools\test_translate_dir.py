#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import requests
import argparse
import os
import re 

os.environ["HTTP_PROXY"]= "http://127.0.0.1:1080"
os.environ["HTTPS_PROXY"]= "http://127.0.0.1:1080"

def translate_post(target:str, text:str):
    credentials = "AIzaSyCo2Jly7begyK0X4UDBmIOsxW5mpEGAejI"
    g_url = "https://translation.googleapis.com/language/translate/v2"
    data = {
            "q":text, 
            "target":target,
            "format":"text",
            "source":"zh-CN",
            "model":"nmt",
            "key":credentials
            }
    resp = requests.post(g_url, data=data)
    content = resp.json()
    return " ".join([t["translatedText"] for t in content["data"]["translations"]])

def process_file(infile, lang):

    chinese_pattern = re.compile(r'[\u4e00-\u9fff]+')
    
    paths, filename = os.path.split(infile)
    destname = filename.replace(".", f"_{lang}.")
    with open(os.path.join(paths, destname), "w", encoding="utf-8") as dest:
        with open(infile, "r", encoding="utf-8") as src:
            for line in src:
                line = line.strip()
                s = chinese_pattern.search(line)
                if s:
                    line = translate_post(lang, line)
                dest.write(line + "\n")
                            
def translate(infile, lang):
    
    if os.path.isfile(infile):
        process_file(infile, lang)        
    else:
        for p, dirs, files in os.walk(infile):
            for name in files:
                partfile = os.path.join(p, name)
                process_file(partfile, lang)
    
def parse_args():
    desc = "translate file to file"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--infile', type=str, default='./test.txt', help='[翻译的内容]')
    parser.add_argument('--lang', type=str, default='en, pt-br, pt-pt', help='[翻译的内容]')
       
    return parser.parse_args()

def main():
    args = parse_args()
    rs = translate(args.infile, args.lang)
    print(rs)

if __name__ == "__main__":
    #python .\tools\test_translate_dir.py --infile D:\banyunjuhe\职场升职记中文字幕 --lang pt-br
    main()
