from PIL import Image
from PIL import ImageDraw, ImageFont
  
marker = "般芸聚合"   
image = Image.new('RGBA',(128,128),"white")

(width, height) = image.size
font = ImageFont.truetype("static/fonts/SIMLI.TTF", 20)
draw = ImageDraw.Draw(image)

# position = (width//2, height//2)
position = (5, height - 25)
    
left, top, right, bottom  = draw.textbbox(position, marker, font=font)
draw.text(position, marker, font=font, fill=(0,0,0,255))
# draw.rectangle((left -5, top -5, right + 5, bottom + 5), fill=(200,200,200,0))


image.save("./media/123.png")


