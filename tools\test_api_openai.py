#!/usr/bin/env python 
# -*- coding:utf-8 -*-
# DALLE-3
#

import requests
import base64
import os
import re 
import json
import random
import argparse
import io
import openai
from openai import OpenAI

from PIL import Image, PngImagePlugin
import os 
import uuid
from ultralytics import YOLO
from supervision import Detections
yolo_path = "./models/models--arnabdhar--YOLOv8-Face-Detection/snapshots/4094ffaba7a6e243801ddb7f5f9a1fae0cf4d78b/model.pt"
yolo_model = YOLO(yolo_path)

os.environ["OPENAI_API_KEY"] = '***************************************************'
os.environ["HTTP_PROXY"] = "http://127.0.0.1:1080"
os.environ["HTTPS_PROXY"] = "http://127.0.0.1:1080"

openai.api_key = '***************************************************'
openai.proxy = "http://127.0.0.1:1080"

client = OpenAI()
import traceback 

def get_resolution(width, height, dest_width, dest_height):
    
    width = int(width)
    height = int(height)
    if width < height:
        dw = dest_width
        dh = int(height / (width / dw))
    else:
        dh = dest_height
        dw = int(width / (height / dh))

    return dw, dh

def resize(img:Image, dest_width, dest_height):
    (width, height) = img.size
    rwidth, rheight = get_resolution(width, height, dest_width, dest_height)
    img = img.resize((rwidth, rheight))
    (width, height) = img.size

    outputs = yolo_model(img)
    results = Detections.from_ultralytics(outputs[0])

    if len(results.xyxy) > 0:
        
        face_corner = results.xyxy[0].tolist()
      
        if width == dest_width:
            left = 0
            right = dest_width
            header = int(face_corner[1])
            if header > dest_height//2:
                upper = header - dest_height//2
                lower = upper + dest_height
            else:      
                upper = 0
                lower = dest_height 
        else:
            upper = 0
            lower = dest_height
            header = int(face_corner[0])
            if header > dest_width // 2:
                left = header - dest_width // 2 
                right = left + dest_width
            else:
                left = 0
                right = dest_width
    else:
        return None
    
    img = img.crop((left, upper, right, lower))
    return img 

def save_img(images:list[str], outdir: str, name_prefix: str):
    
    for i, img in enumerate(images):
     
        prefix = "%06d" % (i)
        suffix = str(uuid.uuid4())
        outfile = os.path.join(outdir, f"{name_prefix}_{prefix}_{suffix}.png")
        while os.path.exists(outfile):
            i +=  1
            prefix = "%06d" % (i)
            outfile = os.path.join(outdir, f"{name_prefix}_{prefix}.png")
        
        with open(outfile, "wb") as f:
            f.write(base64.b64decode(img.b64_json))
            
def gen_image(outdir:str, img, name_prefix:str):

    os.makedirs(outdir, exist_ok=True)
    img = resize(img, 512, 512)
    
    # Convert the image to a BytesIO object
    byte_stream = io.BytesIO()
    img.save(byte_stream, format='PNG')
    byte_array = byte_stream.getvalue()
    
    try:
        response = client.images.create_variation(
            image=byte_array,
            n=1,
            size="512x512",
            response_format = "b64_json"
        )
        save_img(response.data, outdir, name_prefix)
    except openai.OpenAIError as e:
        expr = traceback.format_exc() 
        print(expr)   
            
def encode_image(image):

    with io.BytesIO() as output_bytes:
        metadata = None
        for key, value in image.info.items():
            if isinstance(key, str) and isinstance(value, str):
                if metadata is None:
                    metadata = PngImagePlugin.PngInfo()
                metadata.add_text(key, value)
        image.save(output_bytes, format="PNG", pnginfo=metadata)
        bytes_data = output_bytes.getvalue()
        
        ret = str(base64.b64encode(bytes_data), "utf-8")
        return ret 

def main(args):

    outdir = args.outdir
  
    if os.path.isfile(args.indir):
        name = args.indir
        if name.endswith(".png") or name.endswith(".jpg") or name.endswith(".jpeg"):
            img = Image.open(name)
            name_prefix = os.path.split(name.split(".")[0])[1]
            gen_image(outdir, img, name_prefix)
            
    else:
        
        for p, dirs, files in os.walk(args.indir):
            for name in files:
                if name.endswith(".png") or name.endswith(".jpg") or name.endswith(".jpeg"):
                    print(os.path.join(p, name))
                    img = Image.open(os.path.join(p, name))
                    name_prefix = name.split(".")[0]
                    gen_image(outdir, img, name_prefix)

def setup_parser() -> argparse.ArgumentParser:

    parser = argparse.ArgumentParser()
    parser.add_argument("--indir", type=str, required=True, default=None, help="sence to generate")
    parser.add_argument("--outdir", type=str, required=True, default="outputs", help="dir to write results to")
    return parser

if __name__ == "__main__":
    
    parser = setup_parser()
    args = parser.parse_args()
    main(args)


