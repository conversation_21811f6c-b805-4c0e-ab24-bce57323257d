#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import os
os.environ["HTTP_PROXY"]= "http://127.0.0.1:1080"
os.environ["HTTPS_PROXY"]= "http://127.0.0.1:1080"

import PIL
import requests
import torch
from diffusers import StableDiffusionInstructPix2PixPipeline, EulerAncestralDiscreteScheduler
import pandas as pd
import matplotlib.pyplot as plt
import numpy as np 

model_id = "timbrooks/instruct-pix2pix"
cache_dir="./models/instruct-pix2pix"
pipe = StableDiffusionInstructPix2PixPipeline.from_pretrained(model_id, resume=True, cache_dir=cache_dir, torch_dtype=torch.float16, safety_checker=None)
pipe.to("cuda")
pipe.scheduler = EulerAncestralDiscreteScheduler.from_config(pipe.scheduler.config)

url = "https://raw.githubusercontent.com/timothybrooks/instruct-pix2pix/main/imgs/example.jpg"
def download_image(url):
    image = PIL.Image.open(requests.get(url, stream=True).raw)
    image = PIL.ImageOps.exif_transpose(image)
    image = image.convert("RGB")
    return image
image = download_image(url)

prompt = "turn him into cyborg"
images = pipe(prompt, image=image, num_inference_steps=10, image_guidance_scale=1).images

img = np.array(images[0])
plt.imshow(img)
plt.show()




