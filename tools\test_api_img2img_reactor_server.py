#!/usr/bin/env python 
# -*- coding:utf-8 -*-
# 调用接口 一键换脸
#

import requests
import base64
import os
import re 
import json
import random
import argparse
import io
import uuid

from PIL import Image, PngImagePlugin
base_url="http://************:7860"

def url_reactor():
    return f"{base_url}/reactor/image"

def url_img2img():
    return f"{base_url}/sdapi/v1/img2img"

def url_blip():
    return f"{base_url}/interrogator/prompt"

def url_interrogate():
    return f"{base_url}/sdapi/v1/interrogate"

def simple_interrogate_params(img:str):
    return {
        "image": img,
        "model": "clip"
    }
    
def simple_blip_params(img:str):
    return {
        "image": img,
        "clip_model_name": "ViT-L-14/openai",
        "mode": "fast"
    }
    
def sample_adetailer_params():
    return  {
          "ad_model": "face_yolov8n.pt",
          "ad_prompt": "",
          "ad_negative_prompt": ""
        }
    
def simple_img_reactor_params(model:str):

    # ReActor arguments:
    args=[
        None, #0
        True, #1 Enable ReActor
        '0', #2 Comma separated face number(s) from swap-source image
        '0', #3 Comma separated face number(s) for target image (result)
        '/workspace/stable-diffusion-webui/models/insightface/inswapper_128.onnx', #4 model path
        'CodeFormer', #4 Restore Face: None; CodeFormer; GFPGAN
        1, #5 Restore visibility value
        False, #7 Restore face -> Upscale
        None, #8 Upscaler (type 'None' if doesn't need), see full list here: http://127.0.0.1:7860/sdapi/v1/script-info -> reactor -> sec.8
        1, #9 Upscaler scale value
        1, #10 Upscaler visibility (if scale = 1)
        False, #11 Swap in source image
        True, #12 Swap in generated image
        1, #13 Console Log Level (0 - min, 1 - med or 2 - max)
        0, #14 Gender Detection (Source) (0 - No, 1 - Female Only, 2 - Male Only)
        0, #15 Gender Detection (Target) (0 - No, 1 - Female Only, 2 - Male Only)
        False, #16 Save the original image(s) made before swapping
        0.5, #17 CodeFormer Weight (0 = maximum effect, 1 = minimum effect), 0.5 - by default
        False, #18 Source Image Hash Check, True - by default
        False, #19 Target Image Hash Check, False - by default
        "CUDA", #20 CPU or CUDA (if you have it), CPU - by default
        True, #21 Face Mask Correction
        1, #22 Select Source, 0 - Image, 1 - Face Model, 2 - Source Folder
        model, #23 Filename of the face model (from "models/reactor/faces"), e.g. elena.safetensors, don't forger to set #22 to 1
        "", #24 The path to the folder containing source faces images, don't forger to set #22 to 2
        None, #25 skip it for API
        True, #26 Randomly select an image from the path
        True, #27 Force Upscale even if no face found
        0.6, #28 Face Detection Threshold
        1, #29 Maximum number of faces to detect (0 is unlimited)
    ]
    return args 

def simple_img2img_request():
    
    return {
        "prompt": "",
        "negative_prompt": "EasyNegative",
        "sytles":[],
        "seed": -1,
        "subseed": -1,
        "subseed_strength": 0,
        "seed_resize_from_h": -1,
        "seed_resize_from_w": -1,
        "batch_size": 1,
        "n_iter": 1,
        "steps": 20,
        "cfg_scale": 5,
        "width": 512,
        "height": 768,
        "restore_faces": True,
        "tiling": False,
        "eta": 0,
        "denoising_strength": 0.1,
        "disable_extra_networks": False,
        "comments": {},
        "init_images": [],
        "resize_mode": 0,
        "sampler_index": "DPM++ 2M Karras",
        "include_init_images": True,
        "script_name": None,
        "script_args": [],
        "send_images": True,
        "save_images": False,
        "alwayson_scripts": {}
    }

def adetailer_args(prompt:str):

    face_args = sample_adetailer_params()
    face_args["ad_model"] = "face_yolov8n.pt"
    face_args["ad_prompt"] = f"{prompt},(best illustration),(best shadow),perfect face,finely detail,masterpiece,ultra-detailed,highres"
    
    params = {}
    params["args"] = [
        True,
        False,    
        face_args,
    ]
    return params
    
def save_img(images:list[str], outdir: str, name_prefix: str):
    
    for i, img in enumerate(images):

        if i >= 4:
            break
        
        prefix = "%06d" % (i)
        suffix = str(uuid.uuid4())
        outfile = os.path.join(outdir, f"{name_prefix}_{prefix}_{suffix}.png")
        while os.path.exists(outfile):
            i +=  1
            prefix = "%06d" % (i)
            outfile = os.path.join(outdir, f"{name_prefix}_{prefix}.png")

        with open(outfile, "wb") as f:
            f.write(base64.b64decode(img))
        

def img_reactor(outdir:str, model:str, tgt_img:str, name_prefix:str):
    
    # prompt = requests.post(url_blip(), json=simple_blip_params(tgt_img))
    # prompt = requests.post(url_interrogate(), json=simple_interrogate_params(tgt_img))
    
    params = simple_img2img_request()
    prompt = f"(ultra high res),(highly detailed)"
    params["prompt"] = prompt
    params["init_images"].append(tgt_img)
    params["alwayson_scripts"] =  {"reactor":{"args":simple_img_reactor_params(model)}, "ADetailer": adetailer_args(prompt)}
    
    resp = requests.post(url_img2img(), json=params)
    resp_json = resp.json()
    
    save_img(resp_json["images"], outdir, name_prefix)

def gen_image(outdir:str, model : str, tgt_img : str, name_prefix:str):

    os.makedirs(outdir, exist_ok=True)
    img_reactor(outdir, model, tgt_img, name_prefix)

def encode_image(image):

    with io.BytesIO() as output_bytes:
        metadata = None
        for key, value in image.info.items():
            if isinstance(key, str) and isinstance(value, str):
                if metadata is None:
                    metadata = PngImagePlugin.PngInfo()
                metadata.add_text(key, value)
        image.save(output_bytes, format="PNG", pnginfo=metadata)
        bytes_data = output_bytes.getvalue()
        
        ret = str(base64.b64encode(bytes_data), "utf-8")
        return ret 

def main(args):

    outdir = args.outdir
    facemodel = args.facemodel
    faceList = []
    for face in facemodel.split(","):
        faceList.append(f"{face}.safetensors")
                            
    if os.path.isfile(args.indir):
        name = args.indir
        if name.endswith(".png") or name.endswith(".jpg") or name.endswith(".jpeg"):
            img = Image.open(name)
            img = encode_image(img)
            
            name_prefix = os.path.split(name.split(".")[0])[1]
            for i, model  in enumerate(faceList):
                gen_image(outdir, model, img, f"{i}_{name_prefix}")
    else:
        for p, dirs, files in os.walk(args.indir):
            for name in files:
                if name.endswith(".png") or name.endswith(".jpg") or name.endswith(".jpeg"):
                    print(os.path.join(p, name))
                    img = Image.open(os.path.join(p, name))
                    img = encode_image(img)
                    name_prefix = name.split(".")[0]
                    for i, model  in enumerate(faceList):
                        gen_image(outdir, model, img, f"{i}_{name_prefix}")

def setup_parser() -> argparse.ArgumentParser:

    parser = argparse.ArgumentParser()
    parser.add_argument("--indir", type=str, required=True, default=None, help="sence to generate")
    parser.add_argument("--facemodel", type=str, required=True, default=None, help="facemodel to replace")
    parser.add_argument("--outdir", type=str, required=True, default="outputs", help="dir to write results to")
    return parser

if __name__ == "__main__":
    
    parser = setup_parser()
    args = parser.parse_args()
    main(args)


