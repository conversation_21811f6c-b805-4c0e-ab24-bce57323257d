{"10": {"inputs": {"unet_name": "flux1-dev-fp8.safetensors", "weight_dtype": "fp8_e4m3fn"}, "class_type": "UNETLoader", "_meta": {"title": "Load Diffusion Model"}}, "11": {"inputs": {"lora_name": "flux/Flux.1-Labubu泡泡玛特-开放下载_labubu3D-v1.safetensors", "strength_model": 1.5000000000000002, "strength_clip": 1.5000000000000002, "model": ["10", 0], "clip": ["12", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "12": {"inputs": {"clip_name1": "clip_l.safetensors", "clip_name2": "t5xxl_fp8_e4m3fn.safetensors", "type": "flux", "device": "default"}, "class_type": "DualCLIPLoader", "_meta": {"title": "DualCLIPLoader"}}, "15": {"inputs": {"noise": ["16", 0], "guider": ["21", 0], "sampler": ["36", 0], "sigmas": ["37", 0], "latent_image": ["39", 0]}, "class_type": "SamplerCustomAdvanced", "_meta": {"title": "SamplerCustomAdvanced"}}, "16": {"inputs": {"noise_seed": 2310232945581}, "class_type": "RandomNoise", "_meta": {"title": "RandomNoise"}}, "18": {"inputs": {"text": ["22", 0], "speak_and_recognation": {"__value__": [false, true]}, "clip": ["11", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "21": {"inputs": {"model": ["48", 0], "conditioning": ["24", 0]}, "class_type": "BasicGuider", "_meta": {"title": "BasicGuider"}}, "22": {"inputs": {"text_1": ["57", 0], "text_2": ["59", 2], "text_3": "", "text_4": ""}, "class_type": "LayerUtility: Text<PERSON>oin", "_meta": {"title": "LayerUtility: Text<PERSON>oin"}}, "24": {"inputs": {"downsampling_factor": 3, "downsampling_function": "area", "mode": "keep aspect ratio", "weight": 0.8, "autocrop_margin": 0.1, "conditioning": ["18", 0], "style_model": ["25", 0], "clip_vision": ["26", 0], "image": ["33", 0]}, "class_type": "ReduxAdvanced", "_meta": {"title": "ReduxAdvanced"}}, "25": {"inputs": {"style_model_name": "flux1-redux-dev.safetensors"}, "class_type": "StyleModelLoader", "_meta": {"title": "Load Style Model"}}, "26": {"inputs": {"clip_name": "sigclip_vision_patch14_384.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "33": {"inputs": {"image": "rgthree.compare._temp_lygba_00002_.png"}, "class_type": "LoadImage", "_meta": {"title": "InputImage"}}, "36": {"inputs": {"sampler_name": "lcm"}, "class_type": "KSamplerSelect", "_meta": {"title": "KSamplerSelect"}}, "37": {"inputs": {"scheduler": "normal", "steps": 25, "denoise": 1, "model": ["11", 0]}, "class_type": "BasicScheduler", "_meta": {"title": "BasicScheduler"}}, "39": {"inputs": {"width": ["42", 3], "height": ["42", 4], "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "42": {"inputs": {"aspect_ratio": "original", "proportional_width": 1, "proportional_height": 1, "fit": "letterbox", "method": "lanc<PERSON>s", "round_to_multiple": "8", "scale_to_side": "None", "scale_to_length": 1024, "background_color": "#000000", "image": ["33", 0]}, "class_type": "LayerUtility: ImageScaleByAspectRatio V2", "_meta": {"title": "LayerUtility: ImageScaleByAspectRatio V2"}}, "44": {"inputs": {"samples": ["15", 0], "vae": ["45", 0]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "45": {"inputs": {"vae_name": "flux_ae.safetensors"}, "class_type": "VAELoader", "_meta": {"title": "Load VAE"}}, "46": {"inputs": {"filename_prefix": "ComfyUI", "images": ["44", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "48": {"inputs": {"weight": 0.8, "start_at": 0, "end_at": 1, "model": ["54", 0], "pulid_flux": ["49", 0], "eva_clip": ["58", 0], "face_analysis": ["51", 0], "image": ["42", 0]}, "class_type": "ApplyPulidFlux", "_meta": {"title": "Apply PuLID Flux"}}, "49": {"inputs": {"pulid_file": "pulid_flux_v0.9.1.safetensors"}, "class_type": "PulidFluxModelLoader", "_meta": {"title": "Load PuLID Flux Model"}}, "51": {"inputs": {"provider": "CUDA"}, "class_type": "PulidFluxInsightFaceLoader", "_meta": {"title": "Load InsightFace (PuLID Flux)"}}, "54": {"inputs": {"rel_l1_thresh": 0.4, "cache_device": "offload_device", "wan_coefficients": "disabled", "model": ["11", 0]}, "class_type": "ApplyTeaCachePatch", "_meta": {"title": "ApplyTeaCachePatch"}}, "57": {"inputs": {"text": "<PERSON><PERSON><PERSON>,artistic toy creation,Blind box toy,character design,large round head,small nose,wide smile,white teeth,fluffy,fluffy texture", "speak_and_recognation": {"__value__": [false, true]}}, "class_type": "Text Multiline", "_meta": {"title": "UserPrompt1"}}, "58": {"inputs": {}, "class_type": "PulidFluxEvaClipLoader", "_meta": {"title": "Load <PERSON> (PuLID Flux)"}}, "59": {"inputs": {"model": "promptgen_base_v2.0", "folder_path": "Path to your image folder", "caption_method": "extra_mixed", "max_new_tokens": 1024, "num_beams": 4, "random_prompt": "never", "prefix_caption": "", "suffix_caption": "", "replace_tags": "replace_tags eg:search1:replace1;search2:replace2", "speak_and_recognation": {"__value__": [false, true]}, "images": ["42", 0]}, "class_type": "<PERSON><PERSON><PERSON><PERSON>_Tagger", "_meta": {"title": "🐾MiaoshouAI Tagger"}}, "61": {"inputs": {"anything": ["44", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean VRAM Used"}}}