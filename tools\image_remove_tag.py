#!/usr/bin/env python 
# -*- coding:utf-8 -*-

#pip install alibabacloud_imageenhan20190930

import sys, getopt
from pathlib import Path
import os
import io 
import argparse

from alibabacloud_imageenhan20190930.client import Client
from alibabacloud_imageenhan20190930.models import RemoveImageWatermarkAdvanceRequest, RemoveImageSubtitlesAdvanceRequest
from alibabacloud_tea_openapi.models import Config
from alibabacloud_tea_util.models import RuntimeOptions
import requests

config = Config(
  access_key_id='LTAI5t82fT5f57g5KL7nqW5z',
  access_key_secret='******************************',
  endpoint='imageenhan.cn-shanghai.aliyuncs.com',
  region_id='cn-shanghai'
)

def parse_args():
    desc = "encode image to base64 string format"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--img', type=str, default='image file', help='[image file to be encoded]')
    parser.add_argument('--out', type=str, default='base64 result to a file', help='[base64 str to be saved]')
    return parser.parse_args()

def process_file(image:str, out:str):
    
    img = open(image, 'rb')
    remove_image_watermark_request = RemoveImageWatermarkAdvanceRequest()
    remove_image_watermark_request.image_urlobject = img
    runtime = RuntimeOptions()
    try:
        # 初始化Client
        client = Client(config)
        response = client.remove_image_watermark_advance(remove_image_watermark_request, runtime)

        # 获取整体结果
        resp = response.body
        image_url = resp.data.image_url
        resp = requests.get(image_url)
        with open(out, "wb") as f:
            for chuck in resp.iter_content():
                f.write(chuck)
                
        img = open(out, 'rb')        
        remove_image_subtitle_request = RemoveImageSubtitlesAdvanceRequest()
        remove_image_subtitle_request.image_urlobject = img
        remove_image_subtitle_request.bx = 0
        remove_image_subtitle_request.by = 0
        remove_image_subtitle_request.bw = 1
        remove_image_subtitle_request.by = 1
        
        response = client.remove_image_subtitles_advance(remove_image_subtitle_request, runtime)
        
        # 获取整体结果
        resp = response.body
        image_url = resp.data.image_url
        resp = requests.get(image_url)
        with open(out, "wb") as f:
            for chuck in resp.iter_content():
                f.write(chuck)
            
    except Exception as error:
        # 获取整体报错信息
        print(error)
    
def main():
    args = parse_args()
    process_file(args.img, args.out)

if __name__ == "__main__":
    
    #python .\tools\image_remove_tag.py --img D:\train-material\51_07-nEOzGwt18ggkQFe7.jpg --out D:\train-material\b.png
    main()
