import os
os.environ["HTTP_PROXY"]= "http://127.0.0.1:1080"
os.environ["HTTPS_PROXY"]= "http://127.0.0.1:1080"

# load libraries
from huggingface_hub import hf_hub_download
from ultralytics import YOLO
from supervision import Detections
from PIL import Image
import pprint

# download model
model_path = hf_hub_download(repo_id="arnabdhar/YOLOv8-Face-Detection", filename="model.pt", cache_dir="./models")

# load model
model = YOLO(model_path)

# inference
image_path = "test.png"
img = Image.open(image_path)
output = model(img)
results = Detections.from_ultralytics(output[0])

face_corner = results.xyxy[0].tolist()
print(face_corner)
