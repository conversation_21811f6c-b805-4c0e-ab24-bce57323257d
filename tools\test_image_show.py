import sys
from PIL import Image
import matplotlib.pyplot as plt
import cv2
import traceback
import magic
import io
import numpy as np

def show_image(img_path):
    """
    Reads an image from the given path using PIL and displays it using matplotlib.

    Args:
        img_path: The path to the image file.
    """
    try:
        with open(img_path, 'rb') as f:
            image_data = f.read()
            img = Image.open(io.BytesIO(image_data))
            plt.imshow(img)
            plt.axis('off')
            plt.title(f"Image: {img_path}")
            plt.show()
            
        # img_cv = cv2.imread(img_path)
        # if img_cv is None:
        #     raise FileNotFoundError(f"Error: Image file not found or could not be read at {img_path}")
        # img_rgb = cv2.cvtColor(img_cv, cv2.COLOR_BGR2RGB)
        # plt.imshow(img_rgb)
        # plt.axis('off')
        # plt.title(f"Image: {img_path}")
        # plt.show()

    except FileNotFoundError:
        print(f"Error: Image file not found at {img_path}")
    except Exception as e:
        print(f"An error occurred: {traceback.format_exc()}")

def save_binary_as_png(binary_data, output_path):
    """
    Saves binary data as a PNG image.

    Args:
        binary_data: The binary data to save.
        output_path: The path to save the PNG image.
    """
    try:
        # Create a PIL Image object from the binary data
        img = Image.open(io.BytesIO(binary_data))

        # Save the image as PNG
        img.save(output_path, "PNG")
        print(f"Image saved as PNG to {output_path}")

    except Exception as e:
        print(f"An error occurred while saving as PNG: {traceback.format_exc()}")


def detect_image_type(file_path):
    mime = magic.Magic(mime=True)
    mime_type = mime.from_file(file_path)
    print(mime_type)
    if mime_type.startswith('image'):
        return mime_type.split('/')[1]
    else:
        return "Unknown format"
    
if __name__ == "__main__":
    if len(sys.argv) != 2:
        print("Usage: python test_image_show.py <image_path>")
        sys.exit(1)

    image_path = sys.argv[1]
    show_image(image_path)
    print(detect_image_type(image_path))
    
import numpy as np
    
