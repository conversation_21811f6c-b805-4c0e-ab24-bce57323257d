{"3": {"inputs": {"seed": 274543241437952, "steps": 8, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1, "model": ["64", 0], "positive": ["72", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "5": {"inputs": {"width": 768, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"text": ["51", 0], "speak_and_recognation": true, "clip": ["81", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "(nsfw:1.3),(worst quality:1.5),(low quality:1.5),(normal quality:1.5)bad eye,wrong hand,open mouth,bad anatomy,wrong anatomy,cgi,illustration,cartoon, poorly drawn,watermark,render,painting,drawing,bad quality,grainy,low resolution,(twisted fingers,malformed hands,fusion of hands,a deformed foot, huge hands,extra fingers,missing fingers,fused fingers,extra limb,bad anatomy,independent limb,disconnected limbs,disconnected limbs,amputation, overlapping fingers:0.9),", "speak_and_recognation": true, "clip": ["81", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["81", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "12": {"inputs": {"image": "5fc6ddc0-632b-46bb-8f42-8d95746219cd_1 (1).jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "51": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "watercolor_(medium), ", "text_b": ["118", 1], "text_c": "", "speak_and_recognation": true}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "52": {"inputs": {"strength": 0.8, "conditioning": ["6", 0], "control_net": ["53", 0], "image": ["55", 0]}, "class_type": "ControlNetApply", "_meta": {"title": "Apply ControlNet"}}, "53": {"inputs": {"control_net_name": "controlnet-union-sdxl/diffusion_pytorch_model.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "55": {"inputs": {"preprocessor": "DepthAnythingPreprocessor", "resolution": 768, "image": ["131", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "64": {"inputs": {"weight": 0.8, "weight_type": "strong style transfer", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["69", 0], "ipadapter": ["69", 1], "image": ["76", 0], "clip_vision": ["67", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "67": {"inputs": {"clip_name": "CLIP_ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "69": {"inputs": {"preset": "PLUS (high strength)", "model": ["81", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "72": {"inputs": {"strength": 0.8, "conditioning": ["52", 0], "control_net": ["53", 0], "image": ["74", 0]}, "class_type": "ControlNetApply", "_meta": {"title": "Apply ControlNet"}}, "74": {"inputs": {"preprocessor": "LineArtPreprocessor", "resolution": 768, "image": ["131", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "76": {"inputs": {"image_gen_width": 768, "image_gen_height": 768, "resize_mode": "Crop and Resize", "hint_image": ["101", 0]}, "class_type": "HintImageEnchance", "_meta": {"title": "Enchance And Resize Hint Images"}}, "81": {"inputs": {"ckpt_name": "DreamShaperXL_Lightning-SFW.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "98": {"inputs": {"facedetection": "retinaface_resnet50", "model": "codeformer-v0.1.0.pth", "visibility": 1, "codeformer_weight": 0.5, "image": ["129", 0]}, "class_type": "ReActorRestoreFace", "_meta": {"title": "Restore Face 🌌 ReActor"}}, "101": {"inputs": {"image": "frame_20.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "117": {"inputs": {"filename_prefix": "pink/pink", "images": ["98", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "118": {"inputs": {"task": "more detailed caption", "text_input": "", "max_new_tokens": 1024, "num_beams": 3, "do_sample": false, "fill_mask": false, "FLORENCE2": ["119", 0], "image": ["131", 0]}, "class_type": "Florence2", "_meta": {"title": "Florence2"}}, "119": {"inputs": {"version": "large-ft"}, "class_type": "LoadFlorence2Model", "_meta": {"title": "Load Florence2 Model"}}, "122": {"inputs": {"weight": 1, "weight_type": "linear", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["123", 0], "ipadapter": ["123", 1], "image": ["132", 0], "clip_vision": ["67", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "123": {"inputs": {"preset": "PLUS FACE (portraits)", "model": ["81", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "125": {"inputs": {"enabled": true, "swap_model": "inswapper_128.onnx", "facedetection": "retinaface_resnet50", "face_restore_model": "codeformer-v0.1.0.pth", "face_restore_visibility": 1, "codeformer_weight": 0.5, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "input_image": ["8", 0], "source_image": ["132", 0]}, "class_type": "ReActorFaceSwap", "_meta": {"title": "ReActor 🌌 Fast Face Swap"}}, "127": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "129": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": ***************, "steps": 8, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 0.5, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 10, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 10, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "speak_and_recognation": true, "image": ["125", 0], "model": ["122", 0], "clip": ["81", 1], "vae": ["81", 2], "positive": ["6", 0], "negative": ["7", 0], "bbox_detector": ["127", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "131": {"inputs": {"width": 768, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "132": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["131", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "133": {"inputs": {"anything": ["98", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean GPU Used"}}}