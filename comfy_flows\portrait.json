{"3": {"inputs": {"seed": 389732447359718, "steps": 8, "cfg": 1.8, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1, "model": ["64", 0], "positive": ["72", 0], "negative": ["7", 0], "latent_image": ["86", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "DreamShaperXL_Lightning-SFW.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "6": {"inputs": {"text": ["51", 0], "speak_and_recognation": true, "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "(nsfw:1.5),lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry, embedding:ng_deepnegative_v1_75t, ", "speak_and_recognation": true, "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "portrait/portrait", "images": ["91", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "12": {"inputs": {"image": "1764_17258456127633724.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "51": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "(<PERSON>, <PERSON>:1.2),naked,", "text_b": ["78", 1], "text_c": "", "speak_and_recognation": true}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "52": {"inputs": {"strength": 0.8, "conditioning": ["6", 0], "control_net": ["73", 0], "image": ["55", 0]}, "class_type": "ControlNetApply", "_meta": {"title": "Apply ControlNet"}}, "55": {"inputs": {"preprocessor": "DepthAnythingPreprocessor", "resolution": 1024, "image": ["93", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "64": {"inputs": {"weight": 2, "weight_type": "ease out", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["69", 0], "ipadapter": ["69", 1], "image": ["94", 0], "clip_vision": ["67", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "66": {"inputs": {"image": "portrait.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "67": {"inputs": {"clip_name": "CLIP_ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "69": {"inputs": {"preset": "STANDARD (medium strength)", "model": ["4", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "72": {"inputs": {"strength": 0.8, "conditioning": ["52", 0], "control_net": ["73", 0], "image": ["74", 0]}, "class_type": "ControlNetApply", "_meta": {"title": "Apply ControlNet"}}, "73": {"inputs": {"control_net_name": "controlnet-union-sdxl/diffusion_pytorch_model.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "74": {"inputs": {"preprocessor": "LineartStandardPreprocessor", "resolution": 1024, "image": ["93", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "78": {"inputs": {"task": "more detailed caption", "text_input": "", "max_new_tokens": 1024, "num_beams": 3, "do_sample": false, "fill_mask": false, "FLORENCE2": ["79", 0], "image": ["93", 0]}, "class_type": "Florence2", "_meta": {"title": "Florence2"}}, "79": {"inputs": {"version": "large-ft"}, "class_type": "LoadFlorence2Model", "_meta": {"title": "Load Florence2 Model"}}, "86": {"inputs": {"width": 768, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "91": {"inputs": {"enabled": true, "swap_model": "inswapper_128.onnx", "facedetection": "retinaface_resnet50", "face_restore_model": "codeformer-v0.1.0.pth", "face_restore_visibility": 1, "codeformer_weight": 0.5, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "input_image": ["8", 0], "source_image": ["93", 0]}, "class_type": "ReActorFaceSwap", "_meta": {"title": "ReActor 🌌 Fast Face Swap"}}, "93": {"inputs": {"width": 768, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "94": {"inputs": {"width": 768, "height": 768, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["66", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "97": {"inputs": {"anything": ["91", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean GPU Used"}}}