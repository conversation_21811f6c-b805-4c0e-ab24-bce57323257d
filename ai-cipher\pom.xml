<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.banyunjuhe.ai</groupId>
    <artifactId>ai-cipher</artifactId>
    <version>1.0-SNAPSHOT</version>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <jar.out.dir>lib</jar.out.dir>
        <jar.name>ai-cipher</jar.name>
    </properties>

    <dependencies>
        <dependency>
            <groupId>jupiter.jvm</groupId>
            <artifactId>chipher</artifactId>
            <version>1.3</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/cipher-1.3.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>net.sf.py4j</groupId>
            <artifactId>py4j</artifactId>
            <version>********</version>
            <scope>system</scope>
            <systemPath>${basedir}/lib/py4j********.jar</systemPath>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.14</version>
        </dependency>
    </dependencies>
    <build>
        <plugins>
            <plugin>
                <artifactId>maven-assembly-plugin</artifactId>
                <version>3.3.0</version>
                <executions>
                    <execution>
                        <id>BuildDeployJar</id>
                        <phase>package</phase>
                        <configuration>
                            <appendAssemblyId>false</appendAssemblyId>
                            <tarLongFileMode>gnu</tarLongFileMode>
                            <finalName>${jar.name}</finalName>
                            <outputDirectory>${jar.out.dir}</outputDirectory>
                            <descriptors>
                                <descriptor>jar-with-dependencies.xml</descriptor>
                            </descriptors>
                            <archive>
                                <manifest>
                                    <mainClass>com.banyunjuhe.ai.CipherWrapper</mainClass>
                                </manifest>
                            </archive>
                        </configuration>
                        <goals>
                            <goal>single</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
        </plugins>
    </build>
</project>