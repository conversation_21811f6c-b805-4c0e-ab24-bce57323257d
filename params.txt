ReiszeImage upscale_method lanzcos, bicubic, area, biliner, nearest_exact
HyperLora   strength_model 0.13
HyperLora   strength_clip 0.13

DetailLora   strength_model 0.9
DetailLora   strength_clip 0.9

DepthLora   strength_model 1.0
CannyLora   strength_model 1.0

RefRandomNoise noise_seed -1

RefPuLID weight 1.0
RefPuLID start_at 0.0
RefPuLID end_at 1.0
RefPuLID fusion mead, concat, max, norm_id, max_token, auto_weight, train_weight
RefPuLID fusion_weight_max 1.0
RefPuLID fusion_weight_min 0.0
RefPuLID train_step 1000
RefPuLID use_gray false, true

RefCLIPText guidance 3.5 

RefBasicScheduler scheduler simple, normal, beta
RefBasicScheduler steps 8
RefBasicScheduler denoise 1.0

RefKSamplerSelect sampler_name euler, ddpmpp_sde, ddim 


RefDetailDaemon detail_amount 0.5
RefDetailDaemon start 0.2
RefDetailDaemon end 1.0
RefDetailDaemon bias 1.0
RefDetailDaemon exponent 0.5
RefDetailDaemon start_offset 0.0
RefDetailDaemon end_offset 0.0
RefDetailDaemon fade 0.0
RefDetailDaemon smooth true, false
RefDetailDaemon cfg_override_scale 0.0 

RefReduxAdvanced donwsampling_factor 1.0
RefReduxAdvanced donwsampling_function  nearest, bicubic, area, biliner, nearest_exact
RefReduxAdvanced mode "keep aspect ratio", "center crop (squarue)", "autocrop with mask"
RefReduxAdvanced weight 1.0
RefReduxAdvanced autocrop_margin 0.20

PaintReduxAdvanced donwsampling_factor 1.0
PaintReduxAdvanced donwsampling_function  nearest, bicubic, area, biliner, nearest_exact
PaintReduxAdvanced mode "keep aspect ratio", "center crop (squarue)", "autocrop with mask"
PaintReduxAdvanced weight 1.0
PaintReduxAdvanced autocrop_margin 0.20

PaintCLIPText guidance 1.0

RefModelSamplingFlux max_shift 1.15
RefModelSamplingFlux base_shift 0.5

PaintModelSamplingFlux max_shift 1.15
PaintModelSamplingFlux base_shift 0.5

PaintPuLID weight 1.0
PaintPuLID start_at 0.0
PaintPuLID end_at 1.0
PaintPuLID fusion mead, concat, max, norm_id, max_token, auto_weight, train_weight
PaintPuLID fusion_weight_max 1.0
PaintPuLID fusion_weight_min 0.0
PaintPuLID train_step 1000
PaintPuLID use_gray false, true

PaintRandomNoise noise_seed -1
PaintBasicScheduler scheduler simple, normal, beta
PaintBasicScheduler steps 8
PaintBasicScheduler denoise 1.0

PaintKSamplerSelect sampler_name euler, ddpmpp_sde, ddim 

PaintDetailDaemon detail_amount 0.5
PaintDetailDaemon start 0.2
PaintDetailDaemon end 1.0
PaintDetailDaemon bias 1.0
PaintDetailDaemon exponent 0.5
PaintDetailDaemon start_offset 0.0
PaintDetailDaemon end_offset 0.0
PaintDetailDaemon fade 0.0
PaintDetailDaemon smooth true, false
PaintDetailDaemon cfg_override_scale 0.0 
