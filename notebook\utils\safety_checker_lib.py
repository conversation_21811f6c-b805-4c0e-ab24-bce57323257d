#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from transformers import AutoFeatureExtractor
from diffusers.pipelines.stable_diffusion.safety_checker import StableDiffusionSafetyChecker
from PIL import Image, ImageOps, PngImagePlugin, ImageDraw, ImageFont
import numpy as np
import torch
from ..config import settings
import base64
import io
import os 
from random import choice, random

safety_model_id = "CompVis/stable-diffusion-safety-checker"
safety_cache_dir="./models/safety-checker"

safety_feature_extractor = AutoFeatureExtractor.from_pretrained(safety_model_id, resume_download=True, cache_dir=safety_cache_dir, proxies=settings.google_translate_proxy, torch_dtype=torch.float16)
safety_checker = StableDiffusionSafetyChecker.from_pretrained(safety_model_id,  resume_download=True, cache_dir=safety_cache_dir, proxies=settings.google_translate_proxy, torch_dtype=torch.float16).to("cuda")

def check_safety_comfy(x_image, model_style):

    safety_checker_input = safety_feature_extractor(numpy_to_pil(x_image), return_tensors="pt").to("cuda")
    x_checked_image, has_nsfw_concept = safety_checker(images=x_image, clip_input=safety_checker_input.pixel_values)
    assert x_checked_image.shape[0] == len(has_nsfw_concept)
   
    for i in range(len(has_nsfw_concept)):
        if has_nsfw_concept[i]:
            x_checked_image[i] = load_replacement_comfy(x_checked_image[i], model_style)
   
    return x_checked_image

def load_replacement_comfy(x, model_style):
    try:
        if(random() < 0.5):
            hwc = x.shape
            y = Image.open(os.path.join(settings.comfy_dir, choice(settings.comfy_replace_img))).convert("RGB").resize((hwc[1], hwc[0]))
            y = (np.array(y)/255.0).astype(x.dtype)
            assert y.shape == x.shape
            return y    
        else:
            hwc = x.shape
            y = Image.open(os.path.join(settings.comfy_dir, f"{model_style}.png")).convert("RGB").resize((hwc[1], hwc[0]))
            y = (np.array(y)/255.0).astype(x.dtype)
            assert y.shape == x.shape
            return y
    except Exception as e:
        return x
    
def check_safety(x_image):

    safety_checker_input = safety_feature_extractor(numpy_to_pil(x_image), return_tensors="pt").to("cuda")
    x_checked_image, has_nsfw_concept = safety_checker(images=x_image, clip_input=safety_checker_input.pixel_values)
    assert x_checked_image.shape[0] == len(has_nsfw_concept)
    nsfw = False
    for i in range(len(has_nsfw_concept)):
        if has_nsfw_concept[i]:
            x_checked_image[i] = load_replacement(x_checked_image[i])
            nsfw = True
    return x_checked_image, has_nsfw_concept, nsfw

def numpy_to_pil(images):
    """
    Convert a numpy image or a batch of images to a PIL image.
    """
    if images.ndim == 3:
        images = images[None, ...]
    images = (images * 255).round().astype("uint8")
    pil_images = [Image.fromarray(image) for image in images]

    return pil_images

def pil_to_numpy(images):
    """
    Convert a pil image or a batch of images to a numpy image.
    """

    if type(images) == list:
        images = [np.array(img)/255.0 for img in images] 
    else:
        images = [np.array(images)/255.0]
    return np.array(images)

def load_replacement(x):
    try:
        hwc = x.shape
        y = Image.open("./static/nsfw/nsfw.png").convert("RGB").resize((hwc[1], hwc[0]))
        y = (np.array(y)/255.0).astype(x.dtype)
        assert y.shape == x.shape
        return y
    except Exception as e:
        return x

def add_visible_tags(base: Image):

    out = base.convert(mode="RGBA")    
    (width, height) = base.size 
    font = ImageFont.truetype("static/fonts/SIMLI.TTF", 20)
    draw = ImageDraw.Draw(out)
    position = (5, height - 25)
    text = "般芸聚合"

    left, top, right, bottom  = draw.textbbox(position, text, font=font)
    draw.rectangle((left -5, top -5, right + 5, bottom + 5), fill=(255,255,255,200))
    draw.text(position, text, font=font, fill=(255,255,255,100))

    return out

def encode_pil_to_base64(image, ext:str):

    with io.BytesIO() as output_bytes:
        if ext.lower() == 'png':
            image.save(output_bytes, format="PNG")
        elif ext.lower() in ("jpg", "jpeg", "webp"):
            image.save(output_bytes, format="JPEG")
        bytes_data = output_bytes.getvalue()
    return base64.b64encode(bytes_data)
