#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import shutil
import os
import csv
import argparse
from PIL import Image, PngImagePlugin
import base64
import io 
import re 
import time 
import piexif
import piexif.helper
import traceback

def add_padding(encoded_string):
    missing_padding = len(encoded_string) % 4
    if missing_padding != 0:
        encoded_string += '=' * (4 - missing_padding)
    return encoded_string

def encode_pil_to_base64(image, ext:str):

    with io.BytesIO() as output_bytes:

        if ext.lower() == 'png':
            use_metadata = False
            metadata = PngImagePlugin.PngInfo()
            for key, value in image.info.items():
                if isinstance(key, str) and isinstance(value, str):
                    metadata.add_text(key, value)
                    use_metadata = True
            image.save(output_bytes, format="PNG", pnginfo=(metadata if use_metadata else None), quality=80)

        elif ext.lower() in ("jpg", "jpeg", "webp"):
            parameters = image.info.get('parameters', None)
            exif_bytes = piexif.dump({
                "Exif": { piexif.ExifIFD.UserComment: piexif.helper.UserComment.dump(parameters or "", encoding="unicode") }
            })
            
            image.save(output_bytes, format="JPEG", exif = exif_bytes, quality=80)
        else:
            raise Exception("can not determinate the iamge file format")

        bytes_data = output_bytes.getvalue()

    return base64.b64encode(bytes_data)


def get_resolution(width, height, dest_width, dest_height):
    
    width = int(width)
    height = int(height)
    if width < height:
        dw = dest_width
        dh = int(height * (dw / width))
    else:
        dh = dest_height
        dw = int(width * (dh / height))
 
    return dw, dh

def resize_img(img:Image, img_x:int = 128, img_y:int = 128):
    
    (width, height) = img.size
    rwidth, rheight = get_resolution(width, height, img_x, img_y)
    img = img.resize((rwidth, rheight))
    (width, height) = img.size
    tmp = Image.new('RGB', (img_x, img_y))
    tmp.paste(img, (0, 0))
    return tmp
    
def combine_image(image1:Image, image2:Image, image3:Image):
    
    image1 = resize_img(image1)
    image2 = resize_img(image2)
    
    combined_image = Image.new('RGB', (image1.width + image2.width, image1.height))
    combined_image.paste(image1, (0, 0))
    combined_image.paste(image2, (image1.width, 0))
    
    if image3:
        concat_image =  Image.new('RGB', (image3.width, combined_image.height + image3.height))
        concat_image.paste(image3, (0, 0))
        concat_image.paste(combined_image, (0, image3.height))
        return encode_pil_to_base64(concat_image, 'png')
    else:
        return encode_pil_to_base64(combined_image, 'png')
    
def parse_args():
    desc = "batch memo"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--data_date', type=str, default="20240708", help='[yyyymmdd]')
    return parser.parse_args()

def process_file(log_file:str, out:dict):

    for line in open(log_file, "r", encoding="utf-8"):
        row = line.strip().split(",")
        try:
            ts = row[0]
            host = row[1]
            code = row[2]
            tmplId = row[3]
            appVer = row[4]
            page = row[5]
            imgsrc = row[6]
            imgdata = row[7]
            in_img = Image.open(io.BytesIO(base64.b64decode(add_padding(imgsrc))))
            out_img = Image.open(io.BytesIO(base64.b64decode(add_padding(imgdata))))
            k = f"{tmplId},{code},{appVer},{page}"
            ori_img = Image.open(io.BytesIO(base64.b64decode(out[k]))) if k in out else None 
        
            comb_img = combine_image(in_img, out_img, ori_img)
            out[k] = comb_img
        except Exception as e:
            expr = traceback.format_exc()
            print(expr)
            print(row[0:6])

    return out 
                    
def process_directory(data_date:str):
    out = {}
    for dirpath, dirname, filenames in os.walk(os.path.join("./logs", data_date)):
        for filename in filenames:
            if filename.startswith("img-data-"):
                infile = os.path.join(dirpath, filename)
                out.update(process_file(infile, out))

    outf = open(f"./logs/{data_date}/merged_log.txt", "w")
    for k, v in out.items():
        outf.write(f"{data_date},{k},{v}\n")
        
def main():
    args = parse_args()
    process_directory(args.data_date)
        
if __name__ == "__main__":
    main()