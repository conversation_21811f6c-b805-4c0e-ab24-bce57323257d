#!/bin/bash
#############################################################################################################################
usage="Usage: $0 [data_date(yyyymmdd)]"

# Check data_time parameter
if [[ $# == 0 ]];then
    data_date=$(date -d '-30 days' +%Y%m%d)
elif [[ $1 == [2-3][0-9][0-9][0-9][0-1][0-9][0-3][0-9] ]]
then
    data_date=$1
else
    echo "$usage"
    echo "Default data_time is last hour."
    exit 1
fi

base_dir=$(cd $(dirname $0) && pwd)
cd $base_dir

year=${data_date:0:4}
month=${data_date:4:2}
day=${data_date:6:2}

echo $data_date
echo "$base_dir/logs/$year$month$day"
echo "$base_dir/dataset/$year/$month/$day"
rm -r -f $base_dir/logs/$year$month$day
rm -r -f $base_dir/dataset/$year/$month/$day

