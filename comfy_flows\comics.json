{"3": {"inputs": {"seed": 990603566352682, "steps": 8, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1, "model": ["70", 0], "positive": ["24", 0], "negative": ["16", 0], "latent_image": ["49", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "DreamShaperXL_Lightning-SFW.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "comics/comics", "images": ["28", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "12": {"inputs": {"image": "2154_17268059409579615.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "14": {"inputs": {"text": ["18", 0], "speak_and_recognation": true, "clip": ["65", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "16": {"inputs": {"text": "(nsfw:1.3),lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry,embedding:ng_deepnegative_v1_75t, ", "speak_and_recognation": true, "clip": ["65", 0]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "18": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": ["79", 0], "text_b": ["50", 1], "text_c": "", "speak_and_recognation": true, "result": "ethereal fantasy concept art of . magnificent, celestial, ethereal, painterly, epic, majestic, magical, fantasy art, cover art, dreamy, A woman with long dark hair is smiling at the camera. She is wearing a white and blue striped shirt. She has a gold necklace around her neck. There is a white wall behind the woman."}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "21": {"inputs": {"control_net_name": "controlnet-union-sdxl/diffusion_pytorch_model.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "22": {"inputs": {"preprocessor": "AnyLineArtPreprocessor_aux", "resolution": 1024, "image": ["60", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "24": {"inputs": {"strength": 0.6, "conditioning": ["14", 0], "control_net": ["21", 0], "image": ["22", 0]}, "class_type": "ControlNetApply", "_meta": {"title": "Apply ControlNet"}}, "28": {"inputs": {"facedetection": "retinaface_resnet50", "model": "codeformer-v0.1.0.pth", "visibility": 0.5, "codeformer_weight": 0.5, "image": ["8", 0]}, "class_type": "ReActorRestoreFace", "_meta": {"title": "Restore Face 🌌 ReActor"}}, "49": {"inputs": {"width": 768, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "50": {"inputs": {"task": "more detailed caption", "text_input": "", "max_new_tokens": 1024, "num_beams": 3, "do_sample": false, "fill_mask": true, "FLORENCE2": ["51", 0], "image": ["60", 0]}, "class_type": "Florence2", "_meta": {"title": "Florence2"}}, "51": {"inputs": {"version": "large-ft"}, "class_type": "LoadFlorence2Model", "_meta": {"title": "Load Florence2 Model"}}, "57": {"inputs": {"lora_name": "sdxl/EldritchComicsXL1.2.safetensors", "strength_model": 0.5, "strength_clip": 0.5, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "60": {"inputs": {"width": 768, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "61": {"inputs": {"anything": ["28", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean GPU Used"}}, "65": {"inputs": {"stop_at_clip_layer": -1, "clip": ["57", 1]}, "class_type": "CLIPSetLastLayer", "_meta": {"title": "CLIP Set Last Layer"}}, "68": {"inputs": {"detail_method": "PyMatting", "detail_erode": 6, "detail_dilate": 6, "black_point": 0.01, "white_point": 0.99, "process_detail": true, "image": ["60", 0]}, "class_type": "LayerMask: RmBgUltra V2", "_meta": {"title": "LayerMask: RmBgUltra V2"}}, "69": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["75", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "70": {"inputs": {"weight": 0.5, "start_at": 0, "end_at": 1, "weight_type": "standard", "model": ["73", 0], "ipadapter": ["73", 1], "image": ["69", 0]}, "class_type": "IPAdapter", "_meta": {"title": "IPAdapter"}}, "73": {"inputs": {"preset": "STANDARD (medium strength)", "model": ["57", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "75": {"inputs": {"fill_background": false, "background_color": "#000000", "RGBA_image": ["68", 0]}, "class_type": "LayerUtility: ImageRemoveAlpha", "_meta": {"title": "LayerUtility: ImageRemoveAlpha"}}, "79": {"inputs": {"styles": "fooocus_styles", "select_styles": "sai-comic book", "speak_and_recognation": true}, "class_type": "easy stylesSelector", "_meta": {"title": "Styles Selector"}}}