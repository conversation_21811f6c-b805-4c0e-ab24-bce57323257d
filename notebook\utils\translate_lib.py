#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import requests
import argparse

from ..config import settings

def contains_zhCN(text:str):
    for char in text:
        if char >= u'\u4e00' or char <= u'\u9fa5':
            return True
    return False

def translate_post(target:str, text:str):

    g_url = "https://translation.googleapis.com/language/translate/v2"
    data = {
            "q":text, 
            "target":target,
            "format":"text",
            "source":"zh-CN",
            "model":"nmt",
            "key":settings.google_translate_credentials
            }
    resp = requests.post(g_url, data=data, proxies=settings.google_translate_proxy)
    content = resp.json()
    return " ".join([t["translatedText"] for t in content["data"]["translations"]])

def parse_args():
    desc = "encode image to base64 string format"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--query', type=str, default='查询', help='[翻译的内容]')
    
    return parser.parse_args()

def main():
    args = parse_args()
    rs = translate_post("en", args.query)
    print(rs)

if __name__ == "__main__":
    main()
