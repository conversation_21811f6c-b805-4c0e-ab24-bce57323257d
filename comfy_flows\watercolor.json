{"3": {"inputs": {"seed": 9807587764834, "steps": 8, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1, "model": ["73", 0], "positive": ["64", 0], "negative": ["7", 0], "latent_image": ["84", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "DreamShaperXL_Lightning-SFW.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "6": {"inputs": {"text": ["51", 0], "speak_and_recognation": true, "clip": ["50", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "(nsfw:1.3),embedding:ng_deepnegative_v1_75t, ", "speak_and_recognation": true, "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "watercolor/watercolor", "images": ["63", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "12": {"inputs": {"image": "87a5bfc7-16f6-4898-9253-f6906e5ae459_1.jpg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "50": {"inputs": {"lora_name": "sdxl/ral-wtrclr-sdxl.safetensors", "strength_model": 0.8, "strength_clip": 0.8, "model": ["4", 0], "clip": ["4", 1]}, "class_type": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_meta": {"title": "Load LoRA"}}, "51": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": "masterpiece,best quality, <lora:ral-wtrclr:0.8>", "text_b": ["80", 1], "text_c": "", "speak_and_recognation": true}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "52": {"inputs": {"strength": 1, "conditioning": ["6", 0], "control_net": ["53", 0], "image": ["55", 0]}, "class_type": "ControlNetApply", "_meta": {"title": "Apply ControlNet"}}, "53": {"inputs": {"control_net_name": "controlnet-union-sdxl/diffusion_pytorch_model.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "55": {"inputs": {"preprocessor": "DepthAnythingPreprocessor", "resolution": 1024, "image": ["87", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "63": {"inputs": {"facedetection": "retinaface_resnet50", "model": "codeformer-v0.1.0.pth", "visibility": 1, "codeformer_weight": 1, "image": ["8", 0]}, "class_type": "ReActorRestoreFace", "_meta": {"title": "Restore Face 🌌 ReActor"}}, "64": {"inputs": {"strength": 0.8, "conditioning": ["52", 0], "control_net": ["53", 0], "image": ["66", 0]}, "class_type": "ControlNetApply", "_meta": {"title": "Apply ControlNet"}}, "66": {"inputs": {"preprocessor": "LineartStandardPreprocessor", "resolution": 1024, "image": ["87", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "73": {"inputs": {"weight": 1, "weight_type": "ease out", "combine_embeds": "concat", "start_at": 0, "end_at": 1, "embeds_scaling": "V only", "model": ["75", 0], "ipadapter": ["75", 1], "image": ["86", 0], "clip_vision": ["76", 0]}, "class_type": "IPAdapterAdvanced", "_meta": {"title": "IPAdapter Advanced"}}, "75": {"inputs": {"preset": "PLUS FACE (portraits)", "model": ["50", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "76": {"inputs": {"clip_name": "CLIP_ViT-H-14-laion2B-s32B-b79K.safetensors"}, "class_type": "CLIPVisionLoader", "_meta": {"title": "Load CLIP Vision"}}, "80": {"inputs": {"task": "more detailed caption", "text_input": "", "max_new_tokens": 1024, "num_beams": 3, "do_sample": false, "fill_mask": false, "FLORENCE2": ["81", 0], "image": ["87", 0]}, "class_type": "Florence2", "_meta": {"title": "Florence2"}}, "81": {"inputs": {"version": "large-ft"}, "class_type": "LoadFlorence2Model", "_meta": {"title": "Load Florence2 Model"}}, "84": {"inputs": {"width": 768, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "86": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["87", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "87": {"inputs": {"width": 768, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "88": {"inputs": {"anything": ["63", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean GPU Used"}}}