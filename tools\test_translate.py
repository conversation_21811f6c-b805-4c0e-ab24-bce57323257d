#!/usr/bin/env python 
# -*- coding:utf-8 -*-

import requests
import argparse
import os

os.environ["HTTP_PROXY"]= "http://127.0.0.1:1080"
os.environ["HTTPS_PROXY"]= "http://127.0.0.1:1080"

def translate_post(target:str, text:str):
    credentials = "AIzaSyCo2Jly7begyK0X4UDBmIOsxW5mpEGAejI"
    g_url = "https://translation.googleapis.com/language/translate/v2"
    data = {
            "q":text, 
            "target":target,
            "format":"text",
            "source":"zh-CN",
            "model":"nmt",
            "key":credentials
            }
    resp = requests.post(g_url, data=data)
    content = resp.json()
    return " ".join([t["translatedText"] for t in content["data"]["translations"]])

def parse_args():
    desc = "encode image to base64 string format"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--query', type=str, default='查询', help='[翻译的内容]')
    
    return parser.parse_args()

def main():
    args = parse_args()
    rs = translate_post("pt-br", args.query)
    print(rs)

if __name__ == "__main__":
    #python .\tools\test_translate.py --query "拖掉衣服"
    #D:\banyunjuhe\职场升职记中文字幕
    main()
