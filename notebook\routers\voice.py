#!/usr/bin/env python 
# -*- coding:utf-8 -*-
import uuid
import json
import nls

from aliyunsdkcore.client import AcsClient
from aliyunsdkcore.request import CommonRequest
from ..config import settings
import os
import oss2
import datetime
from fastapi import APIRouter, Body
from ..utils import RespCode
from ..utils import DecryptRoute
from ..schemas.voice import NameVoice

router = APIRouter(prefix='/voice', tags=['语音接口'])
router.route_class = DecryptRoute

def get_token():
    # 创建AcsClient实例
    client = AcsClient(
        settings.OSS_SECRETKEY,
        settings.OSS_SECRETPASS,
        settings.VOICE_REGION
    );

    # 创建request，并设置参数。
    request = CommonRequest()
    request.set_method('POST')
    request.set_domain(settings.VOICE_DOMAIN)
    request.set_version('2019-02-28')
    request.set_action_name('CreateToken')

    try:
        response = client.do_action_with_exception(request)
        print(response)
        jss = json.loads(response)
        if 'Token' in jss and 'Id' in jss['Token']:
            token = jss['Token']['Id']
            expireTime = jss['Token']['ExpireTime']
            print("token = " + token)
            print("expireTime = " + str(expireTime))
            return token
    except Exception as e:
       print(e)
    return None

def tts_calback(text, voice="zhibei_emo"):

    URL = settings.VOICE_URL
    TOKEN = get_token()
    APPKEY = settings.VOICE_APPKEY
    
    fprefix = str(uuid.uuid4())
    temp_file = os.path.join(settings.media_dir, fprefix + ".wav")
    
    out = open(temp_file, "wb")

    def tts_on_data(data):
        try:
            out.write(data)
        except Exception as e:
            print("write data failed:", e)

    def tts_on_close():
        out.close()

    tts = nls.NlsSpeechSynthesizer(url=URL,
                                   token=TOKEN,
                                   appkey=APPKEY,
                                   on_data=tts_on_data,
                                   on_error=tts_on_close,
                                   on_close=tts_on_close
                                   )

    tts.start(text, voice=voice, aformat="wav", wait_complete=True)

    now = datetime.datetime.now()
    year_month_day = now.strftime("%Y/%m/%d")

    oss_path = "/".join([settings.OSS_VOICE_PREFIX, year_month_day, f"{fprefix}.wav"])
    bucket = oss2.Bucket(oss2.Auth(settings.OSS_SECRETKEY, settings.OSS_SECRETPASS), settings.OSS_ENDPOINT, settings.OSS_BUCKET)
    bucket.put_object_from_file(oss_path, temp_file)
        
    os.remove(temp_file)

    return f"https://{settings.OSS_BUCKET}.{settings.OSS_ENDPOINT}/{settings.OSS_VOICE_PREFIX}/{year_month_day}/{fprefix}.wav"

@router.post("/name", summary="生成用户名语音")
def name_vocie(form_data: NameVoice = Body(...)):
    username = form_data.username
    location = tts_calback(username)
    return RespCode.resp_ok({"location":location})




