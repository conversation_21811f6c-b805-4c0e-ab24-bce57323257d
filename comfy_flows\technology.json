{"3": {"inputs": {"seed": 603893873171671, "steps": 8, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 1, "model": ["120", 0], "positive": ["82", 0], "negative": ["7", 0], "latent_image": ["5", 0]}, "class_type": "K<PERSON><PERSON><PERSON>", "_meta": {"title": "K<PERSON><PERSON><PERSON>"}}, "4": {"inputs": {"ckpt_name": "DreamShaperXL_Lightning-SFW.safetensors"}, "class_type": "CheckpointLoaderSimple", "_meta": {"title": "Load Checkpoint"}}, "5": {"inputs": {"width": 768, "height": 1024, "batch_size": 1}, "class_type": "EmptyLatentImage", "_meta": {"title": "Empty Latent Image"}}, "6": {"inputs": {"text": ["51", 0], "speak_and_recognation": true, "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "7": {"inputs": {"text": "lowres, bad anatomy, bad hands, text, error, missing fingers, extra digit, fewer digits, cropped, worst quality, low quality, normal quality, jpeg artifacts, signature, watermark, username, blurry", "speak_and_recognation": true, "clip": ["4", 1]}, "class_type": "CLIPTextEncode", "_meta": {"title": "CLIP Text Encode (Prompt)"}}, "8": {"inputs": {"samples": ["3", 0], "vae": ["4", 2]}, "class_type": "VAEDecode", "_meta": {"title": "VAE Decode"}}, "9": {"inputs": {"filename_prefix": "technology/tecnnology", "images": ["98", 0]}, "class_type": "SaveImage", "_meta": {"title": "Save Image"}}, "12": {"inputs": {"image": "1994_17261967133386538.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "51": {"inputs": {"action": "append", "tidy_tags": "yes", "text_a": " cyberpunk, ", "text_b": ["103", 1], "text_c": "", "speak_and_recognation": true, "result": "cyberpunk, A woman is sitting on a bed. She is wearing a gray bra and gray underwear. There is a blanket on the bed in front of her. The wall behind the woman is white."}, "class_type": "StringFunction|pysssss", "_meta": {"title": "String Function 🐍"}}, "53": {"inputs": {"control_net_name": "controlnet-union-sdxl/diffusion_pytorch_model.safetensors"}, "class_type": "ControlNetLoader", "_meta": {"title": "Load ControlNet Model"}}, "65": {"inputs": {"image": "2024-05-09_19-45-44_3227.jpeg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "66": {"inputs": {"image": "technology.png", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "67": {"inputs": {"image": "CyberPunkAI (1).jpeg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "68": {"inputs": {"image": "aesthetic_lauren_southern_02913_.jpeg", "upload": "image"}, "class_type": "LoadImage", "_meta": {"title": "Load Image"}}, "70": {"inputs": {"image1": ["74", 0], "image2": ["75", 0], "image3": ["76", 0], "image4": ["77", 0]}, "class_type": "ImpactMakeImageBatch", "_meta": {"title": "Make Image Batch"}}, "74": {"inputs": {"image_gen_width": 768, "image_gen_height": 768, "resize_mode": "Crop and Resize", "hint_image": ["67", 0]}, "class_type": "HintImageEnchance", "_meta": {"title": "Enchance And Resize Hint Images"}}, "75": {"inputs": {"image_gen_width": 768, "image_gen_height": 768, "resize_mode": "Crop and Resize", "hint_image": ["66", 0]}, "class_type": "HintImageEnchance", "_meta": {"title": "Enchance And Resize Hint Images"}}, "76": {"inputs": {"image_gen_width": 768, "image_gen_height": 768, "resize_mode": "Crop and Resize", "hint_image": ["65", 0]}, "class_type": "HintImageEnchance", "_meta": {"title": "Enchance And Resize Hint Images"}}, "77": {"inputs": {"image_gen_width": 768, "image_gen_height": 768, "resize_mode": "Crop and Resize", "hint_image": ["68", 0]}, "class_type": "HintImageEnchance", "_meta": {"title": "Enchance And Resize Hint Images"}}, "79": {"inputs": {"preset": "STANDARD (medium strength)", "model": ["4", 0]}, "class_type": "IPAdapterUnifiedLoader", "_meta": {"title": "IPAdapter Unified Loader"}}, "82": {"inputs": {"strength": 0.8, "conditioning": ["6", 0], "control_net": ["53", 0], "image": ["84", 0]}, "class_type": "ControlNetApply", "_meta": {"title": "Apply ControlNet"}}, "84": {"inputs": {"preprocessor": "AnyLineArtPreprocessor_aux", "resolution": 1024, "image": ["116", 0]}, "class_type": "AIO_Preprocessor", "_meta": {"title": "AIO Aux Preprocessor"}}, "85": {"inputs": {"enabled": true, "swap_model": "inswapper_128.onnx", "facedetection": "retinaface_resnet50", "face_restore_model": "codeformer-v0.1.0.pth", "face_restore_visibility": 1, "codeformer_weight": 0.5, "detect_gender_input": "no", "detect_gender_source": "no", "input_faces_index": "0", "source_faces_index": "0", "console_log_level": 1, "input_image": ["8", 0], "source_image": ["115", 0]}, "class_type": "ReActorFaceSwap", "_meta": {"title": "ReActor 🌌 Fast Face Swap"}}, "98": {"inputs": {"facedetection": "retinaface_resnet50", "model": "codeformer-v0.1.0.pth", "visibility": 0.5, "codeformer_weight": 0.5, "image": ["109", 0]}, "class_type": "ReActorRestoreFace", "_meta": {"title": "Restore Face 🌌 ReActor"}}, "103": {"inputs": {"task": "more detailed caption", "text_input": "", "max_new_tokens": 1024, "num_beams": 3, "do_sample": false, "fill_mask": false, "FLORENCE2": ["104", 0], "image": ["116", 0]}, "class_type": "Florence2", "_meta": {"title": "Florence2"}}, "104": {"inputs": {"version": "large-ft"}, "class_type": "LoadFlorence2Model", "_meta": {"title": "Load Florence2 Model"}}, "109": {"inputs": {"guide_size": 512, "guide_size_for": true, "max_size": 1024, "seed": 223977721794542, "steps": 8, "cfg": 2, "sampler_name": "dpmpp_2m", "scheduler": "karras", "denoise": 0.5, "feather": 5, "noise_mask": true, "force_inpaint": true, "bbox_threshold": 0.5, "bbox_dilation": 10, "bbox_crop_factor": 3, "sam_detection_hint": "center-1", "sam_dilation": 0, "sam_threshold": 0.93, "sam_bbox_expansion": 0, "sam_mask_hint_threshold": 0.7, "sam_mask_hint_use_negative": "False", "drop_size": 10, "wildcard": "", "cycle": 1, "inpaint_model": false, "noise_mask_feather": 20, "speak_and_recognation": true, "image": ["85", 0], "model": ["4", 0], "clip": ["4", 1], "vae": ["4", 2], "positive": ["6", 0], "negative": ["7", 0], "bbox_detector": ["111", 0]}, "class_type": "FaceDetailer", "_meta": {"title": "FaceDetailer"}}, "111": {"inputs": {"model_name": "bbox/face_yolov8m.pt"}, "class_type": "UltralyticsDetectorProvider", "_meta": {"title": "UltralyticsDetectorProvider"}}, "115": {"inputs": {"interpolation": "LANCZOS", "crop_position": "top", "sharpening": 0, "image": ["116", 0]}, "class_type": "PrepImageForClipVision", "_meta": {"title": "Prep Image For ClipVision"}}, "116": {"inputs": {"width": 768, "height": 1024, "upscale_method": "nearest-exact", "keep_proportion": true, "divisible_by": 2, "crop": "center", "image": ["12", 0]}, "class_type": "ImageResizeKJ", "_meta": {"title": "Resize Image"}}, "117": {"inputs": {"anything": ["98", 0]}, "class_type": "easy cleanGpuUsed", "_meta": {"title": "Clean GPU Used"}}, "120": {"inputs": {"weight": 0.8, "start_at": 0, "end_at": 1, "weight_type": "style transfer", "model": ["79", 0], "ipadapter": ["79", 1], "image": ["70", 0]}, "class_type": "IPAdapter", "_meta": {"title": "IPAdapter"}}}