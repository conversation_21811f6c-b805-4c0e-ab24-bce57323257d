import shutil
import os
import csv
import argparse
from PIL import Image, PngImagePlugin
import base64
import io 
import re 
import time 
import piexif
import piexif.helper
import traceback
from datetime import datetime
import numpy as np
import pandas as pd
    
def parse_args():
    desc = "batch memo"
    parser = argparse.ArgumentParser(description=desc)
    parser.add_argument('--data_date', type=str, default="20240708", help='[yyyymmdd]')
    return parser.parse_args()

def process_file(log_file:str):

    out = []
    for line in open(log_file, "r"):
        row = line.strip().split(",")
        try:
            ts = row[0]
            host = row[1]
            uid = row[2]
            cid = row[3]
            app = row[4]
            ver = row[5]
            req_path = row[6]
            res_time = row[7]
            if len(row) == 9:
                res_code = row[8]
            else:
                res_code = "200"

            dto = datetime.fromtimestamp(int(ts))
            dts = dto.strftime('%Y-%m-%d %H')
            dt, h = dts.split(" ")
            if res_code == "200":
                out.append([dt, h, req_path, 1, 1, float(res_time)])
            else:
                out.append([dt, h, req_path, 1, 0, float(res_time)])

        except Exception as e:
            expr = traceback.format_exc()
            print(expr)
            print(row[0:6])

    return out 
                    
def process_directory(data_date:str):
    out = []
    for dirpath, dirname, filenames in os.walk(os.path.join("./logs", data_date)):
        for filename in filenames:
            if filename.startswith("req-data-"):
                infile = os.path.join(dirpath, filename)
                out += process_file(infile)

    df = pd.DataFrame(out, columns=["dt", "hour", "path", "req", "ret", "res_tm"])

    grouped = df.groupby(["dt", "hour", "path"])
    req_sum = grouped["req"].sum()
    ret_sum = grouped["ret"].sum()
    res_tm_mean = grouped["res_tm"].mean()
    res_tm_p95 = grouped["res_tm"].quantile(0.95)
    result_df = pd.concat([req_sum, ret_sum, res_tm_mean, res_tm_p95], axis=1)
    result_df.columns = ["req_sum", "ret_sum", "res_tm_mean", "res_tm_p95"]
    
    result_df.to_csv(f"./logs/{data_date}/req_log.txt", header=None)

def main():
    args = parse_args()
    process_directory(args.data_date)
        
if __name__ == "__main__":
    main()
