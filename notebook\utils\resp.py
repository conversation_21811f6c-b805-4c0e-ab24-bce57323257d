from typing import Optional, Any

class RespCode(object):
    OK = 2000
    COMMON_ERROR = 2001
    INVALID_TOKEN = 2002
    PROMPT_TOO_LONG = 2003
    PROMPT_IS_EMTPY = 2004
    OSS_ADDRESS_EMPTY = 2005
    IMAGE_ENTITY_EMPTY = 2006
    PASSWORD_MUST_SAME = 2007
    USERNAME_IS_EMPTY = 2008
    IMAGE_FACE_EMPTY = 2009

    @staticmethod
    def resp_ok(data: Optional[Any] = None):
        return {'code': RespCode.OK, 'msg': "ok", "data": data}
    
    @staticmethod
    def resp_err(code:int, data: Optional[Any] = None):
        return {'code': code, 'msg': "error", "data": data}
