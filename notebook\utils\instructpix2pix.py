#!/usr/bin/env python 
# -*- coding:utf-8 -*-

from typing import Annotated, Binary<PERSON>
from PIL import Image, ImageOps, PngImagePlugin
import requests
import torch
from diffusers import StableDiffusionInstructPix2PixPipeline, EulerAncestralDiscreteScheduler
import matplotlib.pyplot as plt
import numpy as np 
import io
import base64
import piexif
import os
from ..config import settings
from starlette.exceptions import HTTPException

from diffusers.pipelines.stable_diffusion.safety_checker import StableDiffusionSafetyChecker
safety_model_id = "CompVis/stable-diffusion-safety-checker"
safety_cache_dir="./models/safety-checker"

safety_checker = StableDiffusionSafetyChecker.from_pretrained(safety_model_id,  resume_download=True, 
cache_dir=safety_cache_dir, proxies=settings.google_translate_proxy, torch_dtype=torch.float16).to("cuda")

model_id = "timbrooks/instruct-pix2pix"
cache_dir="./models/instruct-pix2pix"

pipe = StableDiffusionInstructPix2PixPipeline.from_pretrained(model_id, resume_download=True, 
cache_dir=cache_dir, torch_dtype=torch.float16, safety_checker=safety_checker, proxies=settings.google_translate_proxy).to("cuda")

pipe.scheduler = EulerAncestralDiscreteScheduler.from_config(pipe.scheduler.config)

def encode_pil_to_base64(image, ext:str):

    with io.BytesIO() as output_bytes:

        if ext.lower() == 'png':
            use_metadata = False
            metadata = PngImagePlugin.PngInfo()
            for key, value in image.info.items():
                if isinstance(key, str) and isinstance(value, str):
                    metadata.add_text(key, value)
                    use_metadata = True
            image.save(output_bytes, format="PNG", pnginfo=(metadata if use_metadata else None), quality=80)

        elif ext.lower() in ("jpg", "jpeg", "webp"):
            parameters = image.info.get('parameters', None)
            exif_bytes = piexif.dump({
                "Exif": { piexif.ExifIFD.UserComment: piexif.helper.UserComment.dump(parameters or "", encoding="unicode") }
            })
            image.save(output_bytes, format="JPEG", exif = exif_bytes, quality=80)

        else:
            raise HTTPException(status_code=500, detail="Invalid image format")

        bytes_data = output_bytes.getvalue()

    return base64.b64encode(bytes_data)

def instruct_img_from_bytes(image : BinaryIO, prompt:str, num_inference_steps=10, image_guidance_scale=1):
    img = Image.open(io.BytesIO(image.read()))
    img = ImageOps.exif_transpose(img)
    (width, height) = img.size
    rwidth = 512
    rheight = int(height/(width / 512))

    img = img.resize((rwidth, rheight))
    img = img.convert("RGB")
    
    images = pipe(prompt, image=img, num_inference_steps=num_inference_steps, image_guidance_scale=image_guidance_scale).images

    return encode_pil_to_base64(images[0], "png")

